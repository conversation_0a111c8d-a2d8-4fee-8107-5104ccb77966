"""
风险分析系统主应用
模块化架构的统一入口点
"""
from flask import Flask, request, jsonify, send_from_directory, abort, send_file
from flask_cors import CORS
import os
import sys
import logging
from datetime import datetime
import pandas as pd
from io import BytesIO

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入各个模块的API
from modules.contract_risk_analysis.api.contract_api import contract_bp
from modules.agent_relationship.api.agent_api import agent_bp
from modules.link_risk_analysis.api.link_api import link_bp
# 用户分析API已整合到unified_user_api.py，通过register_unified_apis注册

# 导入鉴权模块的API
from modules.auth.api.auth_api import auth_bp
from modules.auth.api.user_api import user_bp as auth_user_bp

# 导入数据库管理器
from database.duckdb_manager import db_manager
from database.repositories.task_repository import task_repository
from database.api import db_bp

# 导入鉴权配置
from config.auth_settings import SESSION_CONFIG

# 配置简化日志 - 只显示错误和重要统计信息
logging.basicConfig(
    level=logging.WARNING,  # 设置为WARNING级别，减少详细日志
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 设置关键模块的日志级别
logging.getLogger('modules.contract_risk_analysis.services.contract_analyzer').setLevel(logging.WARNING)
logging.getLogger('modules.user_analysis.services.basic_metrics_calculator').setLevel(logging.WARNING)
logging.getLogger('werkzeug').setLevel(logging.ERROR)  # Flask HTTP日志
logging.getLogger('flask').setLevel(logging.ERROR)

logger = logging.getLogger(__name__)

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 基本配置
    app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB
    
    # 鉴权会话配置
    app.config.update(SESSION_CONFIG)
    
    # 设置Flask应用的日志级别，减少重复输出
    app.logger.setLevel(logging.WARNING)
    
    # 启用CORS (更新为支持鉴权的配置)
    CORS(app, resources={
        r"/api/*": {
            "origins": ["http://localhost:3000", "http://localhost:5005"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"],
            "supports_credentials": True  # 支持cookies
        }
    })
    
    # 注册鉴权蓝图 (必须最先注册)
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(auth_user_bp, url_prefix='/api/auth')
    
    # 注册业务模块蓝图
    app.register_blueprint(contract_bp, url_prefix='/api/contract')
    app.register_blueprint(agent_bp, url_prefix='/api/agent')
    app.register_blueprint(link_bp, url_prefix='/api/link')
    # 注册统一的用户分析API
    from modules.user_analysis.api import register_unified_apis
    register_unified_apis(app)
    app.register_blueprint(db_bp, url_prefix='/api/database')
    
    # 健康检查端点
    @app.route('/health')
    def health_check():
        """健康检查端点"""
        # 获取数据库统计信息
        try:
            db_stats = db_manager.get_table_stats()
            db_status = 'connected'
        except Exception as e:
            db_stats = {'error': str(e)}
            db_status = 'error'
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'modules': {
                'contract_risk_analysis': 'active',
                'agent_relationship': 'active',
                'link_risk_analysis': 'active',
                'user_analysis': 'active'
            },
            'database': {
                'status': db_status,
                'statistics': db_stats
            }
        })
    
    # API健康检查端点
    @app.route('/api/health')
    def api_health_check():
        """API健康检查端点"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'modules': {
                'contract_risk_analysis': 'active',
                'agent_relationship': 'active',
                'link_risk_analysis': 'active',
                'user_analysis': 'active'
            }
        })
    
    # 静态文件服务 - 指向构建后的dist目录
    @app.route('/')
    def index():
        """主页面"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'index.html')
        except:
            return send_from_directory('../frontend/dist', 'index.html')
    
    # 登录页面路由
    @app.route('/login')
    @app.route('/login.html')
    def login():
        """登录页面"""
        try:
            # 优先从frontend源码目录提供登录页面
            frontend_path = os.path.join(project_root, '..', 'frontend', 'src', 'auth')
            if os.path.exists(os.path.join(frontend_path, 'login.html')):
                return send_from_directory(frontend_path, 'login.html')
            else:
                return send_from_directory('../frontend/dist', 'login.html')
        except Exception as e:
            logger.error(f"登录页面加载失败: {str(e)}")
            return "登录页面未找到", 404
    
    # 鉴权测试页面路由
    @app.route('/test-auth.html')
    @app.route('/test-auth')
    def test_auth():
        """鉴权测试页面"""
        try:
            frontend_path = os.path.join(project_root, '..', 'frontend', 'src', 'auth')
            return send_from_directory(frontend_path, 'test-auth.html')
        except Exception as e:
            logger.error(f"测试页面加载失败: {str(e)}")
            return "测试页面未找到", 404
    
    @app.route('/agent_relationship.html')
    @app.route('/agent-relationship')
    def agent_relationship():
        """代理关系分析页面"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'agent_relationship.html')
        except:
            return send_from_directory('../frontend/dist', 'agent_relationship.html')
    
    @app.route('/contract_analysis.html')
    @app.route('/contract-analysis')
    def contract_analysis():
        """合约分析页面"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'contract_analysis.html')
        except:
            return send_from_directory('../frontend/dist', 'contract_analysis.html')
    

    
    @app.route('/contract_integration.html')
    @app.route('/contract-integration')
    def contract_integration():
        """合约整合页面"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'contract_integration.html')
        except:
            return send_from_directory('../frontend/dist', 'contract_integration.html')
    
    @app.route('/user_analysis.html')
    @app.route('/user-analysis')
    def user_analysis():
        """用户分析页面"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist')
            return send_from_directory(dist_path, 'user_analysis.html')
        except:
            return send_from_directory('../frontend/dist', 'user_analysis.html')

    # 静态资源服务 - 包括构建后的js和css文件
    @app.route('/js/<path:filename>')
    def js_files(filename):
        """JavaScript文件服务"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist', 'js')
            return send_from_directory(dist_path, filename)
        except:
            return send_from_directory('../frontend/dist/js', filename)
    
    @app.route('/css/<path:filename>')
    def css_files(filename):
        """CSS文件服务"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist', 'css')
            return send_from_directory(dist_path, filename)
        except:
            return send_from_directory('../frontend/dist/css', filename)
    
    @app.route('/assets/<path:filename>')
    def assets_files(filename):
        """资源文件服务"""
        try:
            dist_path = os.path.join(project_root, 'agent_analyzer', 'static', 'dist', 'assets')
            return send_from_directory(dist_path, filename)
        except:
            return send_from_directory('../frontend/dist/assets', filename)
    
    # 鉴权相关静态文件路由
    @app.route('/frontend/src/auth/<path:filename>')
    def auth_files(filename):
        """鉴权相关文件服务"""
        try:
            auth_path = os.path.join(project_root, '..', 'frontend', 'src', 'auth')
            return send_from_directory(auth_path, filename)
        except Exception as e:
            logger.error(f"鉴权文件加载失败: {filename}, 错误: {str(e)}")
            abort(404)
    
    @app.route('/frontend/src/shared/utils/<path:filename>')
    def auth_utils_files(filename):
        """鉴权工具文件服务"""
        try:
            utils_path = os.path.join(project_root, '..', 'frontend', 'src', 'shared', 'utils')
            return send_from_directory(utils_path, filename)
        except Exception as e:
            logger.error(f"工具文件加载失败: {filename}, 错误: {str(e)}")
            abort(404)
    
    @app.route('/static/<path:filename>')
    def static_files(filename):
        """静态文件服务 - 向后兼容"""
        static_dirs = [
            'agent_analyzer/static',  # 添加dist目录支持
            '../frontend/src/shared',
            '../frontend/css',
            '../frontend/lib'
        ]
        
        for static_dir in static_dirs:
            full_path = os.path.join(project_root, static_dir, filename)
            if os.path.exists(full_path):
                return send_from_directory(os.path.dirname(full_path), os.path.basename(full_path))
        
        abort(404)
    
    # 获取任务列表API
    @app.route('/api/tasks', methods=['GET'])
    def get_tasks():
        """获取所有任务列表，增强版本支持分类"""
        try:
            # 获取基本任务列表
            tasks = task_repository.get_all_tasks()
            
            # 按类型分类任务
            contract_tasks = []
            agent_tasks = []
            other_tasks = []
            
            for task in tasks:
                task_data = {
                    'task_id': task.get('task_id', ''),
                    'name': task.get('name', ''),
                    'type': task.get('type', ''),
                    'status': task.get('status', ''),
                    'created_at': task.get('created_at', ''),
                    'description': task.get('description', ''),
                    'file_count': task.get('file_count', 0),
                    'data_count': task.get('data_count', 0)
                }
                
                if 'contract' in task.get('type', '').lower() or 'risk' in task.get('type', '').lower():
                    contract_tasks.append(task_data)
                elif 'agent' in task.get('type', '').lower() or 'relationship' in task.get('type', '').lower():
                    agent_tasks.append(task_data)
                else:
                    other_tasks.append(task_data)
            
            # 构建增强响应
            response_data = {
                'status': 'success',
                'total_tasks': len(tasks),
                'tasks': tasks,  # 保持原有格式兼容性
                'classified_tasks': {
                    'contract_tasks': contract_tasks,
                    'agent_tasks': agent_tasks,
                    'other_tasks': other_tasks
                },
                'task_options': {
                    'contract_task_options': [
                        {'value': task['task_id'], 'label': f"{task['name']} ({task['status']})"}
                        for task in contract_tasks
                    ],
                    'agent_task_options': [
                        {'value': task['task_id'], 'label': f"{task['name']} ({task['status']})"}
                        for task in agent_tasks
                    ]
                },
                'summary': {
                    'contract_tasks_count': len(contract_tasks),
                    'agent_tasks_count': len(agent_tasks),
                    'other_tasks_count': len(other_tasks),
                    'completed_tasks': len([t for t in tasks if t.get('status') == 'completed']),
                    'running_tasks': len([t for t in tasks if t.get('status') == 'running']),
                    'failed_tasks': len([t for t in tasks if t.get('status') == 'failed'])
                }
            }
            
            return jsonify(response_data)
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {str(e)}")
            return jsonify({
                'status': 'error',
                'error': str(e),
                'tasks': [],
                'classified_tasks': {
                    'contract_tasks': [],
                    'agent_tasks': [],
                    'other_tasks': []
                },
                'task_options': {
                    'contract_task_options': [],
                    'agent_task_options': []
                }
            }), 500

    # 通用任务状态API
    @app.route('/api/task/<task_id>', methods=['GET'])
    def get_task_status(task_id):
        """获取任务状态"""
        try:
            # 从DuckDB获取任务状态
            task = task_repository.get_task(task_id)
            
            if task:
                return jsonify({
                    'task_id': task_id,
                    'status': task.get('status'),
                    'message': task.get('message', ''),
                    'progress': task.get('progress', 0),
                    'created_at': task.get('created_at'),
                    'updated_at': task.get('updated_at'),
                    'completed_at': task.get('completed_at')
                })
            
            # 如果没找到，返回任务不存在
            return jsonify({'error': '任务不存在', 'task_id': task_id}), 404
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/task/<task_id>/debug', methods=['GET'])
    def debug_task_result(task_id):
        """调试任务结果获取"""
        try:
            # 从DuckDB获取任务信息
            task = task_repository.get_task(task_id)
            
            debug_info = {
                'task_id': task_id,
                'task_exists': task is not None,
                'working_dir': os.getcwd(),
                'database_status': 'connected'
            }
            
            if task:
                debug_info.update({
                    'task_type': task.get('task_type'),
                    'status': task.get('status'),
                    'created_at': task.get('created_at'),
                    'updated_at': task.get('updated_at')
                })
                
                # 根据任务类型获取详细结果
                task_type = task.get('task_type', '')
                if task_type == 'contract_analysis':
                    # 🚀 修改：使用数据适配器智能路由到新存储
                    from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
                    adapter = ContractDataAdapter()
                    analysis_result = adapter.get_analysis_result(task_id)
                    debug_info['has_analysis_result'] = analysis_result is not None
                    if analysis_result:
                        result_data = analysis_result.get('result_data', {})
                        debug_info['result_keys'] = list(result_data.keys())
                
            return jsonify(debug_info)
            
        except Exception as e:
            import traceback
            return jsonify({'error': str(e), 'traceback': traceback.format_exc()}), 500

    @app.route('/api/task/<task_id>/result', methods=['GET'])
    def get_task_result(task_id):
        """获取任务结果"""
        try:
            # 获取分页参数
            page = int(request.args.get('page', 1))
            page_size = int(request.args.get('page_size', 100))
            
            # 参数验证
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 1000:
                page_size = 100
            
            # 从DuckDB获取任务信息
            task = task_repository.get_task(task_id)
            
            if not task:
                return jsonify({'error': '任务不存在', 'task_id': task_id}), 404
            
            # 根据任务类型获取结果
            task_type = task.get('task_type', '')
            result_summary = task.get('result_summary', {})
            
            if task_type == 'contract_analysis':
                # 🚀 修改：使用数据适配器智能路由到新存储
                from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
                adapter = ContractDataAdapter()
                analysis_result = adapter.get_analysis_result(task_id)
                
                if analysis_result:
                    result_data = analysis_result.get('result_data', {})
                    return jsonify({
                        'task_id': task_id,
                        'task_type': task_type,
                        'status': task.get('status'),
                        'message': task.get('message', ''),
                        'created_at': task.get('created_at'),
                        'updated_at': task.get('updated_at'),
                        'result': result_data,
                        'page': page,
                        'page_size': page_size,
                        'error': None
                    })
            
            # 其他任务类型返回基本信息
            return jsonify({
                'task_id': task_id,
                'task_type': task_type,
                'status': task.get('status'),
                'message': task.get('message', ''),
                'created_at': task.get('created_at'),
                'updated_at': task.get('updated_at'),
                'result': result_summary,
                'page': page,
                'page_size': page_size,
                'error': None
            })

        except Exception as e:
            # 记录详细错误日志
            app.logger.error(f"获取任务结果出错: {str(e)}", exc_info=True)
            # 返回友好的错误消息
            return jsonify({
                "error": f"获取任务结果失败: {str(e)}",
                "task_id": task_id,
                "status": "error"
            }), 500
    
    @app.route('/api/contract/export/<task_id>', methods=['GET'])
    def export_contract_data(task_id):
        """导出合约分析结果为Excel文件"""
        try:
            logger.info(f"导出合约分析结果: {task_id}")
            
            # 🚀 修改：使用数据适配器智能路由到新存储
            from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
            adapter = ContractDataAdapter()
            analysis_result = adapter.get_analysis_result(task_id)
            
            if not analysis_result:
                return jsonify({'error': '分析结果不存在'}), 404
            
            # 获取分析结果数据
            result_data = analysis_result.get('result_data', {})
            contract_risks = result_data.get('contract_risks', [])
            
            if not contract_risks:
                return jsonify({'error': '没有可导出的风险数据'}), 404
            
            # 创建DataFrame
            df = pd.DataFrame(contract_risks)
            
            # 生成Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='合约风险分析', index=False)
            
            output.seek(0)
            
            # 生成文件名
            filename = f"contract_risk_analysis_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"导出失败: {str(e)}")
            return jsonify({'error': f'导出失败: {str(e)}'}), 500


    @app.route('/api/contract/wash-trading/export/<task_id>', methods=['GET'])
    def export_wash_trading_data(task_id):
        """导出对敲交易分析结果为Excel文件"""
        try:
            logger.info(f"导出对敲交易分析结果: {task_id}")
            
            # 🚀 修改：使用数据适配器智能路由到新存储
            from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
            adapter = ContractDataAdapter()
            analysis_result = adapter.get_analysis_result(task_id)
            
            if not analysis_result:
                return jsonify({'error': '分析结果不存在'}), 404
            
            # 获取对敲交易数据
            result_data = analysis_result.get('result_data', {})
            contract_risks = result_data.get('contract_risks', [])
            
            # 筛选对敲交易数据
            wash_trading_data = [
                risk for risk in contract_risks 
                if risk.get('detection_type') == 'wash_trading'
            ]
            
            if not wash_trading_data:
                return jsonify({'error': '没有可导出的对敲交易数据'}), 404
            
            # 创建DataFrame
            df = pd.DataFrame(wash_trading_data)
            
            # 生成Excel文件
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='对敲交易分析', index=False)
            
            output.seek(0)
            
            # 生成文件名
            filename = f"wash_trading_analysis_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename
            )
            
        except Exception as e:
            logger.error(f"导出对敲交易数据失败: {str(e)}")
            return jsonify({'error': f'导出失败: {str(e)}'}), 500
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        import traceback
        error_details = traceback.format_exc()
        app.logger.error(f"Internal server error: {error_details}")
        return jsonify({
            'error': 'Internal server error',
            'details': str(error),
            'traceback': error_details
        }), 500
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    # 确保必要的目录存在
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('cache', exist_ok=True)
    os.makedirs('temp', exist_ok=True)
    
    logger.info("启动风险分析系统...")
    logger.info("系统架构: 模块化设计")
    logger.info("端口: 5005")
    
    port = int(os.environ.get('PORT', 5005))
    app.run(
        host='0.0.0.0',
        port=port,
        debug=False,  # 关闭调试模式避免重复启动
        threaded=True
    )
