"""
鉴权配置文件 - 必须包含所有必要的配置项
"""
import os

# 会话配置
SESSION_CONFIG = {
    'SECRET_KEY': os.environ.get('SECRET_KEY', 'your-super-secret-key-change-in-production'),
    'SESSION_TIMEOUT_HOURS': int(os.environ.get('SESSION_TIMEOUT_HOURS', '24')),
    'SESSION_COOKIE_NAME': 'risk_analysis_session',
    'SESSION_COOKIE_HTTPONLY': True,
    'SESSION_COOKIE_SECURE': False,  # 开发环境为False，生产环境应该为True
    'SESSION_COOKIE_SAMESITE': 'Lax',
    'SESSION_COOKIE_PATH': '/',  # 确保cookie在所有路径下都有效
    'SESSION_COOKIE_DOMAIN': None  # 让cookie在localhost的所有端口都有效
}

# 密码策略配置
PASSWORD_CONFIG = {
    'MIN_LENGTH': 6,
    'REQUIRE_UPPERCASE': False,
    'REQUIRE_LOWERCASE': False,
    'REQUIRE_NUMBERS': False,
    'REQUIRE_SPECIAL_CHARS': False,
    'MAX_LOGIN_ATTEMPTS': 5,
    'ACCOUNT_LOCK_MINUTES': 30
}

# 安全配置
SECURITY_CONFIG = {
    'ENABLE_CSRF_PROTECTION': True,
    'ENABLE_RATE_LIMITING': True,
    'MAX_REQUESTS_PER_MINUTE': 60,
    'ENABLE_IP_WHITELIST': False,
    'ALLOWED_IPS': [],
    'ENABLE_AUDIT_LOG': True
}

# 默认用户配置
DEFAULT_ADMIN = {
    'username': 'admin',
    'password': 'admin123',  # 首次部署后必须修改
    'email': '<EMAIL>'
} 