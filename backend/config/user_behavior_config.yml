# =============================================
# 用户交易行为分析配置文件
# 版本: 1.0
# =============================================

user_behavior_analysis:
  # 基础分析配置
  thresholds:
    min_total_volume: 3000        # 最小总交易量 (USDT)
    min_trades_count: 3           # 最小交易笔数
    analysis_time_window_days: 30  # 分析时间窗口 (天)
    
  # 资金规模分类（基于真实交易量）
  fund_scale_categories:
    大户: 1000000    # >= 100万 USDT
    中户: 100000     # >= 10万 USDT
    散户: 10000      # >= 1万 USDT
    小户: 0          # < 1万 USDT
    
  # 新用户判定标准
  new_user_criteria:
    max_trading_days: 3      # 最大交易天数
    max_trades_count: 5      # 最大交易笔数 (完整position_id订单数)
    
  # 专业度评分权重
  scoring_weights:
    profitability: 0.40      # 盈利能力权重
    risk_control: 0.25       # 风险控制权重
    trading_behavior: 0.20   # 交易行为权重
    market_understanding: 0.15 # 市场理解权重
    
  # 市场理解指标权重分配
  market_understanding_weights:
    timing_ability: 0.35        # 持仓时机把握能力
    risk_discipline: 0.40       # 风险管理纪律性
    execution_efficiency: 0.25  # 交易执行效率
    
  # 盈利能力子指标权重
  profitability_weights:
    win_rate: 0.35           # 胜率权重35%
    profit_loss_ratio: 0.30  # 盈亏比权重30%
    profit_factor: 0.25      # 盈利因子权重25%
    profit_consistency: 0.10 # 盈利一致性权重10%
  
  # 风险控制子指标权重
  risk_control_weights:
    avg_leverage: 0.40       # 平均杠杆权重40%
    max_leverage: 0.30       # 最大杠杆权重30%
    leverage_stability: 0.20 # 杠杆稳定性权重20%
    max_single_loss: 0.10    # 单笔最大亏损权重10%
  
  # 交易行为子指标权重
  trading_behavior_weights:
    trading_frequency: 0.30  # 交易频率权重30%
    market_order_ratio: 0.25 # 市价单比例权重25%
    profit_loss_time_ratio: 0.25 # 盈亏时长比权重25%
    position_size_consistency: 0.20 # 仓位规模一致性权重20%
    
  # 用户类型评分区间
  trader_type_ranges:
    专业交易员: [75, 100]
    半专业交易员: [50, 75]
    普通散户: [0, 50]
    
  # 币种分类
  coin_categories:
    major_coins: ["BTC", "ETH"]
    mainstream_altcoins: ["BNB", "ADA", "DOT", "LINK", "UNI", "SOL", "AVAX", "MATIC"]
    defi_tokens: ["SUSHI", "COMP", "AAVE", "MKR", "UNI", "CAKE"]
    meme_coins: ["DOGE", "SHIB", "PEPE", "FLOKI"]
    layer1_coins: ["SOL", "AVAX", "NEAR", "ATOM", "FTM"]
    
  # 币种胜率分析配置
  coin_win_rate_analysis:
    enabled: true
    min_trades_threshold: 1         # 最小交易笔数阈值 (降低到1)
    min_volume_threshold: 1000      # 最小交易量阈值 (USDT，降低到1000)
    significance_threshold: 0.6     # 显著性阈值
    
    # 专业程度分级标准
    expertise_levels:
      expert:      
        min_win_rate: 0.70
        min_trades: 20
        min_volume: 50000   # 专家级
      skilled:     
        min_win_rate: 0.60
        min_trades: 10
        min_volume: 20000   # 熟练级  
      average:     
        min_win_rate: 0.45
        min_trades: 5
        min_volume: 5000    # 一般级
      weak:        
        max_win_rate: 0.45
        min_trades: 1
        min_volume: 1000     # 弱势
      
    # 优势币种识别标准
    advantage_identification:
      high_win_rate: 0.65            # 高胜率阈值
      high_profit_factor: 2.0        # 高盈利因子阈值
      min_statistical_confidence: 0.7 # 最小统计置信度
      
    # 币种排名权重
    ranking_weights:
      win_rate: 0.4                  # 胜率权重
      profit_factor: 0.3             # 盈利因子权重
      volume: 0.2                    # 交易量权重
      trade_count: 0.1               # 交易笔数权重
    
  # 异常交易权重配置
  abnormal_weights:
    wash_trading: 0.5       # 对敲交易权重
    high_frequency: 0.4     # 高频交易权重  
    funding_arbitrage: 0.1  # 资金费率套利权重
    
  # 对冲数据统计配置（已移除）
  # hedge_display:
  #   enabled: true
  #   min_time_overlap_minutes: 30     # 最小时间重叠(分钟) - 统计阈值
  #   display_concurrent_positions: true # 显示并发持仓统计
    
  # 大户交易行为增强分析
  large_trader_analysis:
    enabled: true
    min_volume_threshold: 1000000    # 大户最小交易量阈值
    analyze_trade_frequency: true    # 分析单次交易频率
    complex_strategy_detection: true # 复杂策略检测
    
  # 币种分类动态配置  
  dynamic_classification:
    enabled: true
    fallback_category: "other_altcoins"  # 未分类币种归类
    auto_update_enabled: false           # 暂不启用自动更新
    
  # 验证配置
  validation:
    enabled: true
    sample_sizes:
      professional_traders: 50
      semi_professional: 100
      retail_traders: 200
      new_users: 50
    accuracy_thresholds:
      professional_precision: 0.85
      retail_recall: 0.90
      new_user_accuracy: 0.95
      overall_accuracy: 0.80
    stability_thresholds:
      monthly_score_volatility: 0.15
      abnormal_score_ratio: 0.05
      business_correlation: 0.60

# 缓存配置
cache:
  enabled: true
  default_ttl: 3600  # 1小时缓存
  redis_url: "redis://localhost:6379"
  
# 日志配置
logging:
  level: "WARNING"  # 改为WARNING级别，减少详细日志输出
  file_path: "logs/user_behavior_analysis.log"
  max_file_size: "50MB"
  backup_count: 5
  
# 性能配置
performance:
  batch_size: 100              # 批处理大小
  max_workers: 4               # 最大工作线程数
  query_timeout: 30            # 查询超时时间(秒)
  analysis_timeout: 300        # 分析超时时间(秒)
  memory_limit_mb: 1024        # 内存限制(MB)