"""
指标计算器
"""
import pandas as pd
import numpy as np
from typing import Dict, Any, List

class IndicatorCalculator:
    """指标计算器"""
    
    def __init__(self):
        pass
        
    def calculate_trading_frequency(self, df: pd.DataFrame, time_window_minutes: int = 60) -> float:
        """计算交易频率"""
        if df.empty:
            return 0.0
            
        # 简化计算：交易次数除以时间窗口
        return len(df) / (time_window_minutes / 60)
        
    def calculate_volume_variance(self, df: pd.DataFrame) -> float:
        """计算交易量方差"""
        if df.empty or 'volume' not in df.columns:
            return 0.0
            
        volumes = df['volume'].astype(float)
        return float(volumes.var())
        
    def calculate_price_correlation(self, prices1: List[float], prices2: List[float]) -> float:
        """计算价格相关性"""
        if len(prices1) != len(prices2) or len(prices1) == 0:
            return 0.0
            
        return float(np.corrcoef(prices1, prices2)[0, 1]) if len(prices1) > 1 else 0.0 