"""
统一的用户分析数据访问层
合并user_api.py和user_behavior_api.py中重复的数据库查询逻辑
"""

import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import json

from database.duckdb_manager import DuckDBManager
from database.repositories.user_repository import user_repository
from database.repositories.task_repository import task_repository
# 已废弃: from database.repositories.contract_risk_repository import contract_risk_repository
# 🔄 迁移到新存储: 使用 ContractDataAdapter 替代

logger = logging.getLogger(__name__)

class UnifiedUserDataRepository:
    """统一的用户数据访问仓库"""
    
    def __init__(self):
        self.db_manager = DuckDBManager()
    
    def get_unified_task_lists(self) -> Dict:
        """统一获取任务列表"""
        try:
            # 获取已完成的合约和代理关系分析任务
            contract_tasks = self._get_completed_tasks('contract_analysis')
            agent_tasks = self._get_completed_tasks('agent_analysis')
            
            return {
                'contract_tasks': contract_tasks,
                'agent_tasks': agent_tasks,
                'status': 'success'
            }
        except Exception as e:
            logger.error(f"获取统一任务列表失败: {str(e)}")
            raise
    
    def _get_completed_tasks(self, task_type: str) -> List[Dict]:
        """获取已完成的任务列表"""
        try:
            sql = """
            SELECT task_id, filename, created_at, updated_at, 
                   message, status
            FROM tasks 
            WHERE task_type = ? AND status = 'completed'
            ORDER BY created_at DESC
            """
            
            results = self.db_manager.execute_query(sql, [task_type])
            
            tasks = []
            for row in results:
                task = {
                    'id': row[0],  # 前端期望的字段名
                    'name': row[1] if row[1] else f"{task_type}_{row[0][:8]}",  # 前端期望的字段名
                    'task_id': row[0],  # 保留原字段名以兼容
                    'task_name': row[1] if row[1] else f"{task_type}_{row[0][:8]}",  # 保留原字段名以兼容
                    'created_at': row[2],
                    'updated_at': row[3],
                    'description': row[4] if row[4] else '',  # 使用message作为描述
                    'file_path': row[1] if row[1] else '',    # filename作为文件路径
                    'status': row[5]
                }
                tasks.append(task)
            
            logger.info(f"获取到 {len(tasks)} 个 {task_type} 任务")
            return tasks
            
        except Exception as e:
            logger.error(f"获取已完成任务失败 - task_type: {task_type}, 错误: {str(e)}")
            return []
    
    def get_latest_task_id(self, task_type: str) -> Optional[str]:
        """获取指定类型的最新任务ID"""
        try:
            sql = """
            SELECT task_id FROM tasks 
            WHERE task_type = ? AND status = 'completed'
            ORDER BY created_at DESC 
            LIMIT 1
            """
            
            results = self.db_manager.execute_query(sql, [task_type])
            
            if results:
                return results[0][0]
            return None
            
        except Exception as e:
            logger.error(f"获取最新任务ID失败 - task_type: {task_type}, 错误: {str(e)}")
            return None
    
    def get_unified_user_basic_info(self, user_id: str) -> Dict:
        """统一获取用户基础信息"""
        try:
            # 基础用户信息结构
            user_info = {
                'member_id': user_id,
                'digital_id': '',
                'user_type': '普通用户',
                'bd_name': '',
                'last_activity': '',
                'account_status': 'active',
                'registration_date': '',
                'kyc_status': 'unknown'
            }
            
            # 尝试从数据库获取更多信息
            try:
                # 查询用户基本信息（从users表获取digital_id，从position_analysis表获取最新活动时间）
                sql = """
                SELECT u.member_id, u.digital_id, u.created_at, 
                       COALESCE(MAX(p.created_at), u.created_at) as last_activity
                FROM users u 
                LEFT JOIN position_analysis p ON u.member_id = p.member_id
                WHERE u.member_id = ?
                GROUP BY u.member_id, u.digital_id, u.created_at
                LIMIT 1
                """
                
                results = self.db_manager.execute_query(sql, [user_id])
                if results:
                    row = results[0]
                    user_info['digital_id'] = row[1] if row[1] else ''
                    user_info['last_activity'] = row[3] if row[3] else ''
                
            except Exception as e:
                logger.warning(f"获取用户基础信息时出错: {str(e)}")
            
            return user_info
            
        except Exception as e:
            logger.error(f"获取用户基础信息失败 - user_id: {user_id}, 错误: {str(e)}")
            raise
    
    def get_comprehensive_user_associations(self, user_id: str, digital_id: str = '') -> Dict:
        """获取用户关联信息（合并两个API的关联查询逻辑）"""
        try:
            associations = {
                'same_ip_users': [],
                'same_device_users': [],
                'related_accounts': [],
                'association_summary': {
                    'total_associated_users': 0,
                    'high_risk_associations': 0,
                    'association_types': []
                }
            }
            
            # 查询同IP用户
            same_ip_users = self._get_same_ip_users(user_id)
            associations['same_ip_users'] = same_ip_users
            
            # 查询同设备用户
            same_device_users = self._get_same_device_users(user_id)
            associations['same_device_users'] = same_device_users
            
            # 查询相关账户
            if digital_id:
                related_accounts = self._get_related_accounts_by_digital_id(digital_id)
                associations['related_accounts'] = related_accounts
            
            # 计算关联摘要
            all_associated = set()
            all_associated.update([u['member_id'] for u in same_ip_users])
            all_associated.update([u['member_id'] for u in same_device_users])
            all_associated.update([u['member_id'] for u in associations['related_accounts']])
            
            # 移除自己
            all_associated.discard(user_id)
            
            associations['association_summary'] = {
                'total_associated_users': len(all_associated),
                'high_risk_associations': len([u for u in same_ip_users if u.get('risk_level', 'low') == 'high']),
                'association_types': [
                    'same_ip' if same_ip_users else None,
                    'same_device' if same_device_users else None,
                    'related_accounts' if associations['related_accounts'] else None
                ]
            }
            associations['association_summary']['association_types'] = [
                t for t in associations['association_summary']['association_types'] if t
            ]
            
            return associations
            
        except Exception as e:
            logger.error(f"获取用户关联信息失败 - user_id: {user_id}, 错误: {str(e)}")
            return {
                'same_ip_users': [],
                'same_device_users': [],
                'related_accounts': [],
                'association_summary': {
                    'total_associated_users': 0,
                    'high_risk_associations': 0,
                    'association_types': []
                }
            }
    
    def _get_same_ip_users(self, user_id: str) -> List[Dict]:
        """获取同IP用户"""
        try:
            # 这里应该根据实际的数据库结构来查询
            # 暂时返回空列表，实际实现需要根据具体的表结构
            return []
        except Exception as e:
            logger.warning(f"获取同IP用户失败: {str(e)}")
            return []
    
    def _get_same_device_users(self, user_id: str) -> List[Dict]:
        """获取同设备用户"""
        try:
            # 这里应该根据实际的数据库结构来查询
            # 暂时返回空列表，实际实现需要根据具体的表结构
            return []
        except Exception as e:
            logger.warning(f"获取同设备用户失败: {str(e)}")
            return []
    
    def _get_related_accounts_by_digital_id(self, digital_id: str) -> List[Dict]:
        """根据digital_id获取相关账户"""
        try:
            # 这里应该根据实际的数据库结构来查询
            # 暂时返回空列表，实际实现需要根据具体的表结构
            return []
        except Exception as e:
            logger.warning(f"获取相关账户失败: {str(e)}")
            return []
    
    def get_unified_transaction_details(self, user_id: str, task_id: str = '') -> Dict:
        """统一获取用户交易详情（合并两个API的交易查询逻辑）"""
        try:
            # 🔧 修复：使用正确的表结构查询contract_risk_details
            # 该表没有task_id字段，需要通过algorithm_results表进行关联
            
            # 如果没有指定task_id，获取最新的
            if not task_id:
                task_id = self.get_latest_task_id('contract_analysis')
                if not task_id:
                    logger.warning(f"未找到可用的合约分析任务")
                    return self._get_default_transaction_details()
            
            # 🔧 修复：正确的查询语句 - 通过algorithm_results表关联
            sql = """
            SELECT crd.member_id, crd.contract_name, crd.detection_type, 
                   crd.abnormal_volume, crd.risk_score, crd.trade_count,
                   crd.time_range, crd.counterparty_ids, crd.additional_data,
                   crd.created_at, ar.task_id
            FROM contract_risk_details crd
            JOIN algorithm_results ar ON crd.algorithm_result_id = ar.id
            WHERE crd.member_id = ? AND ar.task_id = ?
            ORDER BY crd.created_at DESC
            """
            
            results = self.db_manager.execute_query(sql, [user_id, task_id])
            
            if not results:
                logger.info(f"用户 {user_id} 在任务 {task_id} 中没有交易记录")
                raise ValueError(f"用户 {user_id} 数据不足：未找到交易记录，无法进行分析")
            
            # 🔧 修复：处理交易数据 - 适配新的表结构
            transactions = []
            total_volume = 0
            risk_transactions = 0
            
            for row in results:
                # 解析additional_data中的详细信息（如果有的话）
                additional_data = {}
                try:
                    if row[8]:  # additional_data字段
                        additional_data = json.loads(row[8]) if isinstance(row[8], str) else row[8]
                except:
                    additional_data = {}
                
                # 解析counterparty_ids
                counterparty_list = []
                try:
                    if row[7]:  # counterparty_ids字段
                        counterparty_list = json.loads(row[7]) if isinstance(row[7], str) else row[7]
                        if not isinstance(counterparty_list, list):
                            counterparty_list = [counterparty_list]
                except:
                    counterparty_list = []
                
                transaction = {
                    'member_id': row[0],
                    'symbol': row[1] or 'UNKNOWN',  # contract_name
                    'side': additional_data.get('side', 'UNKNOWN'),
                    'quantity': additional_data.get('quantity', 0),
                    'price': additional_data.get('price', 0),
                    'timestamp': row[9],  # created_at
                    'counterparty': counterparty_list[0] if counterparty_list else '',
                    'risk_score': float(row[4]) if row[4] else 0,  # risk_score
                    'additional_data': row[8] or '{}',
                    'trade_type': row[2] or 'unknown',  # detection_type
                    'contract_name': row[1] or 'UNKNOWN',
                    'detection_type': row[2] or 'unknown',
                    'abnormal_volume': float(row[3]) if row[3] else 0,
                    'trade_count': int(row[5]) if row[5] else 0,
                    'time_range': row[6] or '',
                    'counterparty_ids': counterparty_list
                }
                
                # 计算交易金额 - 使用abnormal_volume作为主要指标
                volume = float(row[3]) if row[3] else 0  # abnormal_volume
                if volume == 0:
                    # 尝试从additional_data中获取
                    volume = additional_data.get('volume', 0)
                    if volume == 0 and transaction['quantity'] and transaction['price']:
                        volume = transaction['quantity'] * transaction['price']
                
                transaction['volume'] = volume
                total_volume += volume
                
                if transaction['risk_score'] > 0.5:
                    risk_transactions += 1
                
                transactions.append(transaction)
            
            return {
                'transactions': transactions,
                'summary': {
                    'total_transactions': len(transactions),
                    'total_volume': total_volume,
                    'risk_transactions': risk_transactions,
                    'risk_ratio': risk_transactions / len(transactions) if transactions else 0
                },
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"获取用户交易详情失败 - user_id: {user_id}, task_id: {task_id}, 错误: {str(e)}")
            return self._get_default_transaction_details()
    
    def _get_default_transaction_details(self) -> Dict:
        """获取默认的交易详情结构"""
        return {
            'transactions': [],
            'summary': {
                'total_transactions': 0,
                'total_volume': 0,
                'risk_transactions': 0,
                'risk_ratio': 0
            },
            'status': 'no_data'
        }
    
    def find_member_id_by_digital_id(self, digital_id: str, agent_task_id: str = None) -> Optional[str]:
        """通过digital_id查找member_id"""
        try:
            # 首先在代理关系数据中查找
            if agent_task_id:
                sql = """
                SELECT DISTINCT member_id FROM agent_relationships 
                WHERE digital_id = ? AND task_id = ?
                LIMIT 1
                """
                results = self.db_manager.execute_query(sql, [digital_id, agent_task_id])
                if results:
                    return results[0][0]
            
            # 在用户数据中查找
            sql = """
            SELECT DISTINCT member_id FROM users 
            WHERE digital_id = ?
            LIMIT 1
            """
            results = self.db_manager.execute_query(sql, [digital_id])
            if results:
                return results[0][0]
            
            return None
            
        except Exception as e:
            logger.error(f"通过digital_id查找member_id失败 - digital_id: {digital_id}, 错误: {str(e)}")
            return None
    
    def get_user_positions(self, user_id: str, task_id: str = '', days: int = 30) -> List[Dict]:
        """获取用户持仓数据 - 从position_analysis表获取实际数据"""
        try:
            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 从position_analysis表查询数据（使用实际的字段名）
            sql = """
            SELECT member_id, contract_name as symbol, 
                   CASE WHEN primary_side = 1 THEN 'LONG' ELSE 'SHORT' END as position_type,
                   total_open_amount as position_size, 
                   avg_open_price as entry_price, 
                   avg_close_price as current_price, 
                   COALESCE(net_pnl, 0) as pnl,
                   COALESCE(leverage, 1) as leverage,
                   0 as margin_ratio,
                   open_time, 
                   close_time,
                   '{}' as additional_data, 
                   '' as digital_id
            FROM position_analysis 
            WHERE member_id = ? 
            AND open_time >= ? 
            AND open_time <= ?
            ORDER BY open_time DESC
            LIMIT 1000
            """
            
            params = [user_id, start_date.isoformat(), end_date.isoformat()]
            results = self.db_manager.execute_query(sql, params)
            
            if not results:
                logger.info(f"在position_analysis表中未找到用户 {user_id} 的持仓数据（时间范围：{start_date.date()} 到 {end_date.date()}）")
                # 尝试查询所有时间范围的数据
                sql_all = """
                SELECT COUNT(*) as total_count FROM position_analysis WHERE member_id = ?
                """
                total_results = self.db_manager.execute_query(sql_all, [user_id])
                total_count = total_results[0][0] if total_results else 0
                logger.info(f"用户 {user_id} 在position_analysis表中总共有 {total_count} 条记录")
                
                if total_count > 0:
                    # 如果有数据但不在时间范围内，扩大时间范围
                    start_date_extended = end_date - timedelta(days=365)  # 扩大到1年
                    sql_extended = sql.replace("AND open_time >= ?", "AND open_time >= ?")
                    params_extended = [user_id, start_date_extended.isoformat(), end_date.isoformat()]
                    results = self.db_manager.execute_query(sql_extended, params_extended)
                    logger.info(f"扩大时间范围后找到 {len(results) if results else 0} 条记录")
                
                if not results:
                    return []
            
            positions = []
            for row in results:
                position = {
                    'member_id': row[0],
                    'symbol': row[1] or 'UNKNOWN',
                    'position_type': row[2] or 'UNKNOWN',
                    'position_size': float(row[3]) if row[3] else 0,
                    'entry_price': float(row[4]) if row[4] else 0,
                    'current_price': float(row[5]) if row[5] else 0,
                    'pnl': float(row[6]) if row[6] else 0,
                    'leverage': float(row[7]) if row[7] else 1,
                    'margin_ratio': float(row[8]) if row[8] else 0,
                    'open_time': row[9],
                    'close_time': row[10],
                    'additional_data': row[11] or '{}',
                    'digital_id': row[12] or ''
                }
                positions.append(position)
            
            logger.info(f"成功获取用户 {user_id} 的 {len(positions)} 条持仓数据")
            return positions
            
        except Exception as e:
            logger.error(f"获取用户持仓数据失败 - user_id: {user_id}, 错误: {str(e)}")
            return []
    
    def get_user_trading_profile(self, user_id: str) -> Optional[Dict]:
        """
        从user_trading_profiles表获取用户的完整交易画像数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 用户交易画像数据，如果不存在返回None
        """
        try:
            sql = """
            SELECT * FROM user_trading_profiles
            WHERE member_id = ?
            ORDER BY analysis_date DESC, created_at DESC
            LIMIT 1
            """
            
            results = self.db_manager.execute_query(sql, [user_id])
            
            if results:
                # 获取列名
                columns_sql = "DESCRIBE user_trading_profiles"
                columns_info = self.db_manager.execute_query(columns_sql)
                column_names = [col[0] for col in columns_info]
                
                # 将结果转换为字典
                profile_data = dict(zip(column_names, results[0]))
                
                logger.info(f"成功从user_trading_profiles表获取用户 {user_id} 的数据")
                return profile_data
            else:
                logger.warning(f"user_trading_profiles表中没有找到用户 {user_id} 的数据")
                return None
                
        except Exception as e:
            logger.error(f"从user_trading_profiles表获取用户 {user_id} 数据失败: {str(e)}")
            return None
    
    def check_user_trading_profile_exists(self, user_id: str) -> bool:
        """
        检查用户是否在user_trading_profiles表中存在
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否存在
        """
        try:
            sql = """
            SELECT COUNT(*) as count FROM user_trading_profiles
            WHERE member_id = ?
            """
            
            results = self.db_manager.execute_query(sql, [user_id])
            count = results[0][0] if results else 0
            
            return count > 0
            
        except Exception as e:
            logger.error(f"检查用户 {user_id} 是否存在user_trading_profiles表失败: {str(e)}")
            return False 