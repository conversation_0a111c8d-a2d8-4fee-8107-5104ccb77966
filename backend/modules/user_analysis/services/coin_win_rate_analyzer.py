"""
币种胜率分析器
负责分析用户在各个币种上的交易表现，识别优势币种和专业度等级
"""

import logging
from typing import List, Dict, Tuple
from collections import defaultdict
import numpy as np

from modules.user_analysis.models.user_behavior_models import (
    CoinAnalysisResult, CoinWinRateAnalysis, PositionData, ExpertiseLevel
)

logger = logging.getLogger(__name__)


class CoinWinRateAnalyzer:
    """币种胜率分析器"""
    
    def __init__(self, config: Dict):
        """
        初始化币种胜率分析器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.coin_config = config.get('coin_win_rate_analysis', {})
        # 🚀 临时降低阈值，让更多币种通过分析
        self.min_trades = self.coin_config.get('min_trades_threshold', 2)  # 降低到2笔
        self.min_volume = self.coin_config.get('min_volume_threshold', 1000)  # 降低到1000
        self.expertise_levels = self.coin_config.get('expertise_levels', {})
        self.ranking_weights = self.coin_config.get('ranking_weights', {})
        self.advantage_config = self.coin_config.get('advantage_identification', {})
        
    def analyze_coin_performance(self, positions: List[PositionData]) -> CoinWinRateAnalysis:
        """
        分析用户在各币种的交易表现
        
        Args:
            positions: 持仓数据列表
            
        Returns:
            CoinWinRateAnalysis: 币种胜率分析结果
        """
        logger.debug(f"开始分析币种胜率，持仓数量: {len(positions)}")
        
        if not positions:
            logger.warning("持仓数据为空，返回空的币种分析结果")
            return CoinWinRateAnalysis()
        
        try:
            # 1. 按币种分组统计
            logger.debug(f"币种分析步骤1/5: 开始按币种分组统计...")
            coin_stats = self._group_positions_by_coin(positions)
            logger.debug(f"币种分析步骤1/5: 按币种分组完成，发现 {len(coin_stats)} 个币种")
            
            # 2. 计算各币种详细指标
            logger.debug(f"币种分析步骤2/5: 开始计算各币种详细指标...")
            coin_analysis = {}
            processed_coins = 0
            skipped_coins = 0
            
            for contract, coin_positions in coin_stats.items():
                if self._meets_analysis_threshold(coin_positions):
                    coin_analysis[contract] = self._calculate_coin_metrics(contract, coin_positions)
                    processed_coins += 1
                else:
                    skipped_coins += 1
                    logger.debug(f"跳过币种 {contract}，不满足分析阈值 (交易数: {len(coin_positions)})")
            
            logger.debug(f"币种分析步骤2/5: 币种指标计算完成 - 有效币种: {processed_coins}, 跳过: {skipped_coins}")
            
            # 3. 识别优势币种
            logger.debug(f"币种分析步骤3/5: 开始识别优势币种...")
            advantage_coins = self._identify_advantage_coins(coin_analysis)
            logger.debug(f"币种分析步骤3/5: 优势币种识别完成，发现 {len(advantage_coins)} 个优势币种")
            
            # 4. 币种专业度分级
            logger.debug(f"币种分析步骤4/5: 开始币种专业度分级...")
            expertise_summary = self._classify_coin_expertise(coin_analysis)
            logger.debug(f"币种分析步骤4/5: 专业度分级完成")
            
            # 5. 性能排名
            logger.debug(f"币种分析步骤5/5: 开始性能排名...")
            ranked_coins = self._rank_coin_performance(coin_analysis)
            logger.debug(f"币种分析步骤5/5: 性能排名完成")
            
            # 6. 计算汇总指标
            total_analyzed_coins = len(coin_analysis)
            avg_coin_win_rate = self._calculate_average_win_rate(coin_analysis)
            
            result = CoinWinRateAnalysis(
                coin_analysis=coin_analysis,
                advantage_coins=advantage_coins,
                coin_expertise_summary=expertise_summary,
                coin_performance_ranking=ranked_coins,
                total_analyzed_coins=total_analyzed_coins,
                avg_coin_win_rate=avg_coin_win_rate
            )
            
            logger.info(f"币种胜率分析完成: 分析币种数={total_analyzed_coins}, "
                       f"平均胜率={avg_coin_win_rate:.3f}, "
                       f"优势币种数={len(advantage_coins)}")
            
            return result
            
        except Exception as e:
            logger.error(f"分析币种胜率时发生错误: {str(e)}")
            raise
    
    def _group_positions_by_coin(self, positions: List[PositionData]) -> Dict[str, List[PositionData]]:
        """按币种分组持仓数据"""
        coin_groups = defaultdict(list)
        
        for position in positions:
            # 使用合约名称作为分组键
            contract = position.contract_name
            coin_groups[contract].append(position)
        
        logger.debug(f"按币种分组完成，共{len(coin_groups)}个币种")
        return dict(coin_groups)
    
    def _meets_analysis_threshold(self, positions: List[PositionData]) -> bool:
        """检查是否满足分析阈值"""
        if len(positions) < self.min_trades:
            return False
        
        total_volume = sum(pos.total_open_amount for pos in positions)
        if total_volume < self.min_volume:
            return False
        
        return True
    
    def _calculate_coin_metrics(self, contract: str, positions: List[PositionData]) -> CoinAnalysisResult:
        """计算单个币种的详细指标"""
        total_trades = len(positions)
        profitable_trades = len([p for p in positions if p.is_profitable])
        losing_trades = total_trades - profitable_trades
        
        total_volume = sum(p.total_open_amount for p in positions)
        total_profit = sum(max(0, p.total_pnl) for p in positions)
        total_loss = sum(min(0, p.total_pnl) for p in positions)
        net_pnl = total_profit + total_loss
        
        win_rate = profitable_trades / total_trades if total_trades > 0 else 0
        avg_trade_size = total_volume / total_trades if total_trades > 0 else 0
        
        # 计算盈利因子
        profit_factor = abs(total_profit / total_loss) if total_loss != 0 else float('inf')
        
        # 计算平均盈亏
        avg_profit_per_trade = total_profit / profitable_trades if profitable_trades > 0 else 0
        avg_loss_per_trade = total_loss / losing_trades if losing_trades > 0 else 0
        
        # 找出最大单笔盈亏
        max_single_profit = max([p.total_pnl for p in positions]) if positions else 0
        max_single_loss = min([p.total_pnl for p in positions]) if positions else 0
        
        # 计算专业度等级
        expertise_level = self._determine_expertise_level(win_rate, total_trades, total_volume)
        
        # 计算性能评分
        performance_score = self._calculate_performance_score(win_rate, profit_factor, total_volume, total_trades)
        
        return CoinAnalysisResult(
            contract=contract,
            total_trades=total_trades,
            profitable_trades=profitable_trades,
            win_rate=round(win_rate, 4),
            total_volume=round(total_volume, 2),
            total_profit=round(total_profit, 2),
            total_loss=round(total_loss, 2),
            net_pnl=round(net_pnl, 2),
            avg_trade_size=round(avg_trade_size, 2),
            profit_factor=round(profit_factor, 2),
            avg_profit_per_trade=round(avg_profit_per_trade, 2),
            avg_loss_per_trade=round(avg_loss_per_trade, 2),
            max_single_profit=round(max_single_profit, 2),
            max_single_loss=round(max_single_loss, 2),
            performance_rank=0,  # 稍后在排名中填充
            expertise_level=expertise_level
        )
    
    def _determine_expertise_level(self, win_rate: float, trades: int, volume: float) -> str:
        """判定币种专业度等级"""
        levels = self.expertise_levels
        
        # 检查专家级
        expert = levels.get('expert', {})
        if (win_rate >= expert.get('min_win_rate', 0.7) and 
            trades >= expert.get('min_trades', 20) and 
            volume >= expert.get('min_volume', 50000)):
            return ExpertiseLevel.EXPERT.value
        
        # 检查熟练级
        skilled = levels.get('skilled', {})
        if (win_rate >= skilled.get('min_win_rate', 0.6) and 
            trades >= skilled.get('min_trades', 15) and 
            volume >= skilled.get('min_volume', 20000)):
            return ExpertiseLevel.SKILLED.value
        
        # 检查一般级
        average = levels.get('average', {})
        if (win_rate >= average.get('min_win_rate', 0.45) and 
            trades >= average.get('min_trades', 10) and 
            volume >= average.get('min_volume', 5000)):
            return ExpertiseLevel.AVERAGE.value
        
        # 弱势级
        return ExpertiseLevel.WEAK.value
    
    def _calculate_performance_score(self, win_rate: float, profit_factor: float, 
                                   volume: float, trades: int) -> float:
        """计算币种综合性能评分 (0-100)"""
        weights = self.ranking_weights
        
        # 胜率评分 (0-100)
        win_rate_score = min(100, win_rate * 100)
        
        # 盈利因子评分 (0-100)
        if profit_factor >= 999 or profit_factor == float('inf'):
            profit_factor_score = 100  # 🚀 修复：处理极大值和无穷大
        else:
            profit_factor_score = min(100, (profit_factor - 1) * 25)
        
        # 交易量评分 (0-100) - 对数缩放
        volume_score = min(100, np.log10(max(1, volume)) * 20)
        
        # 交易笔数评分 (0-100)
        trades_score = min(100, trades * 2)
        
        # 加权综合评分
        total_score = (
            win_rate_score * weights.get('win_rate', 0.4) +
            profit_factor_score * weights.get('profit_factor', 0.3) +
            volume_score * weights.get('volume', 0.2) +
            trades_score * weights.get('trade_count', 0.1)
        )
        
        return total_score
    
    def _identify_advantage_coins(self, coin_analysis: Dict[str, CoinAnalysisResult]) -> List[Dict]:
        """识别优势币种"""
        advantage_coins = []
        
        high_win_rate_threshold = self.advantage_config.get('high_win_rate', 0.65)
        high_profit_factor_threshold = self.advantage_config.get('high_profit_factor', 2.0)
        min_confidence = self.advantage_config.get('min_statistical_confidence', 0.7)
        
        for contract, metrics in coin_analysis.items():
            win_rate = metrics.win_rate
            profit_factor = metrics.profit_factor
            
            advantage_type = None
            significance_score = 0
            
            # 高胜率型
            if win_rate >= high_win_rate_threshold:
                advantage_type = '高胜率型'
                significance_score = win_rate
            
            # 高盈利型
            elif profit_factor >= high_profit_factor_threshold:
                advantage_type = '高盈利型' 
                significance_score = min(1.0, profit_factor / 5.0)  # 归一化到0-1
            
            # 综合优势型
            elif (win_rate >= 0.55 and profit_factor >= 1.5 and metrics.total_trades >= 15):
                advantage_type = '综合优势型'
                significance_score = (win_rate + min(1.0, profit_factor / 3.0)) / 2
            
            # 检查统计显著性
            if advantage_type and significance_score >= min_confidence:
                advantage_coins.append({
                    'contract': contract,
                    'win_rate': win_rate,
                    'profit_factor': profit_factor,
                    'total_trades': metrics.total_trades,
                    'net_pnl': metrics.net_pnl,
                    'min_trades_met': metrics.total_trades >= self.min_trades,
                    'significance_score': round(significance_score, 3),
                    'advantage_type': advantage_type,
                    'expertise_level': metrics.expertise_level
                })
        
        # 按显著性评分排序
        advantage_coins.sort(key=lambda x: x['significance_score'], reverse=True)
        
        logger.debug(f"识别出{len(advantage_coins)}个优势币种")
        return advantage_coins
    
    def _classify_coin_expertise(self, coin_analysis: Dict[str, CoinAnalysisResult]) -> Dict:
        """分类币种专业度"""
        expertise_summary = {
            'expert_coins': [],      # 专家级币种
            'skilled_coins': [],     # 熟练级币种
            'average_coins': [],     # 一般级币种
            'weak_coins': []         # 弱势币种
        }
        
        for contract, metrics in coin_analysis.items():
            expertise_level = metrics.expertise_level
            
            coin_info = {
                'contract': contract,
                'win_rate': metrics.win_rate,
                'total_trades': metrics.total_trades,
                'net_pnl': metrics.net_pnl,
                'expertise_level': expertise_level
            }
            
            if expertise_level == ExpertiseLevel.EXPERT.value:
                expertise_summary['expert_coins'].append(coin_info)
            elif expertise_level == ExpertiseLevel.SKILLED.value:
                expertise_summary['skilled_coins'].append(coin_info)
            elif expertise_level == ExpertiseLevel.AVERAGE.value:
                expertise_summary['average_coins'].append(coin_info)
            else:
                expertise_summary['weak_coins'].append(coin_info)
        
        # 按净盈亏排序
        for coin_list in expertise_summary.values():
            coin_list.sort(key=lambda x: x['net_pnl'], reverse=True)
        
        return expertise_summary
    
    def _rank_coin_performance(self, coin_analysis: Dict[str, CoinAnalysisResult]) -> List[Dict]:
        """对币种表现进行排名"""
        ranked_coins = []
        
        for contract, metrics in coin_analysis.items():
            performance_score = self._calculate_performance_score(
                metrics.win_rate, metrics.profit_factor, 
                metrics.total_volume, metrics.total_trades
            )
            
            ranked_coins.append({
                'rank': 0,  # 稍后填充
                'contract': contract,
                'performance_score': round(performance_score, 2),
                'win_rate': metrics.win_rate,
                'profit_factor': metrics.profit_factor,
                'net_pnl': metrics.net_pnl,
                'total_trades': metrics.total_trades,
                'expertise_level': metrics.expertise_level
            })
        
        # 按性能评分排序
        ranked_coins.sort(key=lambda x: x['performance_score'], reverse=True)
        
        # 添加排名
        for i, coin in enumerate(ranked_coins, 1):
            coin['rank'] = i
            
            # 更新原始数据中的排名
            if coin['contract'] in coin_analysis:
                coin_analysis[coin['contract']].performance_rank = i
        
        logger.debug(f"完成币种性能排名，共{len(ranked_coins)}个币种")
        return ranked_coins
    
    def _calculate_average_win_rate(self, coin_analysis: Dict[str, CoinAnalysisResult]) -> float:
        """计算平均币种胜率"""
        if not coin_analysis:
            return 0.0
        
        total_trades = sum(metrics.total_trades for metrics in coin_analysis.values())
        total_profitable = sum(
            metrics.profitable_trades for metrics in coin_analysis.values()
        )
        
        return round(total_profitable / total_trades, 4) if total_trades > 0 else 0.0
    
    def get_coin_expertise_insights(self, coin_analysis_result: CoinWinRateAnalysis) -> Dict:
        """生成币种专业度洞察"""
        coin_analysis = coin_analysis_result.coin_analysis
        advantage_coins = coin_analysis_result.advantage_coins
        expertise_summary = coin_analysis_result.coin_expertise_summary
        
        insights = {
            'top_performer': None,      # 表现最佳币种
            'most_profitable': None,    # 最盈利币种
            'highest_win_rate': None,   # 最高胜率币种
            'trading_specialty': '',    # 交易专长总结
            'improvement_suggestions': []  # 改进建议
        }
        
        if coin_analysis:
            # 找出表现最佳币种
            best_coin_contract = max(coin_analysis.keys(), 
                                   key=lambda x: self._calculate_performance_score(
                                       coin_analysis[x].win_rate, 
                                       coin_analysis[x].profit_factor,
                                       coin_analysis[x].total_volume, 
                                       coin_analysis[x].total_trades))
            best_coin = coin_analysis[best_coin_contract]
            
            insights['top_performer'] = {
                'contract': best_coin_contract,
                'performance_score': self._calculate_performance_score(
                    best_coin.win_rate, best_coin.profit_factor,
                    best_coin.total_volume, best_coin.total_trades),
                'win_rate': best_coin.win_rate,
                'expertise_level': best_coin.expertise_level
            }
            
            # 找出最盈利币种
            most_profitable_contract = max(coin_analysis.keys(), 
                                         key=lambda x: coin_analysis[x].net_pnl)
            most_profitable = coin_analysis[most_profitable_contract]
            
            insights['most_profitable'] = {
                'contract': most_profitable_contract,
                'net_pnl': most_profitable.net_pnl,
                'total_trades': most_profitable.total_trades
            }
            
            # 找出最高胜率币种
            highest_win_rate_contract = max(coin_analysis.keys(), 
                                          key=lambda x: coin_analysis[x].win_rate)
            highest_win_rate = coin_analysis[highest_win_rate_contract]
            
            insights['highest_win_rate'] = {
                'contract': highest_win_rate_contract,
                'win_rate': highest_win_rate.win_rate,
                'total_trades': highest_win_rate.total_trades
            }
            
            # 生成交易专长总结
            insights['trading_specialty'] = self._generate_specialty_summary(expertise_summary, advantage_coins)
            
            # 生成改进建议
            insights['improvement_suggestions'] = self._generate_improvement_suggestions(coin_analysis, expertise_summary)
        
        return insights
    
    def _generate_specialty_summary(self, expertise_summary: Dict, advantage_coins: List) -> str:
        """生成交易专长总结"""
        expert_count = len(expertise_summary.get('expert_coins', []))
        skilled_count = len(expertise_summary.get('skilled_coins', []))
        advantage_count = len(advantage_coins)
        
        if expert_count >= 3:
            return f"多币种专家，在{expert_count}个币种上达到专家级水平"
        elif expert_count >= 1:
            expert_coin = expertise_summary['expert_coins'][0]['contract']
            return f"{expert_coin}专家，具备专业级交易能力"
        elif skilled_count >= 2:
            return f"在{skilled_count}个币种上表现熟练，有较强适应性"
        elif advantage_count >= 1:
            advantage_type = advantage_coins[0]['advantage_type']
            return f"具备{advantage_type}优势，有一定专业基础"
        else:
            return "正在积累交易经验，建议专注少数币种深度研究"
    
    def _generate_improvement_suggestions(self, coin_analysis: Dict[str, CoinAnalysisResult], 
                                        expertise_summary: Dict) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        weak_coins = expertise_summary.get('weak_coins', [])
        expert_coins = expertise_summary.get('expert_coins', [])
        
        # 基于弱势币种的建议
        if len(weak_coins) > len(expert_coins) * 2:
            suggestions.append("建议专注于表现较好的币种，减少在弱势币种上的分散交易")
        
        # 基于胜率的建议
        low_win_rate_coins = [
            contract for contract, metrics in coin_analysis.items() 
            if metrics.win_rate < 0.4
        ]
        if low_win_rate_coins:
            coin_names = ', '.join(low_win_rate_coins[:2])
            suggestions.append(f"在{coin_names}等币种上胜率较低，建议深入研究或暂停交易")
        
        # 基于交易量的建议
        high_volume_low_performance = [
            contract for contract, metrics in coin_analysis.items()
            if (metrics.total_volume > 20000 and 
                self._calculate_performance_score(metrics.win_rate, metrics.profit_factor,
                                                metrics.total_volume, metrics.total_trades) < 50)
        ]
        if high_volume_low_performance:
            suggestions.append("在一些高交易量币种上表现不佳，建议优化交易策略")
        
        # 专业度提升建议
        if not expert_coins:
            suggestions.append("建议选择1-2个主要币种进行深度研究，提升专业水平")
        
        return suggestions
    
    def get_coin_analysis_summary(self, coin_analysis_result: CoinWinRateAnalysis) -> Dict:
        """获取币种分析摘要"""
        return {
            'total_analyzed_coins': coin_analysis_result.total_analyzed_coins,
            'avg_coin_win_rate': coin_analysis_result.avg_coin_win_rate,
            'expert_coins_count': len(coin_analysis_result.coin_expertise_summary.get('expert_coins', [])),
            'skilled_coins_count': len(coin_analysis_result.coin_expertise_summary.get('skilled_coins', [])),
            'advantage_coins_count': len(coin_analysis_result.advantage_coins),
            'top_3_coins': coin_analysis_result.coin_performance_ranking[:3] if coin_analysis_result.coin_performance_ranking else []
        } 