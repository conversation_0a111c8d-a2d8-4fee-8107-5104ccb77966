"""
用户分析API模块统一入口
统一管理用户分析功能，提供完整的API接口
"""

from flask import Blueprint, request, redirect, url_for, jsonify
from .unified_user_api import unified_user_bp

# 创建统一的用户分析蓝图
unified_user_analysis_bp = Blueprint('user_analysis_unified', __name__, url_prefix='/api/user-analysis')

def register_unified_apis(app):
    """
    注册统一的用户分析API
    提供完整的用户分析功能和兼容性支持
    """
    
    # 1. 注册新的统一API（主要接口）
    app.register_blueprint(unified_user_bp)
    
    # 2. 创建兼容性重定向路由
    @unified_user_analysis_bp.route('/legacy-search/<member_id>', methods=['GET'])
    def legacy_search_redirect(member_id):
        """重定向旧的搜索接口到新的搜索接口"""
        # 获取原始参数
        args = request.args.to_dict()
        
        # 重定向到新的搜索接口
        return redirect(url_for('unified_user_analysis.search_user_analysis', 
                               member_id=member_id, **args))
    
    @unified_user_analysis_bp.route('/legacy-search-by-digital/<digital_id>', methods=['GET'])
    def legacy_search_by_digital_redirect(digital_id):
        """重定向数字ID搜索到新接口"""
        # 获取原始参数
        args = request.args.to_dict()
        
        # 重定向到新的数字ID搜索接口
        return redirect(url_for('unified_user_analysis.search_user_analysis_by_digital', 
                               digital_id=digital_id, **args))
    
    @unified_user_analysis_bp.route('/legacy-batch-analysis', methods=['POST'])
    def legacy_batch_analysis_redirect():
        """重定向批量分析到新接口"""
        return redirect(url_for('unified_user_analysis.batch_analyze_users'), 
                       code=307)  # 保持POST方法
    
    # 注册统一蓝图
    app.register_blueprint(unified_user_analysis_bp)
    
    # 3. 为了向后兼容，创建旧路径的重定向
    @app.route('/api/user/search/<member_id>', methods=['GET'])
    def old_user_search_redirect(member_id):
        """旧用户搜索接口重定向"""
        query_string = request.query_string.decode('utf-8')
        redirect_url = f'/api/user-analysis/search/{member_id}'
        if query_string:
            redirect_url += f'?{query_string}'
        return redirect(redirect_url)
    
    @app.route('/api/user/search-by-digital/<digital_id>', methods=['GET'])
    def old_digital_search_redirect(digital_id):
        """旧数字ID搜索接口重定向"""
        query_string = request.query_string.decode('utf-8')
        redirect_url = f'/api/user-analysis/search-by-digital/{digital_id}'
        if query_string:
            redirect_url += f'?{query_string}'
        return redirect(redirect_url)
    
    @app.route('/api/user/batch-search', methods=['POST'])
    def old_batch_search_redirect():
        """旧批量搜索接口重定向"""
        return redirect('/api/user-analysis/batch-analysis', code=307)
    
    @app.route('/api/user/available-tasks', methods=['GET'])
    def old_available_tasks_redirect():
        """旧任务列表接口重定向"""
        return redirect('/api/user-analysis/available-tasks')
    
    # 用户行为分析API的兼容性重定向
    @app.route('/api/user-behavior/<path:endpoint>', methods=['GET', 'POST'])
    def old_behavior_api_redirect(endpoint):
        """旧用户行为分析接口重定向"""
        # 根据endpoint映射到新的接口
        if endpoint.startswith('complete-analysis/'):
            user_id = endpoint.replace('complete-analysis/', '')
            query_string = request.query_string.decode('utf-8')
            redirect_url = f'/api/user-analysis/complete-analysis/{user_id}'
            if query_string:
                redirect_url += f'?{query_string}'
            return redirect(redirect_url)
        elif endpoint == 'batch-analysis':
            return redirect('/api/user-analysis/batch-analysis', code=307)
        elif endpoint.startswith('analysis-report/'):
            user_id = endpoint.replace('analysis-report/', '')
            query_string = request.query_string.decode('utf-8')
            redirect_url = f'/api/user-analysis/analysis-report/{user_id}'
            if query_string:
                redirect_url += f'?{query_string}'
            return redirect(redirect_url)
        elif endpoint.startswith('coin-analysis/'):
            user_id = endpoint.replace('coin-analysis/', '')
            query_string = request.query_string.decode('utf-8')
            redirect_url = f'/api/user-analysis/coin-analysis/{user_id}'
            if query_string:
                redirect_url += f'?{query_string}'
            return redirect(redirect_url)
        elif endpoint == 'analyzer-status':
            return redirect('/api/user-analysis/analyzer-status')
        else:
            return jsonify({
                'status': 'error',
                'message': f'接口 {endpoint} 已废弃，请使用新的统一API'
            }), 404

def get_api_routes_info():
    """
    获取API路由信息，用于文档和调试
    """
    return {
        'primary_apis': {
            'search_analysis': '/api/user-analysis/search/<member_id>',
            'search_by_digital': '/api/user-analysis/search-by-digital/<digital_id>',
            'complete_analysis': '/api/user-analysis/complete-analysis/<user_id>',
            'batch_analysis': '/api/user-analysis/batch-analysis',
            'coin_analysis': '/api/user-analysis/coin-analysis/<user_id>',
            'analysis_report': '/api/user-analysis/analysis-report/<user_id>',
            'available_tasks': '/api/user-analysis/available-tasks',
            'analyzer_status': '/api/user-analysis/analyzer-status'
        },
        'legacy_redirects': {
            'old_user_search': '/api/user/search/<member_id> → /api/user-analysis/search/<member_id>',
            'old_digital_search': '/api/user/search-by-digital/<digital_id> → /api/user-analysis/search-by-digital/<digital_id>',
            'old_batch_search': '/api/user/batch-search → /api/user-analysis/batch-analysis',
            'old_behavior_apis': '/api/user-behavior/* → /api/user-analysis/*'
        },
        'migration_status': {
            'user_api.py': '已整合到 unified_user_api.py',
            'user_behavior_api.py': '已整合到 unified_user_api.py',
            'compatibility': '通过重定向保持向后兼容',
            'recommended': '使用 /api/user-analysis/* 作为主要接口'
        }
    } 