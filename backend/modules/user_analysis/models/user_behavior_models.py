"""
用户交易行为分析数据模型
包含基础指标、衍生指标、评分结果等数据结构定义
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum


class TraderType(Enum):
    """交易者类型枚举"""
    PROFESSIONAL = "专业交易员"
    SEMI_PROFESSIONAL = "半专业交易员"
    RETAIL = "普通散户"
    NEW_USER = "新用户"


class FundScale(Enum):
    """资金规模枚举"""
    LARGE = "大户"
    MEDIUM = "中户"
    RETAIL = "散户"
    SMALL = "小户"


class ExpertiseLevel(Enum):
    """币种专业度等级"""
    EXPERT = "专家级"
    SKILLED = "熟练级"
    AVERAGE = "一般级"
    WEAK = "新手级"


@dataclass
class BasicMetrics:
    """基础数据指标"""
    # 交易规模指标
    total_volume: float = 0.0              # 总交易量 (USDT)
    total_trades: int = 0                  # 总交易笔数
    avg_trade_size: float = 0.0            # 平均交易规模
    total_positions: int = 0               # 总持仓数量
    completed_positions: int = 0           # 已完成持仓数量
    
    # 盈亏统计指标
    profitable_count: int = 0              # 盈利笔数
    loss_count: int = 0                    # 亏损笔数
    total_profit: float = 0.0              # 盈利总金额
    total_loss: float = 0.0                # 亏损总金额
    
    # 持仓时间指标
    avg_profit_duration: float = 0.0       # 盈利订单平均持续时间(分钟)
    avg_loss_duration: float = 0.0         # 亏损订单平均持续时间(分钟)
    total_trading_days: int = 0            # 总交易天数
    max_holding_time: float = 0.0          # 最大持仓时间(分钟)
    min_holding_time: float = 0.0          # 最小持仓时间(分钟)
    
    # 订单类型分布
    market_orders_open: int = 0            # 开仓市价单笔数
    limit_orders_open: int = 0             # 开仓限价单笔数
    market_orders_close: int = 0           # 平仓市价单笔数
    limit_orders_close: int = 0            # 平仓限价单笔数
    open_limit_orders: int = 0             # 开仓限价单总数
    close_limit_orders: int = 0            # 平仓限价单总数
    
    # 风险控制指标
    leverage_list: List[float] = None      # 所有杠杆倍数列表
    total_commission: float = 0.0          # 总交易手续费
    low_leverage_trades: int = 0           # 低杠杆交易笔数(1-5x)
    medium_leverage_trades: int = 0        # 中杠杆交易笔数(5-10x)
    high_leverage_trades: int = 0          # 高杠杆交易笔数(>10x)
    fee_ratio: float = 0.0                 # 手续费率(手续费/总交易量)
    
    # 资金规模分布
    volume_distribution: Dict = None       # 交易量分布
    position_sizes: List[float] = None     # 所有仓位规模列表
    
    # 真实交易规模
    real_trading_volume: float = 0.0       # 剔除异常交易后的真实交易量
    
    # 🚀 新增扩展指标字段 (来自calculate_extended_metrics)
    max_trade_amount: float = 0.0          # 最大单笔交易金额
    max_trade_contract: str = ""           # 最大交易对应的合约
    min_trade_amount: float = 0.0          # 最小单笔交易金额  
    min_trade_contract: str = ""           # 最小交易对应的合约
    
    # 币种偏好分布
    coin_preference_distribution: Dict = None  # 币种偏好分布 {mainstream, altcoin, defi, others}
    defi_percentage: float = 0.0          # DeFi代币交易占比
    others_percentage: float = 0.0        # 其他币种交易占比
    
    # 时间分布
    time_distribution: Dict = None         # 24小时交易时间分布
    
    # 杠杆分布
    leverage_distribution: Dict = None     # 杠杆使用分布 {1x, 2-5x, 6-10x, 11-20x, 20x+}
    
    # 订单类型详细分布
    order_type_distribution: Dict = None   # 详细订单类型分布
    
    # 仓位规模分布
    position_size_distribution: Dict = None # 仓位规模分布 {small, medium, large, extra_large}
    
    def __post_init__(self):
        """初始化后处理"""
        if self.leverage_list is None:
            self.leverage_list = []
        if self.volume_distribution is None:
            self.volume_distribution = {}
        if self.position_sizes is None:
            self.position_sizes = []
        # 🚀 初始化扩展指标字段
        if self.coin_preference_distribution is None:
            self.coin_preference_distribution = {}
        if self.time_distribution is None:
            self.time_distribution = {}
        if self.leverage_distribution is None:
            self.leverage_distribution = {}
        if self.order_type_distribution is None:
            self.order_type_distribution = {}
        if self.position_size_distribution is None:
            self.position_size_distribution = {}


@dataclass
class DerivedMetrics:
    """衍生分析指标"""
    # 盈利能力指标 (40% 权重)
    win_rate: float = 0.0                  # 胜率 = 盈利笔数/总笔数
    profit_loss_ratio: float = 0.0         # 盈亏比 = 平均盈利/平均亏损
    profit_factor: float = 0.0             # 盈利因子 = 总盈利/总亏损
    profit_consistency: float = 0.0        # 盈利一致性
    
    # 风险控制指标 (25% 权重)
    avg_leverage: float = 0.0              # 平均杠杆倍数
    max_leverage: float = 0.0              # 最大杠杆倍数
    leverage_stability: float = 0.0        # 杠杆使用稳定性
    max_single_loss: float = 0.0           # 单笔最大亏损
    max_single_loss_ratio: float = 0.0     # 单笔最大亏损比例
    
    # 交易行为指标 (20% 权重)
    trading_frequency: float = 0.0         # 交易频率 (笔/天)
    market_order_ratio: float = 0.0        # 市价单比例
    profit_loss_duration_ratio: float = 0.0 # 盈亏时长比
    position_size_consistency: float = 0.0  # 仓位规模一致性
    
    # 市场理解指标 (15% 权重)
    position_timing_ability: float = 0.0      # 持仓时机把握能力 (35%)
    risk_management_discipline: float = 0.0    # 风险管理纪律性 (40%)
    trading_execution_efficiency: float = 0.0  # 交易执行效率 (25%)

    # 🚀 新增: 高级分析指标 (4个字段)
    position_consistency: float = 0.0         # 仓位一致性
    timing_ability: float = 0.0               # 时机把握能力
    risk_discipline: float = 0.0              # 风险纪律性
    execution_efficiency: float = 0.0         # 执行效率


@dataclass
class ProfessionalScores:
    """专业度评分"""
    profitability_score: float = 0.0      # 盈利能力评分
    risk_control_score: float = 0.0       # 风险控制评分
    trading_behavior_score: float = 0.0   # 交易行为评分
    market_understanding_score: float = 0.0 # 市场理解评分
    total_score: float = 0.0              # 综合专业度评分
    trader_type: str = "数据不足"          # 🚀 修改默认值：用户类型
    confidence_level: float = 0.0         # 分类置信度


@dataclass
class CoinAnalysisResult:
    """单个币种分析结果"""
    contract: str                          # 合约名称
    total_trades: int = 0                  # 总交易笔数
    profitable_trades: int = 0             # 盈利笔数
    win_rate: float = 0.0                  # 胜率
    total_volume: float = 0.0              # 总交易量
    total_profit: float = 0.0              # 总盈利
    total_loss: float = 0.0                # 总亏损
    net_pnl: float = 0.0                   # 净盈亏
    avg_trade_size: float = 0.0            # 平均交易规模
    profit_factor: float = 0.0             # 盈利因子
    avg_profit_per_trade: float = 0.0      # 平均单笔盈利
    avg_loss_per_trade: float = 0.0        # 平均单笔亏损
    max_single_profit: float = 0.0         # 最大单笔盈利
    max_single_loss: float = 0.0           # 最大单笔亏损
    performance_rank: int = 0              # 性能排名
    expertise_level: str = ExpertiseLevel.WEAK.value  # 专业程度


@dataclass
class CoinWinRateAnalysis:
    """币种胜率分析结果"""
    coin_analysis: Dict[str, CoinAnalysisResult] = None  # 各币种详细分析
    advantage_coins: List[Dict] = None                   # 优势币种列表
    coin_expertise_summary: Dict = None                  # 币种专业度汇总
    coin_performance_ranking: List[Dict] = None          # 币种性能排名
    total_analyzed_coins: int = 0                        # 分析的币种总数
    avg_coin_win_rate: float = 0.0                       # 平均币种胜率
    
    def __post_init__(self):
        """初始化后处理"""
        if self.coin_analysis is None:
            self.coin_analysis = {}
        if self.advantage_coins is None:
            self.advantage_coins = []
        if self.coin_expertise_summary is None:
            self.coin_expertise_summary = {
                'expert_coins': [],
                'skilled_coins': [],
                'average_coins': [],
                'weak_coins': []
            }
        if self.coin_performance_ranking is None:
            self.coin_performance_ranking = []


# HedgeStatistics类已被移除 - 对冲功能已删除


@dataclass
class TradingPreferences:
    """交易偏好分析"""
    # 币种偏好
    major_coins_ratio: float = 0.0         # 主流币种交易比例 (BTC/ETH)
    altcoins_ratio: float = 0.0            # 山寨币交易比例
    favorite_contracts: List[str] = None   # 偏好合约列表
    diversification_score: float = 0.0     # 分散化程度评分
    
    # 时间偏好
    peak_trading_hours: Dict = None        # 🚀 修改：24小时交易分布统计 {"0": 15, "1": 3, ...}
    peak_hours: List[str] = None           # 主要交易时间段列表
    time_distribution: Dict = None         # 24小时交易分布（兼容性保留）
    heatmap_grid: List[List[int]] = None   # 热力图数据 (7天 x 24小时)
    weekend_activity: float = 0.0          # 周末活跃度
    session_preference: str = ""           # 时段偏好 (亚洲/欧洲/美洲)
    legacy_peak_trading_hours: str = ""    # 🚀 新增：原有格式的交易时间段（兼容性保留）
    
    # 风险偏好
    risk_appetite: str = ""                # 风险偏好 (保守/稳健/激进)
    volatility_preference: str = ""        # 波动偏好 (低波动/中波动/高波动)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.favorite_contracts is None:
            self.favorite_contracts = []
        if self.peak_hours is None:
            self.peak_hours = []
        if self.time_distribution is None:
            self.time_distribution = {}
        if self.heatmap_grid is None:
            self.heatmap_grid = [[0] * 24 for _ in range(7)]
        # 🚀 新增：初始化24小时交易分布字典
        if self.peak_trading_hours is None:
            self.peak_trading_hours = {str(hour): 0 for hour in range(24)}


@dataclass
class AbnormalAnalysis:
    """异常交易分析"""
    abnormal_volume: float = 0.0           # 异常交易总量
    abnormal_ratio: float = 0.0            # 异常交易比例
    wash_trading_volume: float = 0.0       # 对敲交易量
    high_frequency_volume: float = 0.0     # 高频交易量
    funding_arbitrage_volume: float = 0.0  # 资金费率套利量
    risk_events_count: int = 0             # 风险事件总数
    abnormal_details: Dict = None          # 异常交易详情
    
    def __post_init__(self):
        """初始化后处理"""
        if self.abnormal_details is None:
            self.abnormal_details = {}


@dataclass
class UserBehaviorProfile:
    """用户交易行为画像 - 完整结果"""
    user_id: str                           # 用户ID
    analysis_timestamp: datetime           # 分析时间戳
    analysis_period_start: datetime        # 分析期间开始时间
    analysis_period_end: datetime          # 分析期间结束时间
    
    # 核心分析结果
    basic_metrics: BasicMetrics            # 基础指标
    derived_metrics: DerivedMetrics        # 衍生指标
    professional_scores: ProfessionalScores # 专业度评分
    
    # 扩展分析结果
    coin_win_rate_analysis: CoinWinRateAnalysis # 币种胜率分析
    # hedge_statistics: HedgeStatistics      # 对冲数据统计 - 已移除
    trading_preferences: TradingPreferences # 交易偏好
    abnormal_analysis: AbnormalAnalysis    # 异常交易分析
    
    # 分类结果
    fund_scale_category: str = "数据不足"   # 🚀 修改默认值：资金规模分类
    
    # 数据质量标识
    data_quality_score: float = 0.0       # 数据质量评分
    analysis_confidence: float = 0.0      # 分析置信度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，用于API返回"""
        return {
            'user_id': self.user_id,
            'analysis_timestamp': self.analysis_timestamp.isoformat(),
            'analysis_period': {
                'start': self.analysis_period_start.isoformat(),
                'end': self.analysis_period_end.isoformat()
            },
            'basic_metrics': self.basic_metrics.__dict__,
            'derived_metrics': self.derived_metrics.__dict__,
            'professional_scores': self.professional_scores.__dict__,
            'coin_win_rate_analysis': {
                'coin_analysis': {k: v.__dict__ for k, v in self.coin_win_rate_analysis.coin_analysis.items()},
                'advantage_coins': self.coin_win_rate_analysis.advantage_coins,
                'coin_expertise_summary': self.coin_win_rate_analysis.coin_expertise_summary,
                'coin_performance_ranking': self.coin_win_rate_analysis.coin_performance_ranking,
                'total_analyzed_coins': self.coin_win_rate_analysis.total_analyzed_coins,
                'avg_coin_win_rate': self.coin_win_rate_analysis.avg_coin_win_rate
            },
            # 'hedge_statistics': self.hedge_statistics.__dict__,  # 对冲统计已移除
            'trading_preferences': self.trading_preferences.__dict__,
            'abnormal_analysis': self.abnormal_analysis.__dict__,
            'fund_scale_category': self.fund_scale_category,
            'data_quality_score': self.data_quality_score,
            'analysis_confidence': self.analysis_confidence
        }


@dataclass
class PositionData:
    """持仓数据模型 - 用于分析的标准化数据结构"""
    position_id: str                       # 持仓ID
    member_id: str                         # 用户ID
    contract_name: str                     # 合约名称
    primary_side: int                      # 主要方向 (1=多头, 3=空头)
    
    # 时间信息
    open_time: datetime                    # 开仓时间
    close_time: Optional[datetime]         # 平仓时间 (可能为None，表示未完成的持仓)
    duration_minutes: float                # 持仓时长(分钟)
    
    # 交易金额
    total_open_amount: float               # 总开仓金额
    total_close_amount: float              # 总平仓金额
    avg_open_price: float                  # 平均开仓价格
    avg_close_price: float                 # 平均平仓价格
    
    # 盈亏信息
    total_pnl: float                       # 总盈亏
    total_commission: float                # 总手续费
    net_pnl: float                         # 净盈亏
    
    # 杠杆信息
    leverage: float                        # 杠杆倍数
    
    # 关联信息
    task_id: str = ""                      # 关联的任务ID
    
    # 订单类型统计
    market_orders_open: int = 0            # 开仓市价单数量
    limit_orders_open: int = 0             # 开仓限价单数量
    market_orders_close: int = 0           # 平仓市价单数量
    limit_orders_close: int = 0            # 平仓限价单数量
    
    @property
    def is_profitable(self) -> bool:
        """是否盈利"""
        return self.total_pnl > 0
    
    @property
    def duration_hours(self) -> float:
        """持仓时长(小时)"""
        return self.duration_minutes / 60.0
    
    @property
    def duration_days(self) -> float:
        """持仓时长(天)"""
        return self.duration_minutes / (60.0 * 24.0)
    
    @property
    def coin_symbol(self) -> str:
        """提取币种符号"""
        # 从合约名称中提取币种，如 "BTC/USDT" -> "BTC"
        if '/' in self.contract_name:
            return self.contract_name.split('/')[0]
        return self.contract_name
    
    @property
    def market_order_ratio(self) -> float:
        """市价单比例"""
        total_orders = (self.market_orders_open + self.limit_orders_open + 
                       self.market_orders_close + self.limit_orders_close)
        if total_orders == 0:
            return 0.0
        market_orders = self.market_orders_open + self.market_orders_close
        return market_orders / total_orders


# 配置相关的数据类
@dataclass
class AnalysisConfig:
    """分析配置"""
    min_total_volume: float
    min_trades_count: int
    analysis_time_window_days: int
    fund_scale_categories: Dict[str, float]
    new_user_criteria: Dict[str, int]
    scoring_weights: Dict[str, float]
    market_understanding_weights: Dict[str, float]
    profitability_weights: Dict[str, float]
    risk_control_weights: Dict[str, float]
    trading_behavior_weights: Dict[str, float]
    trader_type_ranges: Dict[str, List[float]]
    coin_categories: Dict[str, List[str]]
    coin_win_rate_analysis: Dict
    abnormal_weights: Dict[str, float]
    # hedge_display: Dict  # 对冲显示配置已移除
    large_trader_analysis: Dict
    validation: Dict 