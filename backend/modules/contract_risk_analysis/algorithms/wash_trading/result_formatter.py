import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class WashTradingResultFormatter:
    """
    对敲交易结果格式化器
    负责格式化显示对敲交易的详细信息
    """
    
    def __init__(self):
        """初始化结果格式化器"""
        pass
    
    def format_trade_pair_details(self, trade_pairs: List[Dict], contract_name: str) -> str:
        """
        格式化交易对详情为用户友好的显示格式
        
        参数:
            trade_pairs: 交易对详情列表
            contract_name: 合约名称
            
        返回:
            格式化的详情字符串
        """
        if not trade_pairs:
            return f"🎯 {contract_name} 对敲详情\n暂无详细交易对信息"
        
        try:
            details = []
            details.append(f"🎯 {contract_name} 对敲详情")
            details.append("")
            
            for i, pair in enumerate(trade_pairs, 1):
                pair_detail = self._format_single_pair(pair, i)
                details.append(pair_detail)
                if i < len(trade_pairs):
                    details.append("")  # 空行分隔
            
            # 添加汇总信息
            summary = self._format_summary(trade_pairs, contract_name)
            details.append("")
            details.append(summary)
            
            return "\n".join(details)
            
        except Exception as e:
            logger.error(f"格式化交易对详情失败: {str(e)}")
            return f"🎯 {contract_name} 对敲详情\n格式化失败: {str(e)}"
    
    def _format_single_pair(self, pair: Dict, pair_number: int) -> str:
        """
        格式化单个交易对的详情
        
        参数:
            pair: 交易对详情
            pair_number: 交易对序号
            
        返回:
            格式化的单个交易对字符串
        """
        try:
            user_a = pair['user_a']
            user_b = pair['user_b']
            time_gaps = pair['time_gaps']
            
            # 格式化开仓方向
            user_a_direction = self._format_direction(user_a['open_side'])
            user_b_direction = self._format_direction(user_b['open_side'])
            
            # 格式化时间
            user_a_open_time = self._format_time(user_a['open_time'])
            user_b_open_time = self._format_time(user_b['open_time'])
            user_a_close_time = self._format_time(user_a.get('close_time'))
            user_b_close_time = self._format_time(user_b.get('close_time'))
            
            # 格式化金额
            user_a_open_amount = self._format_amount(user_a['open_amount'])
            user_b_open_amount = self._format_amount(user_b['open_amount'])
            user_a_close_amount = self._format_amount(user_a.get('close_amount', 0))
            user_b_close_amount = self._format_amount(user_b.get('close_amount', 0))
            
            # 格式化利润
            user_a_profit = self._format_profit(user_a.get('profit', 0))
            user_b_profit = self._format_profit(user_b.get('profit', 0))
            
            # 时间差
            open_gap = time_gaps.get('open_gap_seconds', 0)
            close_gap = time_gaps.get('close_gap_seconds', 0)
            
            # 构建详情字符串
            lines = []
            lines.append(f"📊 交易对 #{pair_number}")
            lines.append(f"┌─ 用户A ({self._format_user_id(user_a['member_id'])})")
            lines.append(f"│  {user_a_direction['emoji']} {user_a_direction['text']}: {user_a_open_time}")
            lines.append(f"│  💰 开仓: {user_a_open_amount} USDT")
            
            if user_a_close_time != '未平仓':
                lines.append(f"│  📉 平仓: {user_a_close_time}")
                lines.append(f"│  💰 平仓: {user_a_close_amount} USDT")
            else:
                lines.append(f"│  📉 平仓: 未平仓")
            
            lines.append(f"│  📊 利润: {user_a_profit}")
            lines.append(f"└─ 用户B ({self._format_user_id(user_b['member_id'])}) [开仓时间差: {open_gap}秒]")
            lines.append(f"   {user_b_direction['emoji']} {user_b_direction['text']}: {user_b_open_time}")
            lines.append(f"   💰 开仓: {user_b_open_amount} USDT")
            
            if user_b_close_time != '未平仓':
                close_gap_text = f" [平仓时间差: {close_gap}秒]" if close_gap > 0 else ""
                lines.append(f"   📈 平仓: {user_b_close_time}{close_gap_text}")
                lines.append(f"   💰 平仓: {user_b_close_amount} USDT")
            else:
                lines.append(f"   📈 平仓: 未平仓")
            
            lines.append(f"   📊 利润: {user_b_profit}")
            
            # 添加交易对汇总
            total_amount = self._format_amount(pair.get('total_amount', 0))
            net_profit = self._format_profit(pair.get('net_profit', 0))
            lines.append(f"💵 本对合约金额: {total_amount} USDT | 📊 净盈亏: {net_profit}")
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"格式化单个交易对失败: {str(e)}")
            return f"📊 交易对 #{pair_number} - 格式化失败"
    
    def _format_summary(self, trade_pairs: List[Dict], contract_name: str) -> str:
        """
        格式化汇总信息
        
        参数:
            trade_pairs: 交易对详情列表
            contract_name: 合约名称
            
        返回:
            格式化的汇总字符串
        """
        try:
            total_pairs = len(trade_pairs)
            total_amount = sum(pair.get('total_amount', 0) for pair in trade_pairs)
            total_net_profit = sum(pair.get('net_profit', 0) for pair in trade_pairs)
            
            # 统计盈利和亏损的交易对
            profit_pairs = len([p for p in trade_pairs if p.get('net_profit', 0) > 0])
            loss_pairs = len([p for p in trade_pairs if p.get('net_profit', 0) < 0])
            break_even_pairs = total_pairs - profit_pairs - loss_pairs
            
            # 计算平均时间差
            open_gaps = [p['time_gaps']['open_gap_seconds'] for p in trade_pairs]
            close_gaps = [p['time_gaps']['close_gap_seconds'] for p in trade_pairs if p['time_gaps']['close_gap_seconds'] > 0]
            
            avg_open_gap = sum(open_gaps) / len(open_gaps) if open_gaps else 0
            avg_close_gap = sum(close_gaps) / len(close_gaps) if close_gaps else 0
            
            # 构建汇总信息
            lines = []
            lines.append("📈 对敲汇总统计")
            lines.append(f"合约: {contract_name}")
            lines.append(f"交易对数量: {total_pairs}对")
            lines.append(f"总合约金额: {self._format_amount(total_amount)} USDT")
            lines.append(f"总净盈亏: {self._format_profit(total_net_profit)}")
            lines.append(f"盈利交易对: {profit_pairs}对 | 亏损交易对: {loss_pairs}对 | 持平: {break_even_pairs}对")
            lines.append(f"平均开仓时间差: {avg_open_gap:.1f}秒")
            if avg_close_gap > 0:
                lines.append(f"平均平仓时间差: {avg_close_gap:.1f}秒")
            
            return "\n".join(lines)
            
        except Exception as e:
            logger.error(f"格式化汇总信息失败: {str(e)}")
            return "📈 对敲汇总统计 - 格式化失败"
    
    def _format_direction(self, side: int) -> Dict[str, str]:
        """
        格式化交易方向
        
        参数:
            side: 交易方向 (1=开多, 3=开空, 2=平空, 4=平多)
            
        返回:
            包含emoji和文本的字典
        """
        direction_map = {
            1: {'emoji': '📈', 'text': '开多'},
            3: {'emoji': '📉', 'text': '开空'},
            2: {'emoji': '📈', 'text': '平空'},
            4: {'emoji': '📉', 'text': '平多'}
        }
        
        return direction_map.get(side, {'emoji': '❓', 'text': '未知'})
    
    def _format_time(self, timestamp) -> str:
        """
        格式化时间戳
        
        参数:
            timestamp: 时间戳
            
        返回:
            格式化的时间字符串
        """
        if not timestamp:
            return '未平仓'
        
        try:
            if isinstance(timestamp, str):
                # 如果是字符串，尝试解析
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                dt = timestamp
            
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.debug(f"时间格式化失败: {str(e)}")
            return str(timestamp)
    
    def _format_amount(self, amount: float) -> str:
        """
        格式化金额显示
        
        参数:
            amount: 金额
            
        返回:
            格式化的金额字符串
        """
        if amount >= 1000000:
            return f"${amount/1000000:.1f}M"
        elif amount >= 1000:
            return f"${amount/1000:.1f}K"
        else:
            return f"${amount:.2f}"
    
    def _format_profit(self, profit: float) -> str:
        """
        格式化利润显示
        
        参数:
            profit: 利润
            
        返回:
            格式化的利润字符串
        """
        if profit > 0:
            return f"+{self._format_amount(profit)} USDT (盈利)"
        elif profit < 0:
            return f"{self._format_amount(profit)} USDT (亏损)"
        else:
            return f"{self._format_amount(profit)} USDT (持平)"
    
    def _format_user_id(self, user_id: str) -> str:
        """
        格式化用户ID显示
        
        参数:
            user_id: 用户ID
            
        返回:
            格式化的用户ID
        """
        if not user_id:
            return "未知用户"
        
        # 显示完整的用户ID
        return str(user_id)
    
    def format_for_api(self, trade_pairs: List[Dict]) -> List[Dict]:
        """
        为API接口格式化交易对详情
        
        参数:
            trade_pairs: 交易对详情列表
            
        返回:
            API格式的交易对列表
        """
        try:
            formatted_pairs = []
            
            for pair in trade_pairs:
                formatted_pair = {
                    'pair_index': pair.get('pair_index', 0),
                    'contract_name': pair.get('contract_name', ''),
                    'users': {
                        'user_a': {
                            'id': pair['user_a']['member_id'],
                            'position_id': pair['user_a'].get('position_id'),
                            'open': {
                                'time': self._format_time(pair['user_a']['open_time']),
                                'side_text': self._format_direction(pair['user_a']['open_side'])['text'],
                                'amount': float(pair['user_a']['open_amount']),
                                'amount_formatted': self._format_amount(pair['user_a']['open_amount'])
                            },
                            'close': {
                                'time': self._format_time(pair['user_a'].get('close_time')),
                                'side_text': self._format_direction(pair['user_a'].get('close_side', 0))['text'] if pair['user_a'].get('close_side') is not None else '未平仓',
                                'amount': float(pair['user_a'].get('close_amount', 0)),
                                'amount_formatted': self._format_amount(pair['user_a'].get('close_amount', 0))
                            },
                            'profit': float(pair['user_a'].get('profit', 0)),
                            'profit_formatted': self._format_profit(pair['user_a'].get('profit', 0))
                        },
                        'user_b': {
                            'id': pair['user_b']['member_id'],
                            'position_id': pair['user_b'].get('position_id'),
                            'open': {
                                'time': self._format_time(pair['user_b']['open_time']),
                                'side_text': self._format_direction(pair['user_b']['open_side'])['text'],
                                'amount': float(pair['user_b']['open_amount']),
                                'amount_formatted': self._format_amount(pair['user_b']['open_amount'])
                            },
                            'close': {
                                'time': self._format_time(pair['user_b'].get('close_time')),
                                'side_text': self._format_direction(pair['user_b'].get('close_side', 0))['text'] if pair['user_b'].get('close_side') is not None else '未平仓',
                                'amount': float(pair['user_b'].get('close_amount', 0)),
                                'amount_formatted': self._format_amount(pair['user_b'].get('close_amount', 0))
                            },
                            'profit': float(pair['user_b'].get('profit', 0)),
                            'profit_formatted': self._format_profit(pair['user_b'].get('profit', 0))
                        }
                    },
                    'time_gaps': {
                        'open_seconds': pair['time_gaps']['open_gap_seconds'],
                        'close_seconds': pair['time_gaps']['close_gap_seconds'],
                        'open_text': f"{pair['time_gaps']['open_gap_seconds']}秒",
                        'close_text': f"{pair['time_gaps']['close_gap_seconds']}秒" if pair['time_gaps']['close_gap_seconds'] > 0 else "未完全平仓"
                    },
                    'summary': {
                        'total_amount': float(pair.get('total_amount', 0)),
                        'total_amount_formatted': self._format_amount(pair.get('total_amount', 0)),
                        'net_profit': float(pair.get('net_profit', 0)),
                        'net_profit_formatted': self._format_profit(pair.get('net_profit', 0))
                    }
                }
                
                formatted_pairs.append(formatted_pair)
            
            return formatted_pairs
            
        except Exception as e:
            logger.error(f"为API格式化交易对失败: {str(e)}")
            return [] 