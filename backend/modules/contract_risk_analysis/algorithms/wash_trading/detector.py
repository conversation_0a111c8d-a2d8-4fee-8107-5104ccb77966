"""
新一代对敲检测器 - 核心算法实现
Next-Generation Wash Trading Detector - Core Algorithm Implementation

基于我们的深入讨论和优化方案，这个模块实现了：
1. 全局仓位画像预处理
2. 基于平仓时间差的风险分级
3. 高性能的单遍扫描跨账户检测
4. 完整的配置驱动参数管理
5. BD关系检查和频率过滤
"""

import pandas as pd
import numpy as np
import yaml
import logging
from datetime import datetime, timedelta
from collections import defaultdict, namedtuple
from typing import Dict, List, Tuple, Set, Optional, Any
import os
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义交易对数据结构
TradingPair = namedtuple('TradingPair', [
    'pair_id', 'user_a', 'user_b', 'contract_name',
    'open_time_a', 'open_time_b', 'close_time_a', 'close_time_b',
    'position_id_a', 'position_id_b', 'amount_a', 'amount_b',
    'side_a', 'side_b', 'risk_level', 'risk_score'
])

class NextGenWashTradingDetector:
    """新一代对敲检测器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化检测器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.position_profiles = {}  # 仓位画像缓存
        self.detected_pairs = []  # 检测到的交易对
        self.frequency_tracker = defaultdict(list)  # 频率跟踪器
        self.bd_relationships = {}  # BD关系缓存
        
        # 统计信息
        self.statistics = {
            'total_records': 0,
            'valid_records': 0,
            'same_account_pairs': 0,
            'cross_account_pairs': 0,
            'high_risk_pairs': 0,
            'medium_risk_pairs': 0,
            'filtered_by_amount': 0,
            'filtered_by_bd': 0,
            'filtered_by_frequency': 0,
            'processing_time': 0
        }
    
    def _load_config(self, config_path: str = None) -> dict:
        """加载配置文件"""
        if config_path is None:
            # 默认配置文件路径
            current_dir = Path(__file__).parent
            config_path = current_dir / "wash_trading_config.yml"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}，使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> dict:
        """获取默认配置"""
        return {
            'detection': {'min_records': 10},
            'time_thresholds': {
                'open_time_window': 3,  # 更严格的时间窗口
                'risk_classification': {
                    'high_risk_close_window_minutes': 30,
                    'medium_risk_close_window_hours': 24
                }
            },
            'amount_matching': {
                'relative_tolerance': 0.02,  # 更严格的金额容差
                'absolute_tolerance': 0.01
            },
            'same_account': {'enabled': True},
            'cross_account': {
                'enabled': True, 
                'time_window': 3,  # 更严格的时间窗口
                'check_bd_relationship': True
            },
            'network_analysis': {'enabled': True, 'max_nodes': 5000},
            'validation': {'require_position_id': True},
            'business_rules': {
                'min_trade_amount': 10.0,
                'frequency_check': {
                    'enabled': True,
                    'min_wash_count': 3,
                    'time_window_hours': 24
                }
            },
            'default_analysis_result': {
                'trader_type': '数据不足',
                'fund_scale': '数据不足',
                'confidence': 0.0
            }
        }
    
    def detect(self, df: pd.DataFrame, progress_callback=None) -> dict:
        """
        主检测方法
        
        Args:
            df: 交易数据DataFrame
            progress_callback: 进度回调函数
            
        Returns:
            检测结果字典
        """
        start_time = datetime.now()
        logger.info(f"开始新一代对敲检测，数据量: {len(df)} 条")
        
        try:
            # 阶段0：数据字段映射
            if progress_callback:
                progress_callback({'percentage': 5, 'stage': '字段映射', 'message': '映射数据字段...'})
            
            df = self._standardize_dataframe(df)
            
            # 阶段1：数据验证
            if progress_callback:
                progress_callback({'percentage': 10, 'stage': '数据验证', 'message': '数据验证中...'})
            
            if not self._validate_data(df):
                return self._create_empty_result("数据验证失败")
            
            # 阶段2：全局仓位画像构建
            if progress_callback:
                progress_callback({'percentage': 20, 'stage': '仓位画像构建', 'message': '构建仓位画像...'})
            
            self._build_position_profiles(df)
            
            # 阶段3：同账户对敲检测
            if progress_callback:
                progress_callback({'percentage': 40, 'stage': '同账户检测', 'message': '同账户对敲检测...'})
            
            if self.config['same_account']['enabled']:
                self._detect_same_account_wash_trading(df)
            
            # 阶段4：跨账户对敲检测
            if progress_callback:
                progress_callback({'percentage': 60, 'stage': '跨账户检测', 'message': '跨账户对敲检测...'})
            
            if self.config['cross_account']['enabled']:
                self._detect_cross_account_wash_trading(df)
            
            # 阶段5：网络分析（如果启用）
            if progress_callback:
                progress_callback({'percentage': 80, 'stage': '网络分析', 'message': '网络分析...'})
            
            if self.config['network_analysis']['enabled']:
                self._enhanced_network_analysis()
            
            # 阶段6：结果整理和返回
            if progress_callback:
                progress_callback({'percentage': 100, 'stage': '结果整理', 'message': '整理结果...'})
            
            # 记录执行时间
            self.statistics['processing_time'] = (datetime.now() - start_time).total_seconds()
            
            return self._format_results()
            
        except Exception as e:
            logger.error(f"检测过程出现错误: {e}")
            return self._create_empty_result(f"检测失败: {str(e)}")
    
    def _standardize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化输入数据框的字段名和格式"""
        # 字段名映射字典，将实际字段映射到算法需要的标准字段
        field_map = {
            # 标准字段名: [可能的原始字段名列表]
            'member_id': ['member_id', 'memberId'],
            'digital_id': ['digital_id', 'digitalId'],
            'contract_name': ['contract_name', 'contractName'],
            'side': ['side'],
            'price': ['deal_price', 'price', 'order_price', 'deal_avg_price'],  # 价格字段映射
            'volume': ['deal_vol', 'volume', 'order_vol'],  # 数量字段映射
            'deal_vol_usdt': ['deal_vol_usdt', 'dealVolUsdt'],
            'timestamp': ['timestamp', 'order_create_time', 'create_time'],
            'position_id': ['position_id', 'positionId']
        }
        
        # 创建新的数据框，避免修改原始数据
        standardized_df = df.copy()
        
        # 重命名字段: 遍历映射，找到第一个存在的原始名并重命名为标准名
        for standard_name, possible_original_names in field_map.items():
            if standard_name in standardized_df.columns:  # 如果标准名已存在，跳过
                continue
            for original_name in possible_original_names:
                if original_name in standardized_df.columns:
                    standardized_df[standard_name] = standardized_df[original_name]
                    logger.debug(f"字段映射: {original_name} -> {standard_name}")
                    break  # 找到一个匹配就进行下一个标准名的处理
        
        # 记录映射后的字段情况
        mapped_fields = []
        missing_fields = []
        required_fields = self.config['detection']['required_fields']
        
        for field in required_fields:
            if field in standardized_df.columns:
                mapped_fields.append(field)
            else:
                missing_fields.append(field)
        
        logger.info(f"字段映射完成 - 已映射: {mapped_fields}, 缺失: {missing_fields}")
        
        return standardized_df
    
    def _validate_data(self, df: pd.DataFrame) -> bool:
        """数据验证"""
        self.statistics['total_records'] = len(df)
        
        # 检查最小记录数
        min_records = self.config['detection']['min_records']
        if len(df) < min_records:
            logger.warning(f"数据量不足，最少需要 {min_records} 条记录")
            return False
        
        # 检查必需字段 - 使用更灵活的验证逻辑
        required_fields = self.config['detection']['required_fields']
        critical_fields = ['member_id', 'contract_name', 'side', 'timestamp']  # 关键字段
        
        missing_critical = []
        missing_optional = []
        
        for field in required_fields:
            if field not in df.columns:
                if field in critical_fields:
                    missing_critical.append(field)
                else:
                    missing_optional.append(field)
        
        # 如果缺少关键字段，直接失败
        if missing_critical:
            logger.error(f"缺少关键字段: {missing_critical}")
            return False
        
        # 对于可选字段，尝试提供默认值或警告
        if missing_optional:
            logger.warning(f"缺少可选字段: {missing_optional}, 将尝试使用替代方案")
            
            # 如果没有price字段，尝试计算
            if 'price' in missing_optional:
                if 'deal_vol_usdt' in df.columns and 'volume' in df.columns:
                    # 尝试通过金额和数量计算价格
                    df['price'] = df.apply(lambda row: 
                        row['deal_vol_usdt'] / row['volume'] if row['volume'] > 0 else 0, axis=1)
                    logger.info("通过交易金额和数量计算出价格字段")
                else:
                    logger.warning("无法计算price字段，将使用默认值0")
                    df['price'] = 0
            
            # 如果没有volume字段，尝试使用其他数量字段
            if 'volume' in missing_optional:
                if 'deal_vol' in df.columns:
                    df['volume'] = df['deal_vol']
                    logger.info("使用deal_vol作为volume字段")
                else:
                    logger.warning("无法获取volume字段，将使用默认值1")
                    df['volume'] = 1
        
        # 验证position_id
        if self.config['validation']['require_position_id']:
            if 'position_id' not in df.columns:
                logger.error("配置要求position_id字段，但数据中不存在")
                return False
                
            valid_positions = df['position_id'].notna().sum()
            if valid_positions == 0:
                logger.error("没有有效的position_id，跳过检测")
                return False
            
            # 记录有效记录数
            self.statistics['valid_records'] = valid_positions
            logger.info(f"有效记录数: {valid_positions}/{len(df)}")
        else:
            self.statistics['valid_records'] = len(df)
        
        return True
    
    def _build_position_profiles(self, df: pd.DataFrame):
        """构建全局仓位画像"""
        logger.info("开始构建仓位画像...")
        
        # 过滤有效的position_id记录
        valid_df = df[df['position_id'].notna()].copy()
        
        # 按position_id分组，构建每个仓位的完整生命周期
        position_groups = valid_df.groupby('position_id')
        
        for position_id, group in position_groups:
            # 按时间排序，找到开仓和平仓记录
            group_sorted = group.sort_values('timestamp')
            
            # 开仓记录（第一条）
            open_record = group_sorted.iloc[0]
            
            # 平仓记录（最后一条，如果存在多条记录）
            close_record = group_sorted.iloc[-1] if len(group_sorted) > 1 else None
            
            # 构建仓位画像 (修正字段名)
            profile = {
                'position_id': position_id,
                'member_id': open_record['member_id'],
                'contract_name': open_record['contract_name'],
                'side': open_record['side'],
                'open_time': pd.to_datetime(open_record['timestamp']),
                'open_price': open_record['price'],
                'open_quantity': open_record.get('volume', 0),  # 修正字段名
                'close_time': pd.to_datetime(close_record['timestamp']) if close_record is not None else None,
                'close_price': close_record['price'] if close_record is not None else None,
                'is_closed': close_record is not None,
                'duration_minutes': None,
                'pnl': None
            }
            
            # 计算持仓时间和盈亏
            if profile['is_closed']:
                duration = profile['close_time'] - profile['open_time']
                profile['duration_minutes'] = duration.total_seconds() / 60
                
                # 简单盈亏计算（实际应根据业务规则调整）
                if profile['side'] == 1:  # 开多，修正为数字
                    profile['pnl'] = (profile['close_price'] - profile['open_price']) * profile['open_quantity']
                else:  # 开空
                    profile['pnl'] = (profile['open_price'] - profile['close_price']) * profile['open_quantity']
            
            self.position_profiles[position_id] = profile
        
        logger.info(f"仓位画像构建完成，共 {len(self.position_profiles)} 个仓位")
    
    def _detect_same_account_wash_trading(self, df: pd.DataFrame):
        """同账户对敲检测"""
        logger.info("开始同账户对敲检测...")
        
        # 按(用户, 合约)分组
        user_contract_groups = df[df['position_id'].notna()].groupby(['member_id', 'contract_name'])
        
        for (member_id, contract_name), group in user_contract_groups:
            self._detect_same_account_pairs_in_group(member_id, contract_name, group)
        
        logger.info(f"同账户对敲检测完成，发现 {self.statistics['same_account_pairs']} 对")
    
    def _detect_same_account_pairs_in_group(self, member_id: str, contract_name: str, group: pd.DataFrame):
        """在单个用户-合约组内检测对敲"""
        # 分离开多和开空记录
        long_opens = group[group['side'] == 1].copy()    # 1=开多
        short_opens = group[group['side'] == 3].copy()   # 3=开空
        
        if len(long_opens) == 0 or len(short_opens) == 0:
            return
        
        # 转换时间戳
        long_opens['timestamp'] = pd.to_datetime(long_opens['timestamp'])
        short_opens['timestamp'] = pd.to_datetime(short_opens['timestamp'])
        
        # 时间窗口配置
        time_window = timedelta(seconds=self.config['time_thresholds']['open_time_window'])
        
        # 查找匹配的开仓对
        for _, long_record in long_opens.iterrows():
            long_time = long_record['timestamp']
            long_position_id = long_record['position_id']
            
            # 在时间窗口内寻找匹配的开空仓
            time_mask = (short_opens['timestamp'] >= long_time - time_window) & \
                       (short_opens['timestamp'] <= long_time + time_window)
            
            matching_shorts = short_opens[time_mask]
            
            for _, short_record in matching_shorts.iterrows():
                short_position_id = short_record['position_id']
                
                # 检查金额是否匹配
                long_amount = long_record.get('deal_vol_usdt', long_record.get('volume', 0) * long_record.get('price', 0))
                short_amount = short_record.get('deal_vol_usdt', short_record.get('volume', 0) * short_record.get('price', 0))
                
                # 最小交易金额过滤
                min_amount = self.config['business_rules']['min_trade_amount']
                if long_amount < min_amount or short_amount < min_amount:
                    self.statistics['filtered_by_amount'] += 1
                    continue
                
                if self._amounts_match(long_amount, short_amount):
                    
                    # 获取仓位画像进行风险评估
                    long_profile = self.position_profiles.get(long_position_id)
                    short_profile = self.position_profiles.get(short_position_id)
                    
                    if long_profile and short_profile:
                        # 计算风险等级
                        risk_level, risk_score = self._calculate_risk_level(long_profile, short_profile)
                        
                        # 创建交易对
                        pair = TradingPair(
                            pair_id=f"same_{member_id}_{len(self.detected_pairs)}",
                            user_a=member_id,
                            user_b=member_id,  # 同账户
                            contract_name=contract_name,
                            open_time_a=long_record['timestamp'],
                            open_time_b=short_record['timestamp'],
                            close_time_a=long_profile['close_time'],
                            close_time_b=short_profile['close_time'],
                            position_id_a=long_position_id,
                            position_id_b=short_position_id,
                            amount_a=long_amount,
                            amount_b=short_amount,
                            side_a=1,  # 开多
                            side_b=3,  # 开空
                            risk_level=risk_level,
                            risk_score=risk_score
                        )
                        
                        self.detected_pairs.append(pair)
                        self.statistics['same_account_pairs'] += 1
                        
                        # 更新风险等级统计
                        if risk_level == 'High':
                            self.statistics['high_risk_pairs'] += 1
                        elif risk_level == 'Medium':
                            self.statistics['medium_risk_pairs'] += 1
    
    def _detect_cross_account_wash_trading(self, df: pd.DataFrame):
        """跨账户对敲检测 - 使用单遍扫描算法"""
        logger.info("开始跨账户对敲检测...")
        
        # 按合约分组处理
        contract_groups = df[df['position_id'].notna()].groupby('contract_name')
        processed_pairs = set()  # 避免重复计算
        
        for contract_name, group in contract_groups:
            # 按时间排序
            sorted_group = group.sort_values('timestamp')
            sorted_group['timestamp'] = pd.to_datetime(sorted_group['timestamp'])
            
            # 单遍扫描算法
            time_window = timedelta(seconds=self.config['cross_account']['time_window'])
            
            for i, record_a in sorted_group.iterrows():
                user_a = record_a['member_id']
                time_a = record_a['timestamp']
                side_a = record_a['side']
                
                # 在时间窗口内寻找反向交易
                window_start = time_a
                window_end = time_a + time_window
                
                # 查找窗口内的其他交易
                opposing_sides = []
                if side_a == 1:  # 如果A是开多，寻找开空
                    opposing_sides = [3]
                elif side_a == 3:  # 如果A是开空，寻找开多
                    opposing_sides = [1]
                else:
                    continue  # 平仓交易不参与对敲检测
                
                window_mask = (sorted_group['timestamp'] >= window_start) & \
                             (sorted_group['timestamp'] <= window_end) & \
                             (sorted_group['member_id'] != user_a) & \
                             (sorted_group['side'].isin(opposing_sides))  # 反向交易
                
                window_records = sorted_group[window_mask]
                
                for j, record_b in window_records.iterrows():
                    user_b = record_b['member_id']
                    
                    # 避免重复处理（A-B和B-A是同一对）
                    pair_key = frozenset([user_a, user_b])
                    if pair_key in processed_pairs:
                        continue
                    
                    # 检查金额匹配
                    amount_a = record_a.get('deal_vol_usdt', record_a.get('volume', 0) * record_a.get('price', 0))
                    amount_b = record_b.get('deal_vol_usdt', record_b.get('volume', 0) * record_b.get('price', 0))
                    
                    # 最小交易金额过滤
                    min_amount = self.config['business_rules']['min_trade_amount']
                    if amount_a < min_amount or amount_b < min_amount:
                        self.statistics['filtered_by_amount'] += 1
                        continue
                    
                    if self._amounts_match(amount_a, amount_b):
                        
                        # BD关系检查
                        if self.config['cross_account']['check_bd_relationship']:
                            if self._check_bd_relationship(user_a, user_b):
                                self.statistics['filtered_by_bd'] += 1
                                continue  # 同BD下的用户，跳过
                        
                        # 获取仓位画像
                        profile_a = self.position_profiles.get(record_a['position_id'])
                        profile_b = self.position_profiles.get(record_b['position_id'])
                        
                        if profile_a and profile_b:
                            # 计算风险等级
                            risk_level, risk_score = self._calculate_risk_level(profile_a, profile_b)
                            
                            # 创建交易对
                            pair = TradingPair(
                                pair_id=f"cross_{user_a}_{user_b}_{len(self.detected_pairs)}",
                                user_a=user_a,
                                user_b=user_b,
                                contract_name=contract_name,
                                open_time_a=record_a['timestamp'],
                                open_time_b=record_b['timestamp'],
                                close_time_a=profile_a['close_time'],
                                close_time_b=profile_b['close_time'],
                                position_id_a=record_a['position_id'],
                                position_id_b=record_b['position_id'],
                                amount_a=amount_a,
                                amount_b=amount_b,
                                side_a=side_a,
                                side_b=record_b['side'],
                                risk_level=risk_level,
                                risk_score=risk_score
                            )
                            
                            # 记录到频率跟踪器
                            self._track_pair_frequency(user_a, user_b, time_a)
                            
                            self.detected_pairs.append(pair)
                            self.statistics['cross_account_pairs'] += 1
                            processed_pairs.add(pair_key)
                            
                            # 更新风险等级统计
                            if risk_level == 'High':
                                self.statistics['high_risk_pairs'] += 1
                            elif risk_level == 'Medium':
                                self.statistics['medium_risk_pairs'] += 1
        
        # 频率过滤
        if self.config['business_rules']['frequency_check']['enabled']:
            self._apply_frequency_filter()
        
        logger.info(f"跨账户对敲检测完成，发现 {self.statistics['cross_account_pairs']} 对")
    
    def _amounts_match(self, amount_a: float, amount_b: float) -> bool:
        """检查两个金额是否匹配"""
        if amount_a == 0 or amount_b == 0:
            return False
        
        # 相对容差检查
        relative_diff = abs(amount_a - amount_b) / max(amount_a, amount_b)
        relative_tolerance = self.config['amount_matching']['relative_tolerance']
        
        if relative_diff <= relative_tolerance:
            return True
        
        # 绝对容差检查
        absolute_diff = abs(amount_a - amount_b)
        absolute_tolerance = self.config['amount_matching']['absolute_tolerance']
        
        return absolute_diff <= absolute_tolerance
    
    def _calculate_risk_level(self, profile_a: dict, profile_b: dict) -> Tuple[str, float]:
        """
        计算风险等级和评分
        
        Returns:
            (risk_level, risk_score): 风险等级和评分
        """
        # 检查是否都已平仓
        if not (profile_a['is_closed'] and profile_b['is_closed']):
            # 对于同账户对敲，即使未平仓也应该是高风险
            if profile_a.get('member_id') == profile_b.get('member_id'):
                return 'High', 0.8  # 同账户未平仓仍然是高风险
            return 'Medium', 0.6  # 跨账户未平仓为中风险
        
        # 计算平仓时间间隔
        close_gap_minutes = abs(
            (profile_a['close_time'] - profile_b['close_time']).total_seconds() / 60
        )
        
        # 根据配置的阈值判断风险等级
        high_risk_threshold = self.config['time_thresholds']['risk_classification']['high_risk_close_window_minutes']
        medium_risk_threshold = self.config['time_thresholds']['risk_classification']['medium_risk_close_window_hours'] * 60
        
        if close_gap_minutes <= high_risk_threshold:
            return 'High', 0.9
        elif close_gap_minutes <= medium_risk_threshold:
            return 'Medium', 0.6
        else:
            return 'Low', 0.3
    
    def _enhanced_network_analysis(self):
        """增强网络分析"""
        logger.info("开始网络分析...")
        
        # 这里可以实现更复杂的网络分析逻辑
        # 例如使用networkx构建图，进行社区发现等
        # 由于篇幅限制，这里仅做简单实现
        
        # 统计参与跨账户对敲的用户
        cross_account_users = set()
        for pair in self.detected_pairs:
            if pair.user_a != pair.user_b:  # 跨账户
                cross_account_users.add(pair.user_a)
                cross_account_users.add(pair.user_b)
        
        # 将网络分析结果添加到统计信息中（转换为JSON可序列化的格式）
        self.statistics['network_analysis'] = {
            'cross_account_users_count': len(cross_account_users),
            'cross_account_users': list(cross_account_users)  # 转换为列表
        }
        
        logger.info(f"网络分析完成，涉及 {len(cross_account_users)} 个用户")
    
    def _format_results(self) -> dict:
        """格式化结果"""
        # 统计各风险等级的数量
        risk_level_stats = {}
        same_account_stats = {}
        cross_account_stats = {}
        
        for pair in self.detected_pairs:
            risk_level_stats[pair.risk_level] = risk_level_stats.get(pair.risk_level, 0) + 1
            
            # 分类统计
            if pair.user_a == pair.user_b:  # 同账户
                same_account_stats[pair.risk_level] = same_account_stats.get(pair.risk_level, 0) + 1
            else:  # 跨账户
                cross_account_stats[pair.risk_level] = cross_account_stats.get(pair.risk_level, 0) + 1
        
        logger.info(f"风险等级分布: {risk_level_stats}")
        logger.info(f"同账户风险分布: {same_account_stats}")
        logger.info(f"跨账户风险分布: {cross_account_stats}")
        
        # 只返回配置指定的风险等级的交易对
        save_levels = self.config['storage']['save_risk_levels']
        logger.info(f"配置的保存等级: {save_levels}")
        
        filtered_pairs = [pair for pair in self.detected_pairs if pair.risk_level in save_levels]
        
        filtered_out = len(self.detected_pairs) - len(filtered_pairs)
        if filtered_out > 0:
            logger.warning(f"有 {filtered_out} 个对敲对因风险等级过滤被排除")
            filtered_out_risks = [pair.risk_level for pair in self.detected_pairs if pair.risk_level not in save_levels]
            logger.info(f"被过滤的风险等级: {filtered_out_risks}")
            
            # 详细分析被过滤的对敲对
            for pair in self.detected_pairs:
                if pair.risk_level not in save_levels:
                    pair_type = "同账户" if pair.user_a == pair.user_b else "跨账户"
                    logger.warning(f"被过滤: {pair_type}对敲, 用户: {pair.user_a}-{pair.user_b}, 风险: {pair.risk_level}")
        else:
            logger.info("所有检测到的对敲对都被保留")
        
        # 转换为字典格式
        pairs_data = []
        for pair in filtered_pairs:
            # 根据用户是否相同确定检测方法
            detection_method = 'same_account_wash_trading' if pair.user_a == pair.user_b else 'cross_account_wash_trading'
            
            pair_dict = {
                'pair_id': pair.pair_id,
                'user_a': pair.user_a,
                'user_b': pair.user_b,
                'contract_name': pair.contract_name,
                'detection_method': detection_method,  # 添加检测方法字段
                'open_time_a': pair.open_time_a.isoformat() if pair.open_time_a else None,
                'open_time_b': pair.open_time_b.isoformat() if pair.open_time_b else None,
                'close_time_a': pair.close_time_a.isoformat() if pair.close_time_a else None,
                'close_time_b': pair.close_time_b.isoformat() if pair.close_time_b else None,
                'position_id_a': pair.position_id_a,
                'position_id_b': pair.position_id_b,
                'amount_a': pair.amount_a,
                'amount_b': pair.amount_b,
                'side_a': pair.side_a,
                'side_b': pair.side_b,
                'risk_level': pair.risk_level,
                'risk_score': pair.risk_score
            }
            pairs_data.append(pair_dict)
        
        result = {
            'status': 'success',
            'total_pairs': len(filtered_pairs),
            'statistics': self.statistics,
            'trading_pairs': pairs_data,
            'summary': {
                'high_risk_count': len([p for p in filtered_pairs if p.risk_level == 'High']),
                'medium_risk_count': len([p for p in filtered_pairs if p.risk_level == 'Medium']),
                'same_account_count': len([p for p in filtered_pairs if p.user_a == p.user_b]),
                'cross_account_count': len([p for p in filtered_pairs if p.user_a != p.user_b])
            }
        }
        
        logger.info(f"检测完成，发现 {len(filtered_pairs)} 个高/中风险交易对")
        return result
    
    def _create_empty_result(self, message: str) -> dict:
        """创建空结果"""
        return {
            'status': 'failed',
            'message': message,
            'total_pairs': 0,
            'statistics': self.statistics,
            'trading_pairs': [],
            'summary': {
                'high_risk_count': 0,
                'medium_risk_count': 0,
                'same_account_count': 0,
                'cross_account_count': 0
            }
        }

    def _check_bd_relationship(self, user_a: str, user_b: str) -> bool:
        """检查两个用户是否属于同一个BD
        
        Returns:
            True: 同BD，应该过滤
            False: 不同BD，不过滤
        """
        # 这里需要实现BD关系检查逻辑
        # 暂时简化实现，实际需要查询BD关系数据
        
        # 如果BD关系缓存中没有数据，暂时返回False（不过滤）
        bd_a = self.bd_relationships.get(user_a)
        bd_b = self.bd_relationships.get(user_b)
        
        if bd_a is None or bd_b is None:
            return False
        
        return bd_a == bd_b  # 同BD返回True
    
    def _track_pair_frequency(self, user_a: str, user_b: str, timestamp: datetime):
        """跟踪用户对的对敲频率"""
        pair_key = tuple(sorted([user_a, user_b]))
        self.frequency_tracker[pair_key].append(timestamp)
    
    def _apply_frequency_filter(self):
        """应用频率过滤，移除低频率的对敲"""
        if not self.config['business_rules']['frequency_check']['enabled']:
            return
        
        min_count = self.config['business_rules']['frequency_check']['min_wash_count']
        time_window_hours = self.config['business_rules']['frequency_check']['time_window_hours']
        
        filtered_pairs = []
        
        for pair in self.detected_pairs:
            if pair.user_a == pair.user_b:  # 同账户对敲不受频率限制
                filtered_pairs.append(pair)
                continue
            
            pair_key = tuple(sorted([pair.user_a, pair.user_b]))
            timestamps = self.frequency_tracker.get(pair_key, [])
            
            # 计算时间窗口内的对敲次数
            window_start = pair.open_time_a - timedelta(hours=time_window_hours)
            window_end = pair.open_time_a + timedelta(hours=time_window_hours)
            
            count_in_window = sum(1 for ts in timestamps 
                                if window_start <= ts <= window_end)
            
            if count_in_window >= min_count:
                filtered_pairs.append(pair)
            else:
                self.statistics['filtered_by_frequency'] += 1
        
        original_count = len(self.detected_pairs)
        self.detected_pairs = filtered_pairs
        filtered_count = original_count - len(filtered_pairs)
        
        if filtered_count > 0:
            logger.info(f"频率过滤移除了 {filtered_count} 个低频对敲")

# 为了保持兼容性，保留旧的类名作为别名
WashTradingDetector = NextGenWashTradingDetector 