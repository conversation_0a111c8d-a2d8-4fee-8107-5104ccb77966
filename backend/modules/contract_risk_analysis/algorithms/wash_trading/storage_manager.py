import logging
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class WashTradingStorageManager:
    """
    对敲交易存储管理器
    负责管理数据库表结构和数据存储
    """
    
    def __init__(self):
        """初始化存储管理器"""
        pass
    
    def create_detail_table_if_not_exists(self, db_connection) -> bool:
        """
        创建wash_trading_pairs表（如果不存在）
        
        参数:
            db_connection: 数据库连接
            
        返回:
            是否创建成功
        """
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS wash_trading_pairs (
                id BIGINT AUTO_INCREMENT PRIMARY KEY,
                result_id BIGINT NOT NULL,
                pair_index INT NOT NULL,
                
                -- 交易对基本信息
                user_a_id VARCHAR(50) NOT NULL,
                user_b_id VARCHAR(50) NOT NULL, 
                contract_name VARCHAR(50) NOT NULL,
                
                -- 风险等级和评分 (新增字段)
                risk_level VARCHAR(20) DEFAULT 'Medium',
                risk_score DECIMAL(3,2) DEFAULT 0.50,
                
                -- 用户A的position信息
                user_a_position_id VARCHAR(100),
                user_a_open_time DATETIME,
                user_a_open_side TINYINT,
                user_a_open_amount DECIMAL(15,2),
                user_a_close_time DATETIME,
                user_a_close_side TINYINT,
                user_a_close_amount DECIMAL(15,2),
                user_a_profit DECIMAL(15,2),
                
                -- 用户B的position信息
                user_b_position_id VARCHAR(100),
                user_b_open_time DATETIME,
                user_b_open_side TINYINT,
                user_b_open_amount DECIMAL(15,2),
                user_b_close_time DATETIME,
                user_b_close_side TINYINT,
                user_b_close_amount DECIMAL(15,2),
                user_b_profit DECIMAL(15,2),
                
                -- 时间差和汇总
                open_time_gap_seconds INT,
                close_time_gap_seconds INT,
                total_amount DECIMAL(15,2),
                net_profit DECIMAL(15,2),
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_result_id (result_id),
                INDEX idx_users (user_a_id, user_b_id),
                INDEX idx_contract (contract_name),
                INDEX idx_risk_level (risk_level),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """
            
            db_connection.execute(create_table_sql)
            logger.info("wash_trading_pairs表检查/创建完成")
            return True
            
        except Exception as e:
            logger.error(f"创建wash_trading_pairs表失败: {str(e)}")
            return False
    
    def save_trade_pairs(self, result_id: int, trade_pairs: List[Dict], db_connection) -> bool:
        """
        保存交易对详情到数据库
        
        参数:
            result_id: 主结果ID
            trade_pairs: 交易对详情列表
            db_connection: 数据库连接
            
        返回:
            是否保存成功
        """
        if not trade_pairs:
            logger.debug("无交易对详情需要保存")
            return True
            
        try:
            # 确保表存在
            if not self.create_detail_table_if_not_exists(db_connection):
                logger.error("无法创建详情表，保存失败")
                return False
            
            # 构建插入SQL
            insert_sql = """
            INSERT INTO wash_trading_pairs (
                result_id, pair_index, user_a_id, user_b_id, contract_name,
                risk_level, risk_score,
                user_a_position_id, user_a_open_time, user_a_open_side, user_a_open_amount,
                user_a_close_time, user_a_close_side, user_a_close_amount, user_a_profit,
                user_b_position_id, user_b_open_time, user_b_open_side, user_b_open_amount,
                user_b_close_time, user_b_close_side, user_b_close_amount, user_b_profit,
                open_time_gap_seconds, close_time_gap_seconds, total_amount, net_profit
            ) VALUES (
                %s, %s, %s, %s, %s,
                %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s
            )
            """
            
            # 批量插入
            rows_data = []
            for pair in trade_pairs:
                user_a = pair['user_a']
                user_b = pair['user_b']
                time_gaps = pair['time_gaps']
                
                row = (
                    result_id,
                    pair.get('pair_index', 0),
                    pair.get('user_a', user_a.get('member_id', '')),
                    pair.get('user_b', user_b.get('member_id', '')),
                    pair.get('contract_name', ''),
                    
                    # 风险等级和评分 (新增)
                    pair.get('risk_level', 'Medium'),
                    pair.get('risk_score', 0.5),
                    
                    # 用户A信息
                    pair.get('position_id_a', user_a.get('position_id')),
                    pair.get('open_time_a', user_a.get('open_time')),
                    1 if pair.get('side_a') == 'buy' else 0,  # 转换为数字
                    pair.get('amount_a', user_a.get('open_amount', 0)),
                    pair.get('close_time_a', user_a.get('close_time')),
                    0 if pair.get('side_a') == 'buy' else 1,  # 平仓方向相反
                    pair.get('amount_a', user_a.get('close_amount', 0)),
                    user_a.get('profit', 0),
                    
                    # 用户B信息
                    pair.get('position_id_b', user_b.get('position_id')),
                    pair.get('open_time_b', user_b.get('open_time')),
                    1 if pair.get('side_b') == 'buy' else 0,  # 转换为数字
                    pair.get('amount_b', user_b.get('open_amount', 0)),
                    pair.get('close_time_b', user_b.get('close_time')),
                    0 if pair.get('side_b') == 'buy' else 1,  # 平仓方向相反
                    pair.get('amount_b', user_b.get('close_amount', 0)),
                    user_b.get('profit', 0),
                    
                    # 时间差和汇总
                    time_gaps.get('open_gap_seconds', 0) if time_gaps else 0,
                    time_gaps.get('close_gap_seconds', 0) if time_gaps else 0,
                    pair.get('amount_a', 0) + pair.get('amount_b', 0),
                    pair.get('net_profit', 0)
                )
                rows_data.append(row)
            
            # 批量执行插入
            if hasattr(db_connection, 'executemany'):
                db_connection.executemany(insert_sql, rows_data)
            else:
                # 如果不支持批量插入，逐条插入
                for row in rows_data:
                    db_connection.execute(insert_sql, row)
            
            logger.info(f"成功保存 {len(trade_pairs)} 个交易对详情到数据库")
            return True
            
        except Exception as e:
            logger.error(f"保存交易对详情失败: {str(e)}")
            return False
    
    def get_trade_pairs_by_result_id(self, result_id: int, db_connection) -> List[Dict]:
        """
        根据结果ID获取交易对详情
        
        参数:
            result_id: 主结果ID
            db_connection: 数据库连接
            
        返回:
            交易对详情列表
        """
        try:
            query_sql = """
            SELECT * FROM wash_trading_pairs 
            WHERE result_id = %s 
            ORDER BY pair_index
            """
            
            cursor = db_connection.execute(query_sql, (result_id,))
            rows = cursor.fetchall()
            
            trade_pairs = []
            for row in rows:
                pair_detail = self._format_row_to_dict(row)
                trade_pairs.append(pair_detail)
            
            logger.debug(f"获取到 {len(trade_pairs)} 个交易对详情")
            return trade_pairs
            
        except Exception as e:
            logger.error(f"获取交易对详情失败: {str(e)}")
            return []
    
    def delete_trade_pairs_by_result_id(self, result_id: int, db_connection) -> bool:
        """
        删除指定结果ID的交易对详情
        
        参数:
            result_id: 主结果ID
            db_connection: 数据库连接
            
        返回:
            是否删除成功
        """
        try:
            delete_sql = "DELETE FROM wash_trading_pairs WHERE result_id = %s"
            cursor = db_connection.execute(delete_sql, (result_id,))
            deleted_count = cursor.rowcount
            
            logger.info(f"删除了 {deleted_count} 个交易对详情记录")
            return True
            
        except Exception as e:
            logger.error(f"删除交易对详情失败: {str(e)}")
            return False
    
    def _format_row_to_dict(self, row) -> Dict:
        """
        将数据库行转换为字典格式
        
        参数:
            row: 数据库行数据
            
        返回:
            格式化的字典
        """
        try:
            # 根据数据库行的结构构建字典
            # 这里假设row是一个包含列名的对象或元组
            if hasattr(row, '_asdict'):
                # namedtuple格式
                row_dict = row._asdict()
            elif hasattr(row, 'keys'):
                # 字典格式
                row_dict = dict(row)
            else:
                # 需要手动映射列名
                logger.warning("无法自动解析数据库行格式，返回空字典")
                return {}
            
            # 构建标准格式的交易对详情
            pair_detail = {
                'pair_index': row_dict.get('pair_index', 0),
                'contract_name': row_dict.get('contract_name', ''),
                'user_a': {
                    'member_id': row_dict.get('user_a_id'),
                    'position_id': row_dict.get('user_a_position_id'),
                    'open_time': row_dict.get('user_a_open_time'),
                    'open_side': row_dict.get('user_a_open_side'),
                    'open_amount': float(row_dict.get('user_a_open_amount', 0)),
                    'close_time': row_dict.get('user_a_close_time'),
                    'close_side': row_dict.get('user_a_close_side'),
                    'close_amount': float(row_dict.get('user_a_close_amount', 0)),
                    'profit': float(row_dict.get('user_a_profit', 0))
                },
                'user_b': {
                    'member_id': row_dict.get('user_b_id'),
                    'position_id': row_dict.get('user_b_position_id'),
                    'open_time': row_dict.get('user_b_open_time'),
                    'open_side': row_dict.get('user_b_open_side'),
                    'open_amount': float(row_dict.get('user_b_open_amount', 0)),
                    'close_time': row_dict.get('user_b_close_time'),
                    'close_side': row_dict.get('user_b_close_side'),
                    'close_amount': float(row_dict.get('user_b_close_amount', 0)),
                    'profit': float(row_dict.get('user_b_profit', 0))
                },
                'time_gaps': {
                    'open_gap_seconds': row_dict.get('open_time_gap_seconds', 0),
                    'close_gap_seconds': row_dict.get('close_time_gap_seconds', 0)
                },
                'total_amount': float(row_dict.get('total_amount', 0)),
                'net_profit': float(row_dict.get('net_profit', 0)),
                'created_at': row_dict.get('created_at')
            }
            
            return pair_detail
            
        except Exception as e:
            logger.error(f"格式化数据库行失败: {str(e)}")
            return {}
    
    def get_statistics_by_contract(self, contract_name: str, db_connection, 
                                 days: int = 30) -> Dict:
        """
        获取指定合约的对敲统计信息
        
        参数:
            contract_name: 合约名称
            db_connection: 数据库连接
            days: 统计天数
            
        返回:
            统计信息字典
        """
        try:
            stats_sql = """
            SELECT 
                COUNT(*) as total_pairs,
                COUNT(DISTINCT user_a_id) + COUNT(DISTINCT user_b_id) as unique_users,
                SUM(total_amount) as total_amount,
                SUM(net_profit) as total_net_profit,
                AVG(open_time_gap_seconds) as avg_open_gap,
                AVG(close_time_gap_seconds) as avg_close_gap
            FROM wash_trading_pairs 
            WHERE contract_name = %s 
            AND created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            """
            
            cursor = db_connection.execute(stats_sql, (contract_name, days))
            row = cursor.fetchone()
            
            if row:
                stats = {
                    'contract_name': contract_name,
                    'total_pairs': row[0] or 0,
                    'unique_users': row[1] or 0,
                    'total_amount': float(row[2] or 0),
                    'total_net_profit': float(row[3] or 0),
                    'avg_open_gap': float(row[4] or 0),
                    'avg_close_gap': float(row[5] or 0),
                    'days': days
                }
            else:
                stats = {
                    'contract_name': contract_name,
                    'total_pairs': 0,
                    'unique_users': 0,
                    'total_amount': 0,
                    'total_net_profit': 0,
                    'avg_open_gap': 0,
                    'avg_close_gap': 0,
                    'days': days
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取合约统计信息失败: {str(e)}")
            return {
                'contract_name': contract_name,
                'error': str(e),
                'days': days
            } 