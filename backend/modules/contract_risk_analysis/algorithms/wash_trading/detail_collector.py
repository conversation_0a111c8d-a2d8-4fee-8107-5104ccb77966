import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def safe_timestamp_to_datetime(timestamp):
    """
    安全地将timestamp转换为datetime对象
    支持处理字符串ISO格式、pandas Timestamp、datetime对象
    """
    if timestamp is None:
        return None
    
    if isinstance(timestamp, str):
        try:
            # 尝试解析ISO格式字符串
            return pd.to_datetime(timestamp)
        except:
            return None
    elif hasattr(timestamp, 'to_pydatetime'):
        # pandas Timestamp
        return timestamp.to_pydatetime()
    elif isinstance(timestamp, datetime):
        # 已经是datetime对象
        return timestamp
    else:
        # 尝试转换
        try:
            return pd.to_datetime(timestamp)
        except:
            return None

def safe_time_diff(time1, time2):
    """
    安全地计算两个时间的差值（秒）
    """
    try:
        dt1 = safe_timestamp_to_datetime(time1)
        dt2 = safe_timestamp_to_datetime(time2)
        
        if dt1 is None or dt2 is None:
            return 0
        
        return abs((dt1 - dt2).total_seconds())
    except Exception as e:
        logger.debug(f"计算时间差失败: {e}")
        return 0

def safe_isoformat(timestamp):
    """
    安全地将时间戳转换为ISO格式字符串
    支持处理字符串、datetime、pandas Timestamp等各种类型
    """
    if timestamp is None:
        return None
    
    if isinstance(timestamp, str):
        # 如果已经是字符串，假设它已经是正确的ISO格式
        return timestamp
    
    # 尝试转换为datetime然后格式化
    dt = safe_timestamp_to_datetime(timestamp)
    if dt is not None:
        return dt.isoformat()
    else:
        return str(timestamp) if timestamp is not None else None

class WashTradingDetailCollector:
    """
    对敲交易详细信息收集器
    负责收集和整理对敲交易的详细信息，包括交易对、时间差、利润等
    """
    
    def __init__(self):
        """初始化详细信息收集器"""
        pass
    
    def collect_cross_bd_details(self, validated_patterns: List[Dict], user_id: str, 
                               contract: str, full_df: pd.DataFrame) -> List[Dict]:
        """
        收集跨BD对敲的详细交易对信息（优化版）
        
        优化要点：
        1. 批量获取position详情，避免重复查询
        2. 预先收集所有position_id，一次性处理
        3. 使用缓存机制提升性能
        
        参数:
            validated_patterns: 验证通过的对敲模式列表
            user_id: 用户ID
            contract: 合约名称
            full_df: 完整交易数据
            
        返回:
            详细交易对信息列表
        """
        trade_pairs = []
        
        try:
            if not validated_patterns:
                return trade_pairs
            
            # 1. 预先收集所有position_id（批量处理关键）
            all_position_ids = []
            for pattern in validated_patterns:
                open1, open2 = pattern['open_pair']
                if open1.get('position_id'):
                    all_position_ids.append(open1['position_id'])
                if open2.get('position_id'):
                    all_position_ids.append(open2['position_id'])
            
            # 2. 批量获取所有position详情（性能优化核心）
            position_details_map = self.batch_get_position_details(all_position_ids, full_df)
            logger.debug(f"批量获取了 {len(position_details_map)} 个position的详情")
            
            # 3. 构建交易对详情
            for idx, pattern in enumerate(validated_patterns):
                # 获取开仓交易对
                open1, open2 = pattern['open_pair']
                
                # 从批量结果中获取position详情（避免重复查询）
                pos1_details = position_details_map.get(open1.get('position_id'), {
                    'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0
                })
                pos2_details = position_details_map.get(open2.get('position_id'), {
                    'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0
                })
                
                # 计算时间差
                open_time_gap = safe_time_diff(open2['timestamp'], open1['timestamp'])
                close_time_gap = self._calculate_close_time_gap(pos1_details, pos2_details)
                
                # 构建交易对详情
                pair_detail = {
                    'pair_index': idx + 1,
                    'user_a': {
                        'member_id': open1['member_id'],
                        'position_id': open1.get('position_id'),
                        'open_time': safe_isoformat(open1['timestamp']) if pd.notna(open1['timestamp']) else None,
                        'open_side': int(open1['side']) if pd.notna(open1['side']) else None,
                        'open_amount': float(open1.get('deal_vol_usdt', 0)),
                        'close_time': safe_isoformat(pos1_details.get('close_time')) if pos1_details.get('close_time') and pd.notna(pos1_details.get('close_time')) else None,
                        'close_side': int(pos1_details.get('close_side')) if pos1_details.get('close_side') and pd.notna(pos1_details.get('close_side')) else None,
                        'close_amount': float(pos1_details.get('close_amount', 0)),
                        'profit': float(pos1_details.get('profit', 0))
                    },
                    'user_b': {
                        'member_id': open2['member_id'],
                        'position_id': open2.get('position_id'),
                        'open_time': safe_isoformat(open2['timestamp']) if pd.notna(open2['timestamp']) else None,
                        'open_side': int(open2['side']) if pd.notna(open2['side']) else None,
                        'open_amount': float(open2.get('deal_vol_usdt', 0)),
                        'close_time': safe_isoformat(pos2_details.get('close_time')) if pos2_details.get('close_time') and pd.notna(pos2_details.get('close_time')) else None,
                        'close_side': int(pos2_details.get('close_side')) if pos2_details.get('close_side') and pd.notna(pos2_details.get('close_side')) else None,
                        'close_amount': float(pos2_details.get('close_amount', 0)),
                        'profit': float(pos2_details.get('profit', 0))
                    },
                    'time_gaps': {
                        'open_gap_seconds': int(open_time_gap),
                        'close_gap_seconds': int(close_time_gap)
                    },
                    'total_amount': float(open1.get('deal_vol_usdt', 0) + open2.get('deal_vol_usdt', 0)),
                    'net_profit': float(pos1_details.get('profit', 0) + pos2_details.get('profit', 0))
                }
                
                trade_pairs.append(pair_detail)
            
            logger.debug(f"跨BD详情收集完成: {len(trade_pairs)} 个交易对")
                
        except Exception as e:
            logger.error(f"收集跨BD对敲详情时出错: {str(e)}")
            
        return trade_pairs
    
    def collect_same_account_details(self, validated_patterns: List[Dict], user_id: str, 
                                   contract: str, full_df: pd.DataFrame) -> List[Dict]:
        """
        收集同账户对敲的详细交易对信息（优化版）
        
        优化要点：
        1. 批量获取position详情，避免重复查询
        2. 预先收集所有position_id，一次性处理
        3. 使用缓存机制提升性能
        
        参数:
            validated_patterns: 验证通过的对敲模式列表
            user_id: 用户ID
            contract: 合约名称
            full_df: 完整交易数据
            
        返回:
            详细交易对信息列表
        """
        trade_pairs = []
        
        try:
            if not validated_patterns:
                return trade_pairs
            
            # 1. 预先收集所有position_id（批量处理关键）
            all_position_ids = []
            for pattern in validated_patterns:
                # 根据不同的数据结构获取开仓信息
                if 'open_pair' in pattern:
                    # 新算法结构
                    open1, open2 = pattern['open_pair']
                else:
                    # 传统算法结构
                    open1 = pattern['long_open']
                    open2 = pattern['short_open']
                
                if open1.get('position_id'):
                    all_position_ids.append(open1['position_id'])
                if open2.get('position_id'):
                    all_position_ids.append(open2['position_id'])
            
            # 2. 批量获取所有position详情（性能优化核心）
            position_details_map = self.batch_get_position_details(all_position_ids, full_df)
            logger.debug(f"批量获取了 {len(position_details_map)} 个position的详情")
            
            # 3. 构建交易对详情
            for idx, pattern in enumerate(validated_patterns):
                # 根据不同的数据结构获取开仓信息
                if 'open_pair' in pattern:
                    # 新算法结构
                    open1, open2 = pattern['open_pair']
                else:
                    # 传统算法结构
                    open1 = pattern['long_open']
                    open2 = pattern['short_open']
                
                # 从批量结果中获取position详情（避免重复查询）
                pos1_details = position_details_map.get(open1.get('position_id'), {
                    'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0
                })
                pos2_details = position_details_map.get(open2.get('position_id'), {
                    'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0
                })
                
                # 计算时间差
                open_time_gap = pattern.get('open_time_diff', 0)
                if isinstance(open_time_gap, (int, float)):
                    open_time_gap = float(open_time_gap)
                else:
                    open_time_gap = safe_time_diff(open2['timestamp'], open1['timestamp'])
                
                close_time_gap = self._calculate_close_time_gap(pos1_details, pos2_details)
                
                # 构建交易对详情（同账户对敲，用户ID相同）
                pair_detail = {
                    'pair_index': idx + 1,
                    'user_a': {
                        'member_id': user_id,
                        'position_id': open1.get('position_id'),
                        'open_time': safe_isoformat(open1['timestamp']) if pd.notna(open1['timestamp']) else None,
                        'open_side': int(open1['side']) if pd.notna(open1['side']) else None,
                        'open_amount': float(open1.get('deal_vol_usdt', 0)),
                        'close_time': safe_isoformat(pos1_details.get('close_time')) if pos1_details.get('close_time') and pd.notna(pos1_details.get('close_time')) else None,
                        'close_side': int(pos1_details.get('close_side')) if pos1_details.get('close_side') and pd.notna(pos1_details.get('close_side')) else None,
                        'close_amount': float(pos1_details.get('close_amount', 0)),
                        'profit': float(pos1_details.get('profit', 0))
                    },
                    'user_b': {
                        'member_id': user_id,  # 同账户
                        'position_id': open2.get('position_id'),
                        'open_time': safe_isoformat(open2['timestamp']) if pd.notna(open2['timestamp']) else None,
                        'open_side': int(open2['side']) if pd.notna(open2['side']) else None,
                        'open_amount': float(open2.get('deal_vol_usdt', 0)),
                        'close_time': safe_isoformat(pos2_details.get('close_time')) if pos2_details.get('close_time') and pd.notna(pos2_details.get('close_time')) else None,
                        'close_side': int(pos2_details.get('close_side')) if pos2_details.get('close_side') and pd.notna(pos2_details.get('close_side')) else None,
                        'close_amount': float(pos2_details.get('close_amount', 0)),
                        'profit': float(pos2_details.get('profit', 0))
                    },
                    'time_gaps': {
                        'open_gap_seconds': int(open_time_gap),
                        'close_gap_seconds': int(close_time_gap)
                    },
                    'total_amount': float(open1.get('deal_vol_usdt', 0) + open2.get('deal_vol_usdt', 0)),
                    'net_profit': float(pos1_details.get('profit', 0) + pos2_details.get('profit', 0))
                }
                
                trade_pairs.append(pair_detail)
            
            logger.debug(f"同账户详情收集完成: {len(trade_pairs)} 个交易对")
                
        except Exception as e:
            logger.error(f"收集同账户对敲详情时出错: {str(e)}")
            
        return trade_pairs
    
    def _get_position_details(self, position_id: str, full_df: pd.DataFrame) -> Dict:
        """
        获取position的详细信息，包括平仓数据和利润（优化版）
        
        优化要点：
        1. 添加缓存机制，避免重复查询
        2. 批量处理position数据
        3. 减少DataFrame操作次数
        
        参数:
            position_id: position ID
            full_df: 完整交易数据
            
        返回:
            position详细信息字典
        """
        if not position_id:
            return {'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0}
        
        # 检查缓存
        if hasattr(self, '_position_details_cache') and position_id in self._position_details_cache:
            return self._position_details_cache[position_id]
        
        try:
            # 获取position的所有交易
            position_trades = full_df[full_df['position_id'] == position_id]
            
            if position_trades.empty:
                result = {'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0}
            else:
                # 计算总利润
                total_profit = 0
                if 'profit' in position_trades.columns:
                    total_profit = float(position_trades['profit'].sum())
                
                # 获取平仓信息
                close_trades = position_trades[position_trades['side'].isin([2, 4])]  # 2=平空, 4=平多
                
                close_info = {
                    'profit': total_profit,
                    'close_time': None,
                    'close_side': None,
                    'close_amount': 0
                }
                
                if not close_trades.empty:
                    # 获取最后一次平仓
                    last_close = close_trades.sort_values('timestamp').iloc[-1]
                    close_info.update({
                        'close_time': last_close['timestamp'],
                        'close_side': last_close['side'],
                        'close_amount': float(last_close.get('deal_vol_usdt', 0))
                    })
                
                result = close_info
            
            # 缓存结果
            if not hasattr(self, '_position_details_cache'):
                self._position_details_cache = {}
            self._position_details_cache[position_id] = result
            
            return result
            
        except Exception as e:
            logger.error(f"获取position {position_id} 详情时出错: {str(e)}")
            return {'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0}
    
    def batch_get_position_details(self, position_ids: List[str], full_df: pd.DataFrame) -> Dict[str, Dict]:
        """
        批量获取position详细信息（新增优化方法）
        
        参数:
            position_ids: position ID列表
            full_df: 完整交易数据
            
        返回:
            position详细信息字典 {position_id: details}
        """
        if not position_ids:
            return {}
        
        try:
            logger.debug(f"🔍 开始批量获取 {len(position_ids)} 个position的详情")
            
            # 初始化缓存
            if not hasattr(self, '_position_details_cache'):
                self._position_details_cache = {}
            
            results = {}
            uncached_ids = []
            
            # 检查缓存
            for pos_id in position_ids:
                if pos_id in self._position_details_cache:
                    results[pos_id] = self._position_details_cache[pos_id]
                else:
                    uncached_ids.append(pos_id)
            
            cache_hit_count = len(position_ids) - len(uncached_ids)
            if cache_hit_count > 0:
                logger.debug(f"💾 缓存命中: {cache_hit_count}/{len(position_ids)} 个position")
            
            # 批量处理未缓存的position
            if uncached_ids:
                logger.debug(f"🔄 需要查询 {len(uncached_ids)} 个新position的详情")
                
                # 一次性获取所有相关position的数据
                all_position_trades = full_df[full_df['position_id'].isin(uncached_ids)]
                logger.debug(f"📊 获取到 {len(all_position_trades)} 条position相关交易数据")
                
                processed_count = 0
                for pos_id in uncached_ids:
                    position_trades = all_position_trades[all_position_trades['position_id'] == pos_id]
                    
                    if position_trades.empty:
                        detail = {'profit': 0, 'close_time': None, 'close_side': None, 'close_amount': 0}
                    else:
                        # 计算总利润
                        total_profit = 0
                        if 'profit' in position_trades.columns:
                            total_profit = float(position_trades['profit'].sum())
                        
                        # 获取平仓信息
                        close_trades = position_trades[position_trades['side'].isin([2, 4])]
                        
                        detail = {
                            'profit': total_profit,
                            'close_time': None,
                            'close_side': None,
                            'close_amount': 0
                        }
                        
                        if not close_trades.empty:
                            last_close = close_trades.sort_values('timestamp').iloc[-1]
                            detail.update({
                                'close_time': last_close['timestamp'],
                                'close_side': last_close['side'],
                                'close_amount': float(last_close.get('deal_vol_usdt', 0))
                            })
                    
                    # 缓存和存储结果
                    self._position_details_cache[pos_id] = detail
                    results[pos_id] = detail
                    processed_count += 1
                    
                    # 每处理50个position输出一次进度
                    if processed_count % 50 == 0:
                        logger.debug(f"📈 批量处理进度: {processed_count}/{len(uncached_ids)} 个position")
                
                logger.debug(f"✅ 批量处理完成: {processed_count} 个新position")
            
            logger.debug(f"🎉 批量获取完成: 总计 {len(results)} 个position详情 (缓存命中: {cache_hit_count}, 新查询: {len(uncached_ids)})")
            return results
            
        except Exception as e:
            logger.error(f"❌ 批量获取position详情失败: {str(e)}")
            # 回退到单个获取
            logger.warning(f"🔄 回退到单个获取模式")
            return {pos_id: self._get_position_details(pos_id, full_df) for pos_id in position_ids}
    
    def clear_cache(self):
        """清理缓存"""
        if hasattr(self, '_position_details_cache'):
            self._position_details_cache.clear()
            logger.debug("详情收集器缓存已清理")
    
    def _calculate_close_time_gap(self, pos1_details: Dict, pos2_details: Dict) -> float:
        """
        计算两个position的平仓时间差
        
        参数:
            pos1_details: 第一个position详情
            pos2_details: 第二个position详情
            
        返回:
            时间差（秒）
        """
        try:
            close_time1 = pos1_details.get('close_time')
            close_time2 = pos2_details.get('close_time')
            
            if close_time1 is not None and close_time2 is not None:
                return safe_time_diff(close_time1, close_time2)
            else:
                return 0  # 未完全平仓
                
        except Exception as e:
            logger.debug(f"计算平仓时间差时出错: {str(e)}")
            return 0
    
    def calculate_summary_stats(self, trade_pairs: List[Dict]) -> Dict:
        """
        计算交易对的汇总统计信息
        
        参数:
            trade_pairs: 交易对详情列表
            
        返回:
            汇总统计信息
        """
        if not trade_pairs:
            return {
                'total_pairs': 0,
                'total_amount': 0,
                'net_profit': 0,
                'avg_open_gap': 0,
                'avg_close_gap': 0,
                'profit_pairs': 0,
                'loss_pairs': 0
            }
        
        try:
            total_amount = sum(pair['total_amount'] for pair in trade_pairs)
            net_profit = sum(pair['net_profit'] for pair in trade_pairs)
            
            open_gaps = [pair['time_gaps']['open_gap_seconds'] for pair in trade_pairs]
            close_gaps = [pair['time_gaps']['close_gap_seconds'] for pair in trade_pairs if pair['time_gaps']['close_gap_seconds'] > 0]
            
            profit_pairs = len([pair for pair in trade_pairs if pair['net_profit'] > 0])
            loss_pairs = len([pair for pair in trade_pairs if pair['net_profit'] < 0])
            
            return {
                'total_pairs': len(trade_pairs),
                'total_amount': total_amount,
                'net_profit': net_profit,
                'avg_open_gap': np.mean(open_gaps) if open_gaps else 0,
                'avg_close_gap': np.mean(close_gaps) if close_gaps else 0,
                'profit_pairs': profit_pairs,
                'loss_pairs': loss_pairs
            }
            
        except Exception as e:
            logger.error(f"计算汇总统计时出错: {str(e)}")
            return {
                'total_pairs': len(trade_pairs),
                'total_amount': 0,
                'net_profit': 0,
                'avg_open_gap': 0,
                'avg_close_gap': 0,
                'profit_pairs': 0,
                'loss_pairs': 0
            } 