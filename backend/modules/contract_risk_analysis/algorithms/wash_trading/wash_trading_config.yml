# 对敲检测算法配置文件
# Wash Trading Detection Algorithm Configuration

# ==================== 通用检测配置 ====================
detection:
  # 最小数据量要求
  min_records: 10
  
  # 需要的字段列表 (修正为实际使用的字段名)
  required_fields:
    - member_id
    - digital_id  
    - contract_name
    - side          # 数字类型：1=开多, 2=平空, 3=开空, 4=平多
    - price
    - volume        # 交易数量
    - deal_vol_usdt # 交易金额
    - timestamp
    - position_id

# ==================== 时间阈值配置 ====================
time_thresholds:
  # 开仓时间匹配窗口 (秒) - 从10秒缩小到3秒
  open_time_window: 15
  
  # 风险分级的平仓时间阈值
  risk_classification:
    # 高风险：平仓间隔小于此值(分钟)
    high_risk_close_window_minutes: 1.5
    # 中风险：平仓间隔小于此值(小时) 
    medium_risk_close_window_hours: 0.1

# ==================== 金额匹配配置 ====================
amount_matching:
  # 金额匹配的相对容差 (百分比) - 从5%降到2%
  relative_tolerance: 0.5  # 2%
  
  # 金额匹配的绝对容差 (最小单位)
  absolute_tolerance: 100

# ==================== 同账户对敲配置 ====================
same_account:
  # 是否启用同账户检测
  enabled: true
  
  # 同时持有多空仓位的最大允许时间重叠(分钟) - 暂未实现重叠检测
  # max_overlap_minutes: 5

# ==================== 跨账户对敲配置 ====================
cross_account:
  # 是否启用跨账户检测
  enabled: true
  
  # 跨账户时间窗口 (秒) - 从10秒缩小到3秒
  time_window: 30
  
  # 是否检查BD关系 - 启用BD关系检查
  check_bd_relationship: true

# ==================== 网络分析配置 ====================
network_analysis:
  # 是否启用网络分析
  enabled: true
  
  # 最大网络节点数限制 - 暂未实现详细网络分析
  # max_nodes: 5000
  
  # 最小团伙规模 - 暂未实现
  # min_cluster_size: 3
  
  # 网络分析算法 - 暂未实现
  # algorithm: "louvain"  # 可选: louvain, leiden, label_propagation

# ==================== 风险评分配置 - 暂未实现复合评分系统 ====================
# risk_scoring:
#   # 各因素权重 (总和应为1.0)
#   weights:
#     time_proximity: 0.3      # 时间接近程度
#     amount_similarity: 0.25  # 金额相似程度  
#     frequency: 0.2           # 对敲频率
#     close_time_gap: 0.25     # 平仓时间差
# 
#   # 风险等级阈值
#   thresholds:
#     high_risk: 0.8
#     medium_risk: 0.5

# ==================== 性能优化配置 - 暂未实现 ====================
# performance:
#   # 并行处理配置
#   parallel_processing:
#     enabled: true
#     max_workers: 4
#   
#   # 内存管理
#   memory_management:
#     # 分批处理大小
#     batch_size: 10000
#     # 是否使用低内存模式
#     low_memory_mode: false

# ==================== 数据存储配置 ====================
storage:
  # 只存储的风险等级 - 🔧 添加低风险以确保不丢失对敲对
  save_risk_levels: ["High", "Medium", "Low"]
  
  # 是否保存详细交易对 - 暂未实现可选保存
  # save_trading_pairs: true
  
  # 是否保存网络分析结果 - 暂未实现
  # save_network_analysis: true

# ==================== 日志与监控配置 - 暂未实现 ====================
# logging:
#   # 日志级别: DEBUG, INFO, WARNING, ERROR
#   level: "INFO"
#   
#   # 是否记录性能统计
#   performance_stats: true
#   
#   # 是否记录详细处理日志
#   detailed_processing: false

# ==================== 验证与质量控制 ====================
validation:
  # 是否验证position_id存在
  require_position_id: true
  
  # 是否验证数据完整性 - 暂未实现详细验证
  # check_data_integrity: true
  
  # 允许的最大数据缺失率 - 暂未实现
  # max_missing_rate: 0.1

# ==================== 业务规则配置 ====================
business_rules:
  # 排除的合约类型 - 暂未实现
  # excluded_contracts: []
  
  # 排除的用户类型 - 暂未实现
  # excluded_user_types: []
  
  # 最小交易金额阈值 (过小的交易不参与检测)
  min_trade_amount: 10.0
  
  # 最大交易金额阈值 (0表示无限制) - 暂未实现
  # max_trade_amount: 0
  
  # 频率检查配置
  frequency_check:
    # 是否启用频率检查
    enabled: true
    # 同一用户对在指定时间内的最小对敲次数才认定为异常
    min_wash_count: 2
    # 检查的时间窗口 (小时)
    time_window_hours: 24 