"""
合约风险分析模块API
"""
from flask import Blueprint, request, jsonify
import pandas as pd
import os
import uuid
import tempfile
import json
import time
import logging
from datetime import datetime
from threading import Lock
from werkzeug.utils import secure_filename
import numpy as np

from ..services.contract_analyzer import CTContractAnalyzer
from database.repositories.task_repository import task_repository
from database.repositories.contract_risk_repository import contract_risk_repository
# ========== 新存储相关导入 ==========
from database.algorithm_storage_manager import AlgorithmStorageManager
# ========== 鉴权导入 ==========
from core.utils.decorators import login_required, admin_required


# 配置日志
logger = logging.getLogger(__name__)

# 全局进度存储和锁
PROGRESS_STORE = {}
PROGRESS_LOCK = Lock()

def update_progress(task_id, progress_data):
    """更新任务进度"""
    with PROGRESS_LOCK:
        PROGRESS_STORE[task_id] = {
            'timestamp': time.time(),
            'data': progress_data
        }

def get_progress(task_id):
    """获取任务进度"""
    with PROGRESS_LOCK:
        return PROGRESS_STORE.get(task_id, {}).get('data', {})

contract_bp = Blueprint('contract', __name__)

# 允许的文件类型
ALLOWED_EXTENSIONS = {'xlsx', 'xls', 'csv'}

def allowed_file(filename):
    """检查文件是否为允许的类型"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def json_serializable(obj):
    """
    将对象转换为JSON可序列化的格式
    处理pandas Timestamp、datetime等特殊类型
    🚀 修复：处理 Infinity 和 NaN 值
    """
    import math

    # 🚀 新增：处理 Infinity 和 NaN 值
    if isinstance(obj, (int, float)):
        if math.isinf(obj):
            return 999.99 if obj > 0 else -999.99  # 用大数值替代无穷大
        elif math.isnan(obj):
            return None  # NaN 转为 null

    if hasattr(obj, 'isoformat'):  # datetime, Timestamp等
        return obj.isoformat()
    elif hasattr(obj, 'item'):  # numpy数值类型
        item_value = obj.item()
        # 递归处理 numpy 数值
        return json_serializable(item_value)
    elif isinstance(obj, set):  # set类型转换为列表
        return [json_serializable(item) for item in obj]
    elif isinstance(obj, frozenset):  # frozenset类型转换为列表
        return [json_serializable(item) for item in obj]
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    elif pd.isna(obj):  # pandas NaN值
        return None
    else:
        return obj

def clean_results_for_json(results):
    """
    清理检测结果，确保可以JSON序列化
    """
    cleaned_results = []
    for result in results:
        cleaned_result = json_serializable(result)
        cleaned_results.append(cleaned_result)
    return cleaned_results

@contract_bp.route('/upload', methods=['POST'])
@admin_required  # 只有管理员可以上传文件
def upload_contract_data():
    """上传合约数据进行风险分析"""
    if 'file' not in request.files:
        return jsonify({'error': '未检测到文件'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '未选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '仅支持xlsx、xls、csv文件'}), 400
    
    # 检查是否使用异步模式
    use_async = request.form.get('async', 'true').lower() == 'true'
    
    if use_async:
        # 异步模式：创建任务并返回任务ID
        task_id = str(uuid.uuid4())
        
        # 保存文件到临时位置
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1])
        file.save(temp_file.name)
        
        # 创建任务数据
        task_data = {
            'file_path': temp_file.name,
            'filename': file.filename,
            'analysis_type': request.form.get('analysis_type', 'full'),
            'enable_advanced_detection': request.form.get('enable_advanced_detection', 'true').lower() == 'true'
        }
        
        # 创建DuckDB任务（使用INSERT OR REPLACE避免重复主键问题）
        task_repository.create_task(task_id, 'contract_analysis', file.filename)
        
        # 启动异步处理（这里可以使用线程或其他异步机制）
        import threading
        thread = threading.Thread(target=process_contract_analysis_task, args=(task_id, task_data))
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'status': 'pending',
            'message': '合约风险分析任务已创建'
        })
    else:
        # 同步模式：保持向后兼容
        return _process_contract_sync(file)

def _process_contract_sync(file):
    """同步处理合约分析（保持向后兼容）"""
    try:
        # 保存文件到临时位置
        filename = secure_filename(file.filename)
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1])
        file.save(temp_file.name)
        
        # 读取数据
        if filename.endswith('.csv'):
            df = pd.read_csv(temp_file.name, dtype={'digital_id': str, 'recommender_digital_id': str})
        else:
            df = pd.read_excel(temp_file.name, engine='openpyxl', dtype={'digital_id': str, 'recommender_digital_id': str})
        
        # 初始化分析器
        analyzer = CTContractAnalyzer()
        
        # 执行分析
        results = analyzer.process_contract_data(df)
        
        # 调试信息

        
        logger.info(f"🔍 同步模式调试: 分析完成，结果数={len(results)}")
        if results:
            logger.info(f"🔍 第一个结果类型: {type(results[0])}")
            logger.info(f"🔍 第一个结果内容: {results[0]}")
        
        # 生成摘要数据
        summary = _generate_summary(results, df)
        
        # 统计合约信息
        contract_stats = []
        if 'contract_name' in df.columns:
            contract_stats = df.groupby('contract_name').size().reset_index(name='count').sort_values('count', ascending=False).to_dict(orient='records')
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 构建结果数据
        result_data = {
            'task_id': task_id,
            'status': 'completed',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'filename': filename,
            'contract_risks': results,
            'summary': summary,
            'contract_stats': contract_stats,
            'total_analyzed': len(df),
            'risks_found': len(results),
            'message': f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点'
        }
        
        # 保存到DuckDB (临时禁用以调试)
        # task_repository.create_task(task_id, 'contract_analysis', filename)
        # task_repository.update_task_status(task_id, 'completed', 100, result_data['message'])
        logger.info("🔧 DuckDB任务保存临时禁用，用于调试API问题")
        
        # ========== 旧存储逻辑 (已注释) ==========
        # contract_risk_repository.save_analysis_result(
        #     task_id=task_id,
        #     analysis_type='full',
        #     filename=filename,
        #     total_contracts=len(df),
        #     risk_contracts=len(results),
        #     wash_trading_count=len([r for r in results if 'wash_trading' in r.get('detection_type', '')]),
        #     cross_bd_count=len([r for r in results if 'cross_bd' in r.get('detection_method', '')]),
        #     result_data=result_data
        # )
        
        # ========== 新存储逻辑 (临时禁用以调试) ==========
        # 导入新的存储管理器
        # from database.algorithm_storage_manager import AlgorithmStorageManager
        
        # 使用新存储管理器保存结果
        # storage_manager = AlgorithmStorageManager()
        
        # 确保新存储表已初始化
        # if not storage_manager._check_new_storage_exists():
        #     logger.info("初始化新存储表结构...")
        #     storage_manager.initialize_tables()
        
        # 保存到新存储结构
        # new_result_id = storage_manager.store_algorithm_result(
        #     task_id=task_id,
        #     algorithm_type='suspected_wash_trading',  # 同步模式默认使用对敲检测
        #     result_data=result_data
        # )
        
        # if new_result_id:
        #     logger.info(f"✅ 新存储保存成功，result_id: {new_result_id}")
        # else:
        #     logger.error("❌ 新存储保存失败")
        
        logger.info("🔧 新存储逻辑临时禁用，用于调试API问题")
        
        # ========== 旧存储备份（已禁用） ==========
        # try:
        #     contract_risk_repository.save_analysis_result(
        #         task_id=task_id,
        #         analysis_type='full',
        #         filename=filename,
        #         total_contracts=len(df),
        #         risk_contracts=len(results),
        #         wash_trading_count=len([r for r in results if 'wash_trading' in r.get('detection_type', '')]),
        #         cross_bd_count=len([r for r in results if 'cross_bd' in r.get('detection_method', '')]),
        #         result_data=result_data
        #     )
        #     logger.info("✅ 旧存储备份保存成功")
        # except Exception as e:
        #     logger.warning(f"⚠️ 旧存储备份失败: {e}")
        logger.info("✅ 存储系统已升级为新架构，支持智能路由和数据兼容")
        # ========================================
        
        # 返回格式与前端期望的格式一致
        return jsonify({
            'status': 'success',
            'task_id': task_id,
            'rows': len(df),
            'result': {
                'suspicious': results,
                'summary': summary
            },
            'contract_stats': contract_stats,
            'message': f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点'
        })
        
    except Exception as e:
        return jsonify({'error': f'分析失败: {str(e)}'}), 500
    finally:
        # 清理临时文件
        if 'temp_file' in locals() and hasattr(temp_file, 'name') and os.path.exists(temp_file.name):
            os.remove(temp_file.name)

def process_contract_analysis_task(task_id, task_data):
    """处理合约风险分析任务"""
    try:
        # 更新任务状态
        task_repository.update_task_status(task_id, 'processing', 0, '正在处理合约风险分析...')
        
        # 初始化进度
        update_progress(task_id, {
            'stage': '任务初始化',
            'percentage': 0,
            'message': '正在初始化分析任务...'
        })
        
        file_path = task_data['file_path']
        filename = task_data['filename']
        
        # 进度更新：读取数据
        update_progress(task_id, {
            'stage': '数据读取',
            'percentage': 5,
            'message': f'正在读取文件: {filename}...'
        })
        
        # 读取数据
        if filename.endswith('.csv'):
            df = pd.read_csv(file_path, dtype={'digital_id': str, 'recommender_digital_id': str})
        else:
            df = pd.read_excel(file_path, engine='openpyxl', dtype={'digital_id': str, 'recommender_digital_id': str})
        
        # 进度更新：数据读取完成
        update_progress(task_id, {
            'stage': '数据读取完成',
            'percentage': 10,
            'message': f'成功读取 {len(df)} 条记录，正在初始化分析器...'
        })
        
        # 初始化分析器
        analyzer = CTContractAnalyzer(task_id=task_id)
        
        # 创建进度回调函数
        def progress_callback(progress_info):
            # 将分析器的进度映射到总进度的10%-95%范围
            analyzer_percentage = progress_info.get('percentage', 0)
            total_percentage = 10 + (analyzer_percentage / 100) * 85
            
            update_progress(task_id, {
                'stage': progress_info.get('stage', '正在分析'),
                'percentage': min(total_percentage, 95),
                'message': progress_info.get('message', '正在进行合约风险分析...'),
                'details': progress_info
            })
        
        # 执行分析
        analysis_result = analyzer.process_contract_data(df, progress_callback=progress_callback)

        # 提取结果数据
        if isinstance(analysis_result, dict):
            results = analysis_result.get('results', [])
            complete_positions = analysis_result.get('complete_positions', {})
            statistics = analysis_result.get('statistics', {})
        else:
            # 向后兼容：如果返回的是列表格式
            results = analysis_result
            complete_positions = {}
            statistics = {}

        # 进度更新：结果处理
        update_progress(task_id, {
            'stage': '结果处理',
            'percentage': 95,
            'message': f'正在处理 {len(results)} 个检测结果...'
        })

        # 生成摘要数据
        summary = _generate_summary(results, df)
        
        # 统计合约信息
        contract_stats = []
        if 'contract_name' in df.columns:
            contract_stats = df.groupby('contract_name').size().reset_index(name='count').sort_values('count', ascending=False).to_dict(orient='records')
        
        # 清理结果数据，确保JSON可序列化
        cleaned_results = clean_results_for_json(results)
        cleaned_summary = json_serializable(summary)
        cleaned_contract_stats = json_serializable(contract_stats)
        
        # 构建最终结果
        result_data = {
            'task_id': task_id,
            'status': 'completed',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'filename': filename,
            'analysis_type': task_data.get('analysis_type', 'full'),
            'contract_risks': cleaned_results,
            'summary': cleaned_summary,
            'contract_stats': cleaned_contract_stats,
            'total_analyzed': len(df),
            'risks_found': len(results),
            'message': f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点'
        }
        
        # 最终进度更新
        update_progress(task_id, {
            'stage': '分析完成',
            'percentage': 100,
            'message': f'合约风险分析完成，发现 {len(results)} 个风险点',
            'final_results': {
                'total_analyzed': len(df),
                'risks_found': len(results),
                'completion_time': datetime.now().isoformat()
            }
        })
        
        # ========== 旧存储逻辑 (已注释) ==========
        # contract_risk_repository.save_analysis_result(
        #     task_id=task_id,
        #     analysis_type=task_data.get('analysis_type', 'full'),
        #     filename=filename,
        #     total_contracts=len(df),
        #     risk_contracts=len(results),
        #     wash_trading_count=len([r for r in results if 'wash_trading' in r.get('detection_type', '')]),
        #     cross_bd_count=len([r for r in results if 'cross_bd' in r.get('detection_method', '')]),
        #     result_data=result_data
        # )
        
        # ========== 新存储逻辑 (启用) ==========
        # 导入新的存储管理器
        from database.algorithm_storage_manager import AlgorithmStorageManager
        
        # 使用新存储管理器保存结果
        storage_manager = AlgorithmStorageManager()
        
        # 确保新存储表已初始化
        if not storage_manager._check_new_storage_exists():
            logger.info("初始化新存储表结构...")
            storage_manager.initialize_tables()
        
        # 保存到新存储结构
        # 映射分析类型到算法类型
        analysis_type = task_data.get('analysis_type', 'full')
        algorithm_type = 'suspected_wash_trading' if analysis_type == 'full' else analysis_type
        
        new_result_id = storage_manager.store_algorithm_result(
            task_id=task_id,
            algorithm_type=algorithm_type,
            result_data=result_data
        )
        
        if new_result_id:
            logger.info(f"✅ 新存储保存成功，result_id: {new_result_id}")
        else:
            logger.error("❌ 新存储保存失败")

        # ========== 用户行为分析触发 (新增) ==========
        if new_result_id:
            try:
                from modules.contract_risk_analysis.services.user_analysis_trigger import UserAnalysisTrigger

                # 创建用户行为分析触发器
                user_trigger = UserAnalysisTrigger()

                # 触发用户行为分析
                analysis_success = user_trigger.trigger_user_behavior_analysis(
                    task_id=task_id,
                    result_id=new_result_id,
                    complete_positions=complete_positions,
                    progress_callback=lambda progress: update_progress(task_id, progress)
                )

                if analysis_success:
                    logger.info("✅ 用户行为分析触发成功")
                else:
                    logger.warning("⚠️ 用户行为分析触发失败，但不影响合约分析结果")

            except Exception as e:
                logger.error(f"用户行为分析触发异常: {str(e)}")
                # 不影响主流程，继续执行

        # ========== 旧存储备份（已禁用） ==========
        # try:
        #     contract_risk_repository.save_analysis_result(
        #         task_id=task_id,
        #         analysis_type=task_data.get('analysis_type', 'full'),
        #         filename=filename,
        #         total_contracts=len(df),
        #         risk_contracts=len(results),
        #         wash_trading_count=len([r for r in results if 'wash_trading' in r.get('detection_type', '')]),
        #         cross_bd_count=len([r for r in results if 'cross_bd' in r.get('detection_method', '')]),
        #         result_data=result_data
        #     )
        #     logger.info("✅ 旧存储备份保存成功")
        # except Exception as e:
        #     logger.warning(f"⚠️ 旧存储备份失败: {e}")
        logger.info("✅ 存储系统已升级为新架构，通过数据适配器提供兼容性保障")
        # ========================================
        
        # 完成任务
        task_repository.update_task_status(task_id, 'completed', 100, f'成功分析 {len(df)} 条记录，发现 {len(results)} 个风险点')
        
    except Exception as e:
        error_msg = f"合约风险分析任务失败: {str(e)}"
        
        # 错误进度更新
        update_progress(task_id, {
            'stage': '分析失败',
            'percentage': 0,
            'message': error_msg,
            'error': str(e)
        })
        
        # 标记任务失败
        task_repository.update_task_status(task_id, 'failed', 0, error_msg)
    finally:
        # 清理临时文件
        if 'file_path' in task_data and os.path.exists(task_data['file_path']):
            os.remove(task_data['file_path'])
        
        # 清理进度信息（可选，或者保留一段时间）
        # 这里我们保留进度信息，以便前端获取最终状态

def _generate_summary(results, df):
    """生成摘要数据"""
    summary = {
        'total_analyzed': len(df),
        'risks_found': len(results),
        'risk_distribution': {'high': 0, 'medium': 0, 'low': 0},
        'contract_distribution': {},
        'anomaly_distribution': {}
    }
    
    # 统计风险分布、合约分布和异常类型分布
    for result in results:
        # 风险等级分布
        risk_level = result.get('severity', result.get('risk_level', '中'))
        risk_key = 'medium'  # 默认中风险
        
        if str(risk_level).lower() in ['高', 'high', 'critical']:
            risk_key = 'high'
        elif str(risk_level).lower() in ['低', 'low']:
            risk_key = 'low'
        elif str(risk_level).lower() in ['中', 'medium', 'normal']:
            risk_key = 'medium'
        
        if risk_key in summary['risk_distribution']:
            summary['risk_distribution'][risk_key] += 1
        
        # 合约分布
        contract_name = result.get('contractName', result.get('contract_name', '未知合约'))
        if contract_name:
            summary['contract_distribution'][contract_name] = summary['contract_distribution'].get(contract_name, 0) + 1
        
        # 异常类型分布
        anomaly_type = result.get('detection_type', '未知类型')
        if anomaly_type:
            summary['anomaly_distribution'][anomaly_type] = summary['anomaly_distribution'].get(anomaly_type, 0) + 1
    
    return summary

@contract_bp.route('/analyze', methods=['POST'])
@login_required  # 需要登录才能分析数据
def analyze_contract_data():
    """对已有数据进行合约风险分析"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': '缺少数据'}), 400
    
    try:
        # 将JSON数据转换为DataFrame
        df = pd.DataFrame(data)
        
        # 初始化分析器（生成临时task_id用于即时分析）
        import uuid
        temp_task_id = f"temp_{str(uuid.uuid4())[:8]}"
        analyzer = CTContractAnalyzer(task_id=temp_task_id)
        
        # 执行分析
        results = analyzer.process_contract_data(df)
        
        # 生成摘要数据
        summary = _generate_summary(results, df)
        
        # 清理结果数据，确保JSON可序列化
        cleaned_results = clean_results_for_json(results)
        cleaned_summary = json_serializable(summary)
        
        return jsonify({
            'status': 'success',
            'result': {
                'suspicious': cleaned_results,
                'summary': cleaned_summary
            },
            'message': f'分析完成，发现 {len(results)} 个风险点'
        })
        
    except Exception as e:
        return jsonify({'error': f'分析失败: {str(e)}'}), 500

@contract_bp.route('/algorithms', methods=['GET'])
@login_required  # 需要登录才能查看算法
def get_algorithms():
    """获取可用的风险检测算法列表"""
    algorithms = [
        {
            'id': 'high_frequency_trading',
            'name': '高频交易检测',
            'description': '检测异常高频交易行为'
        },
        {
            'id': 'wash_trading',
            'name': '对敲交易检测', 
            'description': '检测可疑的对敲交易模式'
        },
        {
            'id': 'funding_arbitrage',
            'name': '资金费率套利检测',
            'description': '检测利用资金费率进行套利的行为'
        },
        {
            'id': 'regular_brush',
            'name': '常持刷量检测',
            'description': '检测短时间持仓的刷量行为'
        },

    ]
    
    return jsonify({
        'status': 'success',
        'algorithms': algorithms
    })

@contract_bp.route('/status/<task_id>', methods=['GET'])
@login_required  # 需要登录才能查看任务状态
def get_task_status(task_id):
    """获取任务状态"""
    try:
        task = task_repository.get_task(task_id)
        if task:
            return jsonify({
                'task_id': task_id,
                'status': task.get('status'),
                'message': task.get('message', ''),
                'progress': task.get('progress', 0),
                'created_at': task.get('created_at'),
                'updated_at': task.get('updated_at')
            })
        else:
            return jsonify({'error': '任务不存在'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@contract_bp.route('/result/<task_id>', methods=['GET'])
@login_required  # 需要登录才能查看结果
def get_task_result(task_id):
    """获取任务分析结果 - 升级版（使用数据适配器）"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        
        # 从DuckDB获取任务
        task = task_repository.get_task(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        # 使用数据适配器获取分析结果（自动路由到新存储系统）
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()
        
        # 确保新存储系统已启用
        adapter.enable_new_storage()
        
        # 获取分析结果
        analysis_result = adapter.get_analysis_result(task_id)
        if not analysis_result:
            return jsonify({'error': '分析结果不存在'}), 404
        
        result_data = analysis_result.get('result_data', {})
        contract_risks = result_data.get('contract_risks', [])
        summary = result_data.get('summary', {})
        
        # 分页处理
        total_records = len(contract_risks)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paged_risks = contract_risks[start_idx:end_idx]
        total_pages = (total_records + page_size - 1) // page_size
        
        # 计算合约分布统计
        contract_stats = {}
        if contract_risks:
            for risk in contract_risks:
                contract_name = risk.get('contract_name') or risk.get('contractName', 'unknown')
                contract_stats[contract_name] = contract_stats.get(contract_name, 0) + 1
            # 按数量排序，取前10
            contract_stats = dict(sorted(contract_stats.items(), key=lambda x: x[1], reverse=True)[:10])
        
        # 异常类型中文映射
        anomaly_type_mapping = {
            'same_account_wash_trading': '同账户对敲',
            'cross_account_wash_trading': '跨账户对敲', 
            'suspected_wash_trading': '对敲交易',
            'high_frequency_trading': '高频交易',
            'regular_brush_trading': '规律性刷量',
            'funding_rate_arbitrage': '资金费率套利',
            'unknown': '未知类型'
        }
        
        # 数据质量统计
        quality_stats = {
            'high_quality': 0,
            'medium_quality': 0,
            'low_quality': 0,
            'total_validated': 0,
            'average_quality_score': 0.0
        }
        
        # 计算详细的异常分布（包括对敲交易细分）和数据质量统计
        detailed_anomaly_distribution = {}
        quality_scores = []
        
        if contract_risks:
            for risk in contract_risks:
                detection_type = risk.get('detection_type', 'unknown')
                detection_method = risk.get('detection_method', '')
                
                # 对敲交易需要细分
                if 'wash_trading' in detection_type:
                    if 'same_account' in detection_method:
                        key = 'same_account_wash_trading'  # 同账户对敲
                    elif 'cross_account' in detection_method:
                        key = 'cross_account_wash_trading'  # 跨账户对敲
                    else:
                        key = 'suspected_wash_trading'  # 未分类的对敲
                else:
                    key = detection_type  # 其他检测类型直接使用
                
                # 转换为中文名称
                chinese_key = anomaly_type_mapping.get(key, key)
                detailed_anomaly_distribution[chinese_key] = detailed_anomaly_distribution.get(chinese_key, 0) + 1
                
                # 统计数据质量
                data_quality = risk.get('data_quality', {})
                if data_quality:
                    quality_stats['total_validated'] += 1
                    confidence_level = data_quality.get('confidence_level', 'low')
                    quality_score = data_quality.get('quality_score', 0.0)
                    quality_scores.append(quality_score)
                    
                    if confidence_level == 'high':
                        quality_stats['high_quality'] += 1
                    elif confidence_level == 'medium':
                        quality_stats['medium_quality'] += 1
                    else:
                        quality_stats['low_quality'] += 1
        
        # 计算平均质量评分
        if quality_scores:
            quality_stats['average_quality_score'] = sum(quality_scores) / len(quality_scores)
        
        # 构建返回格式
        result = {
            'task_id': task_id,
            'task_type': 'contract_analysis',
            'status': task.get('status'),
            'message': task.get('message', ''),
            'created_at': task.get('created_at'),
            'updated_at': task.get('updated_at'),
            'result': {
                'suspicious': paged_risks,
                'summary': {
                    'risk_distribution': summary.get('risk_distribution', {}),
                    'anomaly_distribution': detailed_anomaly_distribution,  # 使用详细的异常分布
                    'quality_stats': quality_stats  # 添加数据质量统计
                },
                'contract_stats': contract_stats,  # 使用动态计算的合约统计
                'total_analyzed': result_data.get('total_analyzed', total_records),
                'risks_found': result_data.get('risks_found', total_records)
            },
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            },
            'error': None
        }
        
        
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"接口异常: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@contract_bp.route('/config', methods=['GET', 'POST'])
def handle_config():
    """处理分析配置"""
    if request.method == 'GET':
        # 返回默认配置
        default_config = {
            'algorithms': {
                'high_frequency_trading': {'enabled': True, 'threshold': 0.8},
                'wash_trading': {'enabled': True, 'threshold': 0.7},
                'funding_arbitrage': {'enabled': True, 'threshold': 0.6},
                'regular_brush': {'enabled': True, 'threshold': 0.7},

            },
            'detection_sensitivity': 'medium',
            'max_analysis_records': 100000
        }
        return jsonify(default_config)
    
    elif request.method == 'POST':
        # 更新配置
        config_data = request.get_json()
        # 配置保存功能暂未实现
        return jsonify({'message': '配置已更新', 'config': config_data})

@contract_bp.route('/data/<task_id>', methods=['GET'])
def get_contract_risk_data(task_id):
    """获取合约风险数据（用于合约整合分析）- 升级版"""
    try:
        # 🚀 修改：使用数据适配器智能路由到新存储
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()
        analysis_result = adapter.get_analysis_result(task_id)
        
        if not analysis_result:
            return jsonify({'error': '分析结果不存在'}), 404
        
        result_data = analysis_result.get('result_data', {})
        
        # 获取修复后的合约风险数据（repository已自动修复格式）
        contract_risks = result_data.get('contract_risks', [])
        
        # 构建合约整合所需的数据格式
        response_data = {
            'status': 'success',
            'task_id': task_id,
            'contract_risks': contract_risks,  # 已修复格式
            'summary': result_data.get('summary', {}),
            'total_analyzed': analysis_result.get('total_contracts', 0),
            'risks_found': analysis_result.get('risk_contracts', 0),
            'created_at': analysis_result.get('created_at'),
            'filename': analysis_result.get('filename', '')
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({'error': f'获取数据失败: {str(e)}'}), 500

@contract_bp.route('/tasks', methods=['GET'])
def get_completed_tasks():
    """获取已完成的合约风险分析任务列表"""
    try:
        # 从DuckDB获取合约分析任务
        tasks = task_repository.get_tasks_by_type('contract_analysis')
        
        completed_tasks = []
        
        # 🚀 修改：使用数据适配器
        from modules.contract_risk_analysis.services.data_adapter import ContractDataAdapter
        adapter = ContractDataAdapter()
        
        for task in tasks:
            if task.get('status') == 'completed':
                # 获取分析结果的详细信息
                analysis_result = adapter.get_analysis_result(task.get('task_id'))
                
                # 从数据库记录中直接获取字段，而不是从result_data中获取
                total_contracts = analysis_result.get('total_contracts', 0) if analysis_result else 0
                risk_contracts = analysis_result.get('risk_contracts', 0) if analysis_result else 0
                
                completed_tasks.append({
                    'task_id': task.get('task_id'),
                    'filename': task.get('filename', '未知文件'),
                    'total_contracts': total_contracts,
                    'risk_contracts': risk_contracts,
                    'total_analyzed': total_contracts,  # 保持向后兼容
                    'risks_found': risk_contracts,  # 保持向后兼容
                    'created_at': task.get('created_at'),
                    'status': task.get('status')
                })
        
        return jsonify({
            'status': 'success',
            'tasks': completed_tasks,
            'total': len(completed_tasks)
        })
        
    except Exception as e:
        return jsonify({'error': f'获取任务列表失败: {str(e)}'}), 500

@contract_bp.route('/progress/<task_id>', methods=['GET'])
def get_analysis_progress(task_id):
    """获取合约风险分析进度"""
    try:
        progress = get_progress(task_id)
        if not progress:
            return jsonify({'error': '任务不存在或已完成'}), 404
        
        return jsonify(progress)
    
    except Exception as e:
        return jsonify({'error': f'获取进度失败: {str(e)}'}), 500

def _get_current_timestamp():
    """获取当前时间戳字符串"""
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

@contract_bp.route('/wash-trading/details/<result_id>', methods=['GET'])
def get_wash_trading_details(result_id):
    """获取对敲交易详情"""
    try:
        # 使用新一代检测器
        from ..algorithms.wash_trading.detector import NextGenWashTradingDetector
        from ....database.duckdb_manager import DuckDBManager
        
        detector = NextGenWashTradingDetector()
        
        # 检查是否支持详细信息功能
        info = detector.get_detailed_analysis_info()
        if not info.get('supports_detailed_info', False):
            return jsonify({
                'status': 'error',
                'message': '当前版本不支持详细信息功能',
                'trade_pairs': []
            }), 400
        
        # 获取数据库连接
        try:
            db_manager = DuckDBManager()
            with db_manager.get_connection() as conn:
                # 获取交易对详情
                trade_pairs = detector.get_trade_pair_details(result_id, conn)
                
                # 格式化为API友好的格式
                formatted_pairs = detector.result_formatter.format_for_api(trade_pairs) if hasattr(detector, 'result_formatter') else trade_pairs
                
                return jsonify({
                    'status': 'success',
                    'result_id': result_id,
                    'trade_pairs': formatted_pairs,
                    'total_pairs': len(formatted_pairs),
                    'message': f'成功获取 {len(formatted_pairs)} 个交易对详情'
                })
                
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'获取交易对详情失败: {str(e)}',
                'trade_pairs': []
            }), 500
            
    except ImportError as e:
        return jsonify({
            'status': 'error',
            'message': '对敲交易检测模块不可用',
            'trade_pairs': []
        }), 500
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'获取对敲交易详情失败: {str(e)}',
            'trade_pairs': []
        }), 500

@contract_bp.route('/wash-trading/format/<result_id>', methods=['GET'])
def format_wash_trading_details(result_id):
    """格式化显示对敲交易详情"""
    try:
        # 使用新一代检测器
        from ..algorithms.wash_trading.detector import NextGenWashTradingDetector
        from ....database.duckdb_manager import DuckDBManager
        
        detector = NextGenWashTradingDetector()
        
        # 检查是否支持详细信息功能
        info = detector.get_detailed_analysis_info()
        if not info.get('supports_detailed_info', False):
            return jsonify({
                'status': 'error',
                'message': '当前版本不支持详细信息功能',
                'formatted_text': ''
            }), 400
        
        # 获取合约名称参数
        contract_name = request.args.get('contract_name', '未知合约')
        
        try:
            # 获取数据库连接
            db_manager = DuckDBManager()
            with db_manager.get_connection() as conn:
                # 获取交易对详情
                trade_pairs = detector.get_trade_pair_details(result_id, conn)
                
                # 格式化为用户友好的文本
                formatted_text = detector.format_trade_pair_details(trade_pairs, contract_name)
                
                return jsonify({
                    'status': 'success',
                    'result_id': result_id,
                    'contract_name': contract_name,
                    'formatted_text': formatted_text,
                    'total_pairs': len(trade_pairs),
                    'message': f'成功格式化 {len(trade_pairs)} 个交易对详情'
                })
                
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'格式化交易对详情失败: {str(e)}',
                'formatted_text': ''
            }), 500
            
    except ImportError as e:
        return jsonify({
            'status': 'error',
            'message': '对敲交易检测模块不可用',
            'formatted_text': ''
        }), 500
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'格式化对敲交易详情失败: {str(e)}',
            'formatted_text': ''
        }), 500 