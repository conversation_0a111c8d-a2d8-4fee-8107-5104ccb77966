"""
鉴权服务 - 用户认证、创建、密码管理等核心业务逻辑
"""
import logging
from datetime import datetime, timedelta
from database.duckdb_manager import db_manager
from core.utils.password_utils import PasswordUtils

logger = logging.getLogger(__name__)

class AuthService:
    """鉴权服务类"""
    
    def __init__(self):
        self.db = db_manager
        self.password_utils = PasswordUtils()
    
    def authenticate_user(self, username, password, ip_address):
        """用户认证"""
        try:
            # 获取用户信息
            user = self._get_user_by_username(username)
            if not user:
                self._log_failed_login(username, ip_address, "用户不存在")
                return None
            
            # 检查用户是否被禁用
            if not user['is_active']:
                self._log_failed_login(username, ip_address, "用户被禁用")
                return None
            
            # 检查账户是否被锁定
            if self._is_account_locked(user):
                self._log_failed_login(username, ip_address, "账户被锁定")
                return None
            
            # 验证密码
            if not self.password_utils.verify_password(password, user['password_hash']):
                self._handle_failed_login(user['id'], username, ip_address)
                return None
            
            # 登录成功，重置失败次数并更新最后登录时间
            self._reset_login_attempts(user['id'])
            self._update_last_login(user['id'])
            
            # 记录成功日志
            self.log_user_activity(user['id'], 'login_success', 'user_login', ip_address, True)
            
            logger.info(f"用户登录成功: {username}, IP: {ip_address}")
            
            return {
                'id': user['id'],
                'username': user['username'],
                'role': user['role'],
                'email': user['email']
            }
            
        except Exception as e:
            logger.error(f"用户认证失败: {str(e)}")
            return None
    
    def create_user(self, username, password, role, email=None):
        """创建新用户"""
        try:
            # 检查用户名是否已存在
            if self._get_user_by_username(username):
                return False, "用户名已存在"
            
            # 验证密码强度
            is_valid, message = self.password_utils.validate_password_strength(password)
            if not is_valid:
                return False, message
            
            # 验证角色
            if role not in ['viewer', 'admin']:
                return False, "无效的用户角色"
            
            # 加密密码
            password_hash = self.password_utils.hash_password(password)
            
            # 插入用户 - 手动指定ID以兼容DuckDB
            # 先获取下一个可用的ID
            next_id_query = "SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM auth_users"
            next_id_result = self.db.fetch_one(next_id_query)
            next_id = next_id_result['next_id'] if next_id_result else 1
            
            query = """
            INSERT INTO auth_users (id, username, password_hash, role, email)
            VALUES (?, ?, ?, ?, ?)
            """
            self.db.execute_query(query, [next_id, username, password_hash, role, email])
            
            logger.info(f"创建用户成功: {username}, 角色: {role}")
            return True, "用户创建成功"
            
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            return False, "创建用户失败"
    
    def change_password(self, user_id, old_password, new_password):
        """修改密码"""
        try:
            # 获取用户信息
            user = self._get_user_by_id(user_id)
            if not user:
                return False, "用户不存在"
            
            # 验证旧密码
            if not self.password_utils.verify_password(old_password, user['password_hash']):
                return False, "旧密码错误"
            
            # 验证新密码强度
            is_valid, message = self.password_utils.validate_password_strength(new_password)
            if not is_valid:
                return False, message
            
            # 更新密码
            new_password_hash = self.password_utils.hash_password(new_password)
            query = "UPDATE auth_users SET password_hash = ?, updated_at = ? WHERE id = ?"
            self.db.execute_query(query, [new_password_hash, datetime.now(), user_id])
            
            logger.info(f"用户修改密码: {user['username']}")
            return True, "密码修改成功"
            
        except Exception as e:
            logger.error(f"修改密码失败: {str(e)}")
            return False, "修改密码失败"
    
    def log_user_activity(self, user_id, action, resource, ip_address, success, error_message=None):
        """记录用户活动日志"""
        try:
            # 获取下一个可用的ID
            next_id_query = "SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM auth_user_activity_logs"
            next_id_result = self.db.fetch_one(next_id_query)
            next_id = next_id_result['next_id'] if next_id_result else 1
            
            query = """
            INSERT INTO auth_user_activity_logs (id, user_id, action, resource, ip_address, success, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            self.db.execute_query(query, [next_id, user_id, action, resource, ip_address, success, error_message])
        except Exception as e:
            logger.error(f"记录活动日志失败: {str(e)}")
    
    def get_user_list(self, page=1, page_size=20):
        """获取用户列表"""
        try:
            offset = (page - 1) * page_size
            query = """
            SELECT id, username, role, email, created_at, last_login, is_active
            FROM auth_users
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
            """
            users = self.db.fetch_all(query, [page_size, offset])
            
            # 获取总数
            count_query = "SELECT COUNT(*) as total FROM auth_users"
            total_result = self.db.fetch_one(count_query)
            total = total_result['total'] if total_result else 0
            
            return {
                'users': users,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        except Exception as e:
            logger.error(f"获取用户列表失败: {str(e)}")
            return {'users': [], 'total': 0, 'page': page, 'page_size': page_size}
    
    def toggle_user_status(self, user_id, is_active):
        """启用/禁用用户"""
        try:
            query = "UPDATE auth_users SET is_active = ?, updated_at = ? WHERE id = ?"
            self.db.execute_query(query, [is_active, datetime.now(), user_id])
            
            user = self._get_user_by_id(user_id)
            status = "启用" if is_active else "禁用"
            logger.info(f"{status}用户: {user['username'] if user else user_id}")
            return True, f"用户{status}成功"
        except Exception as e:
            logger.error(f"切换用户状态失败: {str(e)}")
            return False, "操作失败"
    
    def _get_user_by_username(self, username):
        """根据用户名获取用户"""
        query = "SELECT * FROM auth_users WHERE username = ?"
        return self.db.fetch_one(query, [username])
    
    def _get_user_by_id(self, user_id):
        """根据ID获取用户"""
        query = "SELECT * FROM auth_users WHERE id = ?"
        return self.db.fetch_one(query, [user_id])
    
    def _is_account_locked(self, user):
        """检查账户是否被锁定"""
        if not user['locked_until']:
            return False
        return datetime.now() < user['locked_until']
    
    def _handle_failed_login(self, user_id, username, ip_address):
        """处理登录失败"""
        try:
            # 增加失败次数
            query = "UPDATE auth_users SET login_attempts = login_attempts + 1 WHERE id = ?"
            self.db.execute_query(query, [user_id])
            
            # 检查是否需要锁定账户
            user = self._get_user_by_id(user_id)
            max_attempts = self._get_max_login_attempts()
            
            if user['login_attempts'] + 1 >= max_attempts:
                lock_minutes = self._get_account_lock_minutes()
                locked_until = datetime.now() + timedelta(minutes=lock_minutes)
                
                lock_query = "UPDATE auth_users SET locked_until = ? WHERE id = ?"
                self.db.execute_query(lock_query, [locked_until, user_id])
                
                logger.warning(f"账户锁定: {username}, 锁定到: {locked_until}")
            
            self._log_failed_login(username, ip_address, "密码错误")
            
        except Exception as e:
            logger.error(f"处理登录失败异常: {str(e)}")
    
    def _reset_login_attempts(self, user_id):
        """重置登录失败次数"""
        query = "UPDATE auth_users SET login_attempts = 0, locked_until = NULL WHERE id = ?"
        self.db.execute_query(query, [user_id])
    
    def _update_last_login(self, user_id):
        """更新最后登录时间"""
        query = "UPDATE auth_users SET last_login = ? WHERE id = ?"
        self.db.execute_query(query, [datetime.now(), user_id])
    
    def _log_failed_login(self, username, ip_address, reason):
        """记录登录失败日志"""
        logger.warning(f"登录失败: {username}, IP: {ip_address}, 原因: {reason}")
    
    def _get_max_login_attempts(self):
        """获取最大登录尝试次数"""
        try:
            query = "SELECT value FROM auth_system_config WHERE key = 'max_login_attempts'"
            result = self.db.fetch_one(query)
            return int(result['value']) if result else 5
        except:
            return 5
    
    def _get_account_lock_minutes(self):
        """获取账户锁定时间"""
        try:
            query = "SELECT value FROM auth_system_config WHERE key = 'account_lock_minutes'"
            result = self.db.fetch_one(query)
            return int(result['value']) if result else 30
        except:
            return 30

# 全局鉴权服务实例
auth_service = AuthService() 