"""
用户管理API - 管理员专用功能
"""
from flask import Blueprint, request, jsonify, g
import logging
from modules.auth.services.auth_service import auth_service
from core.utils.decorators import admin_required

user_bp = Blueprint('user', __name__)
logger = logging.getLogger(__name__)

@user_bp.route('/users', methods=['GET'])
@admin_required
def get_users():
    """获取用户列表 - 仅管理员可访问"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        
        result = auth_service.get_user_list(page, page_size)
        
        # 记录操作日志
        auth_service.log_user_activity(
            g.current_user['id'],
            'view_users',
            f"查看用户列表 (第{page}页)",
            request.remote_addr,
            True
        )
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取用户列表失败'
        }), 500

@user_bp.route('/users', methods=['POST'])
@admin_required
def create_user():
    """创建新用户 - 仅管理员可访问"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '')
        role = data.get('role', 'viewer')
        email = data.get('email', '')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'error': '用户名和密码不能为空'
            }), 400
        
        if role not in ['viewer', 'admin']:
            return jsonify({
                'success': False,
                'error': '无效的用户角色'
            }), 400
        
        success, message = auth_service.create_user(username, password, role, email)
        
        # 记录操作日志
        auth_service.log_user_activity(
            g.current_user['id'],
            'create_user',
            f"创建用户: {username}, 角色: {role}",
            request.remote_addr,
            success,
            None if success else message
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '创建用户失败，请重试'
        }), 500

@user_bp.route('/users/<int:user_id>/status', methods=['PUT'])
@admin_required
def toggle_user_status(user_id):
    """切换用户状态 - 仅管理员可访问"""
    try:
        data = request.get_json()
        is_active = data.get('is_active', True)
        
        # 防止管理员禁用自己
        if user_id == g.current_user['id'] and not is_active:
            return jsonify({
                'success': False,
                'error': '不能禁用自己的账户'
            }), 400
        
        success, message = auth_service.toggle_user_status(user_id, is_active)
        
        # 记录操作日志
        status = "启用" if is_active else "禁用"
        auth_service.log_user_activity(
            g.current_user['id'],
            'toggle_user_status',
            f"{status}用户ID: {user_id}",
            request.remote_addr,
            success,
            None if success else message
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        logger.error(f"切换用户状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '操作失败，请重试'
        }), 500

@user_bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@admin_required
def reset_user_password(user_id):
    """重置用户密码 - 仅管理员可访问"""
    try:
        from core.utils.password_utils import PasswordUtils
        
        # 生成随机密码
        new_password = PasswordUtils.generate_random_password(8)
        password_hash = PasswordUtils.hash_password(new_password)
        
        # 更新密码
        from database.duckdb_manager import db_manager
        from datetime import datetime
        
        query = "UPDATE auth_users SET password_hash = ?, updated_at = ?, login_attempts = 0, locked_until = NULL WHERE id = ?"
        db_manager.execute_query(query, [password_hash, datetime.now(), user_id])
        
        # 记录操作日志
        auth_service.log_user_activity(
            g.current_user['id'],
            'reset_password',
            f"重置用户密码: {user_id}",
            request.remote_addr,
            True
        )
        
        logger.info(f"管理员 {g.current_user['username']} 重置了用户 {user_id} 的密码")
        
        return jsonify({
            'success': True,
            'message': '密码重置成功',
            'new_password': new_password
        })
        
    except Exception as e:
        logger.error(f"重置密码失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '重置密码失败，请重试'
        }), 500

@user_bp.route('/activity-logs', methods=['GET'])
@admin_required
def get_activity_logs():
    """获取用户活动日志 - 仅管理员可访问"""
    try:
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 50))
        user_id = request.args.get('user_id')
        action = request.args.get('action')
        
        from database.duckdb_manager import db_manager
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if user_id:
            where_conditions.append("l.user_id = ?")
            params.append(int(user_id))
        
        if action:
            where_conditions.append("l.action LIKE ?")
            params.append(f"%{action}%")
        
        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 查询日志
        offset = (page - 1) * page_size
        query = f"""
        SELECT l.*, u.username
        FROM auth_user_activity_logs l
        JOIN auth_users u ON l.user_id = u.id
        {where_clause}
        ORDER BY l.created_at DESC
        LIMIT ? OFFSET ?
        """
        params.extend([page_size, offset])
        
        logs = db_manager.fetch_all(query, params)
        
        # 获取总数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM auth_user_activity_logs l
        JOIN auth_users u ON l.user_id = u.id
        {where_clause}
        """
        count_params = params[:-2]  # 移除LIMIT和OFFSET参数
        total_result = db_manager.fetch_one(count_query, count_params)
        total = total_result['total'] if total_result else 0
        
        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'total': total,
                'page': page,
                'page_size': page_size
            }
        })
        
    except Exception as e:
        logger.error(f"获取活动日志失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取活动日志失败'
        }), 500 