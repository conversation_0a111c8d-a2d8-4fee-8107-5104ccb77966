"""
鉴权API - 登录/登出等核心功能
"""
from flask import Blueprint, request, jsonify, session
import logging
from modules.auth.services.auth_service import auth_service
from modules.auth.services.session_manager import session_manager
from core.utils.decorators import login_required

auth_bp = Blueprint('auth', __name__)
logger = logging.getLogger(__name__)

@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'error': '用户名和密码不能为空'
            }), 400
        
        # 验证用户凭据
        user = auth_service.authenticate_user(username, password, request.remote_addr)
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户名或密码错误'
            }), 401
        
        # 创建会话
        session_id = session_manager.create_session(
            user['id'], 
            request.remote_addr, 
            request.headers.get('User-Agent', '')
        )
        
        # 设置session
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['role'] = user['role']
        session['session_id'] = session_id
        session.permanent = True
        
        # 记录登录成功日志
        auth_service.log_user_activity(
            user['id'], 
            'login_success', 
            f"登录成功", 
            request.remote_addr, 
            True
        )
        
        logger.info(f"用户登录成功: {username}, IP: {request.remote_addr}")
        
        return jsonify({
            'success': True,
            'message': '登录成功',
            'user': {
                'id': user['id'],
                'username': user['username'],
                'role': user['role']
            }
        })
        
    except Exception as e:
        logger.error(f"登录处理异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '登录处理失败，请重试'
        }), 500

@auth_bp.route('/logout', methods=['POST'])
@login_required
def logout():
    """用户登出"""
    try:
        user_id = session.get('user_id')
        username = session.get('username')
        session_id = session.get('session_id')
        
        # 销毁会话
        if session_id:
            session_manager.destroy_session(session_id)
        
        # 记录登出日志
        if user_id:
            auth_service.log_user_activity(
                user_id, 
                'logout', 
                "用户登出", 
                request.remote_addr, 
                True
            )
        
        # 清除session
        session.clear()
        
        logger.info(f"用户登出: {username}, IP: {request.remote_addr}")
        
        return jsonify({
            'success': True,
            'message': '已安全登出'
        })
        
    except Exception as e:
        logger.error(f"登出处理异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '登出处理失败'
        }), 500

@auth_bp.route('/check', methods=['GET'])
def check_auth():
    """检查登录状态"""
    try:
        if 'user_id' not in session:
            return jsonify({
                'authenticated': False,
                'user': None
            })
        
        # 验证会话
        if not session_manager.validate_session(session.get('session_id')):
            session.clear()
            return jsonify({
                'authenticated': False,
                'user': None
            })
        
        return jsonify({
            'authenticated': True,
            'user': {
                'id': session['user_id'],
                'username': session['username'],
                'role': session['role']
            }
        })
        
    except Exception as e:
        logger.error(f"检查认证状态异常: {str(e)}")
        return jsonify({
            'authenticated': False,
            'user': None
        })

@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    try:
        data = request.get_json()
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')
        
        if not old_password or not new_password:
            return jsonify({
                'success': False,
                'error': '原密码和新密码不能为空'
            }), 400
        
        user_id = session['user_id']
        success, message = auth_service.change_password(user_id, old_password, new_password)
        
        # 记录密码修改日志
        auth_service.log_user_activity(
            user_id, 
            'change_password', 
            "修改密码", 
            request.remote_addr, 
            success,
            None if success else message
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        logger.error(f"修改密码异常: {str(e)}")
        return jsonify({
            'success': False,
            'error': '修改密码失败，请重试'
        }), 500 