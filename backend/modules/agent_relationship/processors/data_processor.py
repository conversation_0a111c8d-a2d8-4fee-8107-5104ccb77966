"""
数据处理器模块
提供数据预处理功能，集成层级分析功能
"""
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional

# 导入层级分析模块
from ..services.level_analyzer import (
    AgentLevelAnalyzer, 
    UserDataRepository,
    LevelRelationshipAnalyzer,
    LevelRiskEvaluator
)

logger = logging.getLogger(__name__)

def preprocess_data(df: pd.DataFrame, level_column: str = '') -> pd.DataFrame:
    """
    数据预处理函数
    
    参数:
        df: 原始数据框
        level_column: 层级列名
        
    返回:
        预处理后的数据框
    """
    if df is None or df.empty:
        return df
    
    # 确保必要的列存在
    required_columns = ['digital_id', 'device_id', 'ip']
    for col in required_columns:
        if col not in df.columns:
            df[col] = ''
    
    # 处理缺失值
    df = df.fillna('')
    
    # 确保ID列为字符串类型
    id_columns = ['digital_id', 'recommender_digital_id', 'top_kol_digital_id']
    for col in id_columns:
        if col in df.columns:
            df[col] = df[col].astype(str)
    
    # 映射字段名称到统一的命名规范
    # 将top_kol_bd_name映射为bd_name
    if 'top_kol_bd_name' in df.columns:
        df['bd_name'] = df['top_kol_bd_name']
    elif 'bd_name' not in df.columns:
        df['bd_name'] = '未分配'
    
    # member_id和digital_id应该是独立的字段，不需要相互替换
    
    # 处理用户名（如果存在的话）
    if 'username' not in df.columns:
        df['username'] = ''
    
    # 处理代理级别
    if 'user_agent_level' in df.columns:
        df['agent_level'] = df['user_agent_level'].fillna('未知级别')
    elif 'agent_level' not in df.columns:
        df['agent_level'] = '未知级别'
    
    # 处理层级列
    if level_column and level_column in df.columns:
        df['level'] = df[level_column]
    elif 'level' not in df.columns:
        df['level'] = 1
    
    # 确保created_time列存在，如果没有则使用当前时间
    if 'created_time' not in df.columns:
        from datetime import datetime
        df['created_time'] = datetime.now()
    else:
        # 尝试转换created_time为datetime类型
        try:
            df['created_time'] = pd.to_datetime(df['created_time'])
        except:
            from datetime import datetime
            df['created_time'] = datetime.now()
    
    return df

def find_shared_relationships(df: pd.DataFrame, shared_field: str) -> List[Dict[str, Any]]:
    """
    查找共享关系
    
    参数:
        df: 数据框
        shared_field: 共享字段名（如'device_id'或'ip'）
        
    返回:
        共享关系列表
    """
    if df.empty or shared_field not in df.columns:
        return []
    
    results = []
    
    # 按共享字段分组，找出有多个用户的组
    grouped = df.groupby(shared_field)
    
    for shared_value, group in grouped:
        if len(group) > 1 and shared_value and str(shared_value).strip():
            # 获取用户列表
            users = group.to_dict('records')
            
            # 生成用户对
            for i in range(len(users)):
                for j in range(i + 1, len(users)):
                    user_a = users[i]
                    user_b = users[j]
                    
                    result = {
                        f'shared_{shared_field}': shared_value,
                        'user_a_mid': user_a.get('digital_id', ''),
                        'user_a_member_id': user_a.get('member_id', ''),
                        'user_a_name': user_a.get('user_name', ''),
                        'user_a_bd': user_a.get('bd_team', ''),
                        'user_a_level': user_a.get('level', ''),
                        'user_a_time': user_a.get('login_time', ''),
                        'user_b_mid': user_b.get('digital_id', ''),
                        'user_b_member_id': user_b.get('member_id', ''),
                        'user_b_name': user_b.get('user_name', ''),
                        'user_b_bd': user_b.get('bd_team', ''),
                        'user_b_level': user_b.get('level', ''),
                        'user_b_time': user_b.get('login_time', ''),
                        'same_bd': user_a.get('bd_team', '') == user_b.get('bd_team', ''),
                        'match_count': len(group)
                    }
                    results.append(result)
    
    return results

def find_both_shared(device_shared: List[Dict], ip_shared: List[Dict]) -> List[Dict]:
    """
    查找同时共享设备和IP的关系
    
    参数:
        device_shared: 设备共享关系列表
        ip_shared: IP共享关系列表
        
    返回:
        同时共享设备和IP的关系列表
    """
    both_shared = []
    
    # 创建设备共享的用户对集合
    device_pairs = set()
    for item in device_shared:
        pair = tuple(sorted([item['user_a_mid'], item['user_b_mid']]))
        device_pairs.add(pair)
    
    # 检查IP共享中哪些用户对也在设备共享中
    for item in ip_shared:
        pair = tuple(sorted([item['user_a_mid'], item['user_b_mid']]))
        if pair in device_pairs:
            # 添加到两者都共享的列表
            both_item = item.copy()
            both_item['shared_type'] = 'both'
            both_shared.append(both_item)
    
    return both_shared


def enhance_data_with_level_analysis(df: pd.DataFrame, 
                                   enable_risk_evaluation: bool = False) -> pd.DataFrame:
    """
    使用层级分析增强数据
    
    参数:
        df: 原始数据框
        enable_risk_evaluation: 是否启用风险评估
        
    返回:
        增强后的数据框，包含层级分析和风险评估结果
    """
    try:
        if df.empty:
            logger.warning("数据框为空，跳过层级分析")
            return df
        
        # 创建用户数据仓库
        user_repository = UserDataRepository(df.copy())
        
        # 创建层级分析器
        level_analyzer = AgentLevelAnalyzer(user_repository)
        
        # 创建关系分析器
        relationship_analyzer = LevelRelationshipAnalyzer(level_analyzer)
        
        # 存储分析结果的列表
        level_results = []
        
        logger.debug(f"开始对{len(df)}条记录进行层级分析")
        
        # 逐行分析用户层级
        for idx, row in df.iterrows():
            try:
                user_data = row.to_dict()
                
                # 层级分析
                level_result = level_analyzer.analyze(user_data)
                level_results.append(level_result)
                
            except Exception as e:
                logger.error(f"第{idx}行数据分析失败: {e}")
                # 添加默认结果
                level_results.append({
                    'level_type': '分析失败',
                    'level_number': 0,
                    'description': f'分析失败: {str(e)}',
                    'analysis_method': '异常处理'
                })
        
        # 将层级分析结果添加到DataFrame
        if level_results:
            level_df = pd.DataFrame(level_results)
            df = df.reset_index(drop=True)
            level_df = level_df.reset_index(drop=True)
            
            # 添加层级分析字段
            df['analyzed_level_type'] = level_df['level_type']
            df['analyzed_level_number'] = level_df['level_number']
            df['level_description'] = level_df['description']
            df['analysis_method'] = level_df['analysis_method']
        
        # 为了保持向后兼容，添加默认的风险字段（但不进行实际评估）
        df['risk_score'] = 0.0  # 默认无风险
        df['risk_level'] = 'very_low'  # 默认极低风险
        df['risk_description'] = '已禁用风险评估'
        
        logger.info(f"层级分析完成，处理{len(df)}条记录")
        
        return df
        
    except Exception as e:
        logger.error(f"层级分析增强失败: {e}")
        return df


def analyze_relationships_in_shared_data(shared_data: List[Dict[str, Any]], 
                                       user_repository: Optional[UserDataRepository] = None) -> List[Dict[str, Any]]:
    """
    分析共享数据中的用户关系
    
    参数:
        shared_data: 共享关系数据列表
        user_repository: 用户数据仓库（可选）
        
    返回:
        增强的关系数据，包含层级关系分析
    """
    try:
        if not shared_data:
            return []
        
        # 创建层级分析器
        level_analyzer = AgentLevelAnalyzer(user_repository) if user_repository else None
        relationship_analyzer = LevelRelationshipAnalyzer(level_analyzer)
        
        enhanced_relationships = []
        
        logger.info(f"开始分析{len(shared_data)}个关系对")
        
        for relationship in shared_data:
            try:
                # 获取用户信息
                user_a_id = relationship.get('user_a_mid', '')
                user_b_id = relationship.get('user_b_mid', '')
                
                user_a_data = None
                user_b_data = None
                
                if user_repository:
                    user_a_data = user_repository.get_user_by_digital_id(user_a_id)
                    user_b_data = user_repository.get_user_by_digital_id(user_b_id)
                
                if not user_a_data or not user_b_data:
                    # 从关系数据构建简单的用户数据
                    user_a_data = {
                        'digital_id': user_a_id,
                        'user_agent_level': relationship.get('user_a_level', 0),
                        'bd_name': relationship.get('user_a_bd', ''),
                        'agent_flag': '未知'
                    }
                    user_b_data = {
                        'digital_id': user_b_id,
                        'user_agent_level': relationship.get('user_b_level', 0),
                        'bd_name': relationship.get('user_b_bd', ''),
                        'agent_flag': '未知'
                    }
                
                # 分析层级
                if level_analyzer:
                    level_a_result = level_analyzer.analyze(user_a_data)
                    level_b_result = level_analyzer.analyze(user_b_data)
                    level_a = level_a_result.get('level_number', 0)
                    level_b = level_b_result.get('level_number', 0)
                else:
                    level_a = user_a_data.get('user_agent_level', 0)
                    level_b = user_b_data.get('user_agent_level', 0)
                
                # 分析关系
                relationship_analysis = relationship_analyzer.analyze_relationship(
                    level_a, level_b, user_a_data, user_b_data
                )
                
                # 合并结果
                enhanced_relationship = relationship.copy()
                enhanced_relationship.update({
                    'user_a_analyzed_level': level_a,
                    'user_b_analyzed_level': level_b,
                    'relationship_type': relationship_analysis.get('relationship_type'),
                    'level_difference': relationship_analysis.get('level_difference'),
                    'risk_factor': relationship_analysis.get('risk_factor'),
                    'system_cross': relationship_analysis.get('system_cross'),
                    'hierarchy_direction': relationship_analysis.get('hierarchy_direction'),
                    'relationship_description': relationship_analysis.get('description'),
                    'relationship_risk_score': 0.0,  # 默认无风险
                    'relationship_risk_level': 'very_low'  # 默认极低风险
                })
                
                enhanced_relationships.append(enhanced_relationship)
                
            except Exception as e:
                logger.error(f"关系分析失败: {e}")
                # 保留原始关系数据
                enhanced_relationships.append(relationship)
        
        logger.info(f"关系分析完成，处理了{len(enhanced_relationships)}个关系对")
        
        return enhanced_relationships
        
    except Exception as e:
        logger.error(f"关系分析失败: {e}")
        return shared_data


def generate_level_analysis_report(df: pd.DataFrame) -> Dict[str, Any]:
    """
    生成层级分析报告
    
    参数:
        df: 包含层级分析结果的数据框
        
    返回:
        层级分析报告
    """
    try:
        if df.empty:
            return {'error': '数据框为空'}
        
        report = {
            'total_users': len(df),
            'analysis_time': pd.Timestamp.now().isoformat()
        }
        
        # 层级分布统计
        if 'analyzed_level_type' in df.columns:
            level_distribution = df['analyzed_level_type'].value_counts().to_dict()
            report['level_distribution'] = level_distribution
        
        # 风险分布统计
        if 'risk_level' in df.columns:
            risk_distribution = df['risk_level'].value_counts().to_dict()
            report['risk_distribution'] = risk_distribution
            
            # 高风险用户
            high_risk_users = df[df['risk_level'] == 'high']
            report['high_risk_count'] = len(high_risk_users)
            report['high_risk_ratio'] = len(high_risk_users) / len(df) if len(df) > 0 else 0
        
        # BD分布统计
        if 'bd_name' in df.columns:
            bd_distribution = df['bd_name'].value_counts().to_dict()
            report['bd_distribution'] = bd_distribution
        
        # 分析方法统计
        if 'analysis_method' in df.columns:
            method_distribution = df['analysis_method'].value_counts().to_dict()
            report['analysis_method_distribution'] = method_distribution
        
        # 风险评分已禁用，设置为默认值
        report['average_risk_score'] = 0.0
        report['max_risk_score'] = 0.0
        report['min_risk_score'] = 0.0
        
        return report
        
    except Exception as e:
        logger.error(f"生成层级分析报告失败: {e}")
        return {'error': str(e)}


def filter_bd_relationships(df: pd.DataFrame, exclude_direct_customers: bool = True, include_direct_customers: bool = False) -> pd.DataFrame:
    """
    根据记忆中的BD用户识别规则过滤关系数据
    
    参数:
        df: 包含用户数据的DataFrame
        exclude_direct_customers: 是否排除直客用户
        include_direct_customers: 是否包含直客体系用户（层级11+）
        
    返回:
        过滤后的DataFrame，包含BD推荐体系的用户和直客体系用户（如果启用）
    """
    try:
        if df.empty:
            return df

        filtered_df = df.copy()
        
        # 根据记忆中的规则识别BD用户
        # BD 用户识别规则：1) 没有 recommender_digital_id（因为BD是源头）；2) agent_flag 不是 '直客'；3) 可能有 user_agent_level = 1 或者通过排除法识别
        
        if exclude_direct_customers:
            # 排除直客用户（agent_flag = '直客' 且 level_number >= 11）
            # 检查是否存在analyzed_level_number列
            if 'analyzed_level_number' in filtered_df.columns:
                bd_condition = (
                    (filtered_df['agent_flag'] != '直客') |  # 不是直客
                    (filtered_df['analyzed_level_number'] < 11)  # 或者层级数字小于11（不在直客体系）
                )
            else:
                # 如果没有analyzed_level_number列，只根据agent_flag过滤
                bd_condition = (filtered_df['agent_flag'] != '直客')
            
            filtered_df = filtered_df[bd_condition]
            
            logger.info(f"排除直客后剩余{len(filtered_df)}条记录")
        
        # 根据include_direct_customers参数决定层级过滤范围
        if 'analyzed_level_number' in filtered_df.columns:
            if include_direct_customers:
                # 包含直客模式：BD体系（1-4层级）+ 直客体系（11+层级）
                system_condition = (
                    ((filtered_df['analyzed_level_number'] >= 1) & (filtered_df['analyzed_level_number'] <= 4)) |  # BD体系
                    (filtered_df['analyzed_level_number'] >= 11)  # 直客体系
                )
                logger.info(f"包含直客模式：保留BD体系（1-4层级）和直客体系（11+层级）用户")
            else:
                # 传统模式：只保留BD体系（1-4层级）
                system_condition = (
                    (filtered_df['analyzed_level_number'] >= 1) & 
                    (filtered_df['analyzed_level_number'] <= 4)
                )
                logger.info(f"传统模式：只保留BD体系（1-4层级）用户")
            
            filtered_df = filtered_df[system_condition]
            
            logger.info(f"层级过滤后剩余{len(filtered_df)}条记录")
        
        return filtered_df
        
    except Exception as e:
        logger.error(f"BD关系过滤失败: {e}")
        return df


def build_bd_pyramid_structure(df: pd.DataFrame, include_direct_customer_trees: bool = False, include_isolated_dc_stats: bool = False) -> Dict[str, Any]:
    """
    构建BD为起点的金字塔链路图结构
    
    参数:
        df: 包含用户层级分析结果的DataFrame
        include_direct_customer_trees: 是否包含有下级的直客树结构
        include_isolated_dc_stats: 是否包含孤立直客的统计信息
        
    返回:
        金字塔结构的字典，包含所有BD树和直客树（如果启用）
    """
    try:
        if df.empty:
            return {'bd_trees': [], 'error': '数据为空'}
        
        logger.info(f"开始构建BD金字塔结构，输入数据{len(df)}条记录")
        
        # 创建用户字典，去重合并相同digital_id的记录
        user_dict = {}
        for _, row in df.iterrows():
            digital_id = row.get('digital_id', '')
            
            if digital_id in user_dict:
                # 已存在该用户，合并设备和IP信息
                existing_user = user_dict[digital_id]
                
                # 合并设备ID（用集合去重）
                existing_devices = set(existing_user.get('device_ids', []))
                new_device = row.get('device_id', '')
                if new_device:
                    existing_devices.add(new_device)
                existing_user['device_ids'] = list(existing_devices)
                
                # 合并IP（用集合去重）
                existing_ips = set(existing_user.get('ips', []))
                new_ip = row.get('ip', '')
                if new_ip:
                    existing_ips.add(new_ip)
                existing_user['ips'] = list(existing_ips)
                
                # 更新最新的用户名等信息（保留最新的记录信息）
                if row.get('user_name', ''):
                    existing_user['user_name'] = row.get('user_name', '')
                
            else:
                # 新用户，创建记录
                # member_id和digital_id应该是独立的字段，不需要相互替换
                member_id = row.get('member_id', '')  # 保持原始的member_id，可以为空
                
                user_data = {
                    'digital_id': digital_id,
                    'member_id': member_id,
                    'user_name': row.get('user_name', ''),
                    'bd_name': row.get('bd_name', ''),
                    'agent_flag': row.get('agent_flag', ''),
                    'user_agent_level': row.get('user_agent_level', 0),
                    'analyzed_level_type': row.get('analyzed_level_type', '未知'),
                    'analyzed_level_number': row.get('analyzed_level_number', 0),
                    'recommender_digital_id': row.get('recommender_digital_id', ''),
                    'risk_level': 'very_low',  # 固定为极低风险
                    'risk_score': 0.0,        # 固定为0分
                    'device_ids': [row.get('device_id', '')] if row.get('device_id', '') else [],
                    'ips': [row.get('ip', '')] if row.get('ip', '') else [],
                    'children': []  # 用于存储下级代理
                }
                user_dict[digital_id] = user_data
        
        # 识别BD用户（没有推荐人或推荐人为空，且不是直客）
        bd_users = []
        for user_id, user_data in user_dict.items():
            recommender_id = user_data.get('recommender_digital_id', '')
            agent_flag = user_data.get('agent_flag', '')
            level_number = user_data.get('analyzed_level_number', 0)
            
            # BD识别规则：没有推荐人（源头），且不是直客，且层级为1
            is_bd = (
                (not recommender_id or recommender_id.strip() == '' or str(recommender_id) == 'nan') and
                agent_flag != '直客' and
                level_number == 1
            )
            
            if is_bd:
                bd_users.append(user_data)
        
        logger.info(f"识别出{len(bd_users)}个BD用户")
        
        # 按BD名称合并相同的BD
        bd_by_name = {}
        for bd_user in bd_users:
            bd_name = bd_user.get('bd_name', '')
            if bd_name in bd_by_name:
                # 合并相同名称的BD
                existing_bd = bd_by_name[bd_name]
                
                # 合并digital_ids
                existing_bd['digital_ids'] = existing_bd.get('digital_ids', [existing_bd['digital_id']])
                if bd_user['digital_id'] not in existing_bd['digital_ids']:
                    existing_bd['digital_ids'].append(bd_user['digital_id'])
                
                # 合并设备和IP
                existing_devices = set(existing_bd.get('device_ids', []))
                existing_devices.update(bd_user.get('device_ids', []))
                existing_bd['device_ids'] = list(existing_devices)
                
                existing_ips = set(existing_bd.get('ips', []))
                existing_ips.update(bd_user.get('ips', []))
                existing_bd['ips'] = list(existing_ips)
                
                # 更新用户名（如果新的有名字而旧的没有）
                if bd_user.get('user_name', '') and not existing_bd.get('user_name', ''):
                    existing_bd['user_name'] = bd_user['user_name']
                
                # 保留member_id（优先使用已有的，如果没有则使用新的）
                if not existing_bd.get('member_id', '') and bd_user.get('member_id', ''):
                    existing_bd['member_id'] = bd_user['member_id']
                
            else:
                # 新的BD名称
                bd_user['digital_ids'] = [bd_user['digital_id']]
                bd_by_name[bd_name] = bd_user
        
        merged_bd_users = list(bd_by_name.values())
        logger.info(f"合并后BD数量: {len(merged_bd_users)} (原始{len(bd_users)}个)")
        
        # 构建每个合并后BD的推荐树
        bd_trees = []
        for bd_user in merged_bd_users:
            bd_tree = build_merged_recommendation_tree(bd_user, user_dict)
            bd_trees.append(bd_tree)
        
        # 如果启用直客树分析，构建有下级的直客树
        direct_customer_trees = []
        if include_direct_customer_trees:
            direct_customer_users = []
            
            # 识别直客用户（agent_flag = '直客' 且 level_number >= 11）
            for user_id, user_data in user_dict.items():
                agent_flag = user_data.get('agent_flag', '')
                level_number = user_data.get('analyzed_level_number', 0)
                recommender_id = user_data.get('recommender_digital_id', '')
                
                # 直客识别规则：agent_flag = '直客' 且 level_number >= 11，且没有推荐人（是源头）
                is_direct_customer_root = (
                    agent_flag == '直客' and
                    level_number >= 11 and
                    (not recommender_id or recommender_id.strip() == '' or str(recommender_id) == 'nan')
                )
                
                if is_direct_customer_root:
                    direct_customer_users.append(user_data)
            
            logger.info(f"识别出{len(direct_customer_users)}个潜在直客根用户")
            
            # 为每个直客根用户构建推荐树，但只保留有下级的
            for dc_user in direct_customer_users:
                dc_tree = build_recommendation_tree(dc_user, user_dict)
                
                # 只保留有下级的直客树（排除单一直客）
                if dc_tree.get('children') and len(dc_tree['children']) > 0:
                    # 标记这是直客树
                    dc_tree['user_info']['tree_type'] = '直客推荐体系'
                    dc_tree['user_info']['is_direct_customer_root'] = True
                    direct_customer_trees.append(dc_tree)
            
            logger.info(f"构建了{len(direct_customer_trees)}个有下级的直客树")
        
        # 孤立直客统计（如果启用）
        isolated_dc_stats = None
        if include_isolated_dc_stats:
            isolated_dc_users = []
            
            # 识别孤立直客用户（没有下级的直客根）
            for user_id, user_data in user_dict.items():
                agent_flag = user_data.get('agent_flag', '')
                level_number = user_data.get('analyzed_level_number', 0)
                recommender_id = user_data.get('recommender_digital_id', '')
                
                # 孤立直客识别规则：agent_flag = '直客' 且 level_number >= 11，且没有推荐人（是源头）
                is_isolated_dc = (
                    agent_flag == '直客' and
                    level_number >= 11 and
                    (not recommender_id or recommender_id.strip() == '' or str(recommender_id) == 'nan')
                )
                
                if is_isolated_dc:
                    # 检查是否有下级，如果没有下级则是孤立直客
                    has_children = False
                    for other_id, other_data in user_dict.items():
                        other_recommender = other_data.get('recommender_digital_id', '')
                        if str(other_recommender) == str(user_id) and other_id != user_id:
                            has_children = True
                            break
                    
                    if not has_children:
                        isolated_dc_users.append(user_data)
            
            # 生成孤立直客统计
            isolated_dc_stats = {
                'total_isolated_dc_count': len(isolated_dc_users),
                'level_distribution': {},
                'risk_distribution': {},
                'device_ip_stats': {
                    'total_devices': 0,
                    'total_ips': 0,
                    'unique_devices': set(),
                    'unique_ips': set()
                }
            }
            
            # 统计层级和风险分布
            for dc_user in isolated_dc_users:
                level = dc_user.get('analyzed_level_number', 'unknown')
                risk = dc_user.get('risk_level', 'unknown')
                
                isolated_dc_stats['level_distribution'][level] = isolated_dc_stats['level_distribution'].get(level, 0) + 1
                isolated_dc_stats['risk_distribution'][risk] = isolated_dc_stats['risk_distribution'].get(risk, 0) + 1
                
                # 设备和IP统计
                device_ids = dc_user.get('device_ids', [])
                ips = dc_user.get('ips', [])
                
                isolated_dc_stats['device_ip_stats']['unique_devices'].update(device_ids)
                isolated_dc_stats['device_ip_stats']['unique_ips'].update(ips)
            
            # 转换集合为数量
            isolated_dc_stats['device_ip_stats']['total_devices'] = len(isolated_dc_stats['device_ip_stats']['unique_devices'])
            isolated_dc_stats['device_ip_stats']['total_ips'] = len(isolated_dc_stats['device_ip_stats']['unique_ips'])
            del isolated_dc_stats['device_ip_stats']['unique_devices']
            del isolated_dc_stats['device_ip_stats']['unique_ips']
            
            logger.info(f"统计了{len(isolated_dc_users)}个孤立直客用户")
        
        # 统计信息
        total_users_in_bd_trees = sum(count_tree_nodes(tree) for tree in bd_trees)
        total_users_in_dc_trees = sum(count_tree_nodes(tree) for tree in direct_customer_trees)
        total_users_in_trees = total_users_in_bd_trees + total_users_in_dc_trees
        
        pyramid_data = {
            'bd_trees': bd_trees,
            'direct_customer_trees': direct_customer_trees,
            'metadata': {
                'generation_time': pd.Timestamp.now().isoformat(),
                'total_bd_count': len(bd_trees),
                'total_direct_customer_trees': len(direct_customer_trees),
                'total_users_in_bd_pyramid': total_users_in_bd_trees,
                'total_users_in_dc_pyramid': total_users_in_dc_trees,
                'total_users_in_pyramid': total_users_in_trees,
                'original_dataset_size': len(df),
                'data_source': 'BD推荐体系分析' + (' + 直客推荐体系分析' if include_direct_customer_trees else ''),
                'include_direct_customer_trees': include_direct_customer_trees,
                'include_isolated_dc_stats': include_isolated_dc_stats
            }
        }
        
        # 添加孤立直客统计（如果启用）
        if isolated_dc_stats:
            pyramid_data['isolated_direct_customer_stats'] = isolated_dc_stats
        
        logger.info(f"金字塔结构构建完成，BD树{len(bd_trees)}个（用户{total_users_in_bd_trees}人），直客树{len(direct_customer_trees)}个（用户{total_users_in_dc_trees}人）")
        
        return pyramid_data
        
    except Exception as e:
        logger.error(f"构建BD金字塔结构失败: {e}")
        return {'bd_trees': [], 'error': str(e)}


def build_merged_recommendation_tree(root_user: Dict[str, Any], user_dict: Dict[str, Dict]) -> Dict[str, Any]:
    """
    构建合并后BD的推荐树（处理多个digital_id的情况）
    
    参数:
        root_user: 合并后的BD用户数据
        user_dict: 所有用户数据字典
        
    返回:
        完整的推荐树结构
    """
    try:
        # 获取该BD的所有digital_id
        bd_digital_ids = root_user.get('digital_ids', [root_user['digital_id']])
        
        # 查找所有以这些BD ID为推荐人的用户
        direct_children = []
        processed_users = set()  # 避免重复处理相同用户
        
        for bd_id in bd_digital_ids:
            for user_id, user_data in user_dict.items():
                recommender_id = user_data.get('recommender_digital_id', '')
                if str(recommender_id) == str(bd_id) and user_id != bd_id and user_id not in processed_users:
                    # 递归构建子树
                    child_tree = build_recommendation_tree(user_data, user_dict)
                    direct_children.append(child_tree)
                    processed_users.add(user_id)
        
        # 按层级排序子节点
        direct_children.sort(key=lambda x: x.get('user_info', {}).get('analyzed_level_number', 0))
        
        # 构建当前节点的完整信息
        tree_node = {
            'user_info': {
                'digital_id': root_user['digital_id'],  # 主要ID
                'member_id': root_user.get('member_id', root_user['digital_id']),
                'digital_ids': bd_digital_ids,  # 所有ID
                'user_name': root_user['user_name'],
                'bd_name': root_user['bd_name'],
                'agent_flag': root_user['agent_flag'],
                'user_agent_level': root_user['user_agent_level'],
                'analyzed_level_type': root_user['analyzed_level_type'],
                'analyzed_level_number': root_user['analyzed_level_number'],
                'device_count': len(root_user.get('device_ids', [])),
                'ip_count': len(root_user.get('ips', [])),
                'account_count': len(bd_digital_ids),  # 账号数量
                'device_ids': root_user.get('device_ids', []),
                'ips': root_user.get('ips', [])
            },
            'children': direct_children,
            'statistics': {
                'direct_children_count': len(direct_children),
                'total_descendants_count': sum(child.get('statistics', {}).get('total_descendants_count', 0) + 1 
                                             for child in direct_children),
                'level_distribution': {}
            }
        }
        
        # 计算层级分布统计
        level_dist = {}
        for child in direct_children:
            child_level = child.get('user_info', {}).get('analyzed_level_number', 0)
            level_dist[child_level] = level_dist.get(child_level, 0) + 1
            
            # 合并子树的层级分布
            child_level_dist = child.get('statistics', {}).get('level_distribution', {})
            for level, count in child_level_dist.items():
                level_dist[level] = level_dist.get(level, 0) + count
        
        tree_node['statistics']['level_distribution'] = level_dist
        
        return tree_node
        
    except Exception as e:
        logger.error(f"构建合并推荐树失败: {e}")
        return {
            'user_info': root_user,
            'children': [],
            'statistics': {'error': str(e)}
        }


def build_recommendation_tree(root_user: Dict[str, Any], user_dict: Dict[str, Dict]) -> Dict[str, Any]:
    """
    递归构建单个用户的推荐树
    
    参数:
        root_user: 根用户数据
        user_dict: 所有用户数据字典
        
    返回:
        完整的推荐树结构
    """
    try:
        root_id = root_user['digital_id']
        
        # 查找直接下级（推荐人是当前用户的用户）
        direct_children = []
        for user_id, user_data in user_dict.items():
            recommender_id = user_data.get('recommender_digital_id', '')
            if str(recommender_id) == str(root_id) and user_id != root_id:
                # 递归构建子树
                child_tree = build_recommendation_tree(user_data, user_dict)
                direct_children.append(child_tree)
        
        # 按层级排序子节点
        direct_children.sort(key=lambda x: x.get('analyzed_level_number', 0))
        
        # 构建当前节点的完整信息
        tree_node = {
            'user_info': {
                'digital_id': root_user['digital_id'],
                'member_id': root_user.get('member_id', root_user['digital_id']),
                'user_name': root_user['user_name'],
                'bd_name': root_user['bd_name'],
                'agent_flag': root_user['agent_flag'],
                'user_agent_level': root_user['user_agent_level'],
                'analyzed_level_type': root_user['analyzed_level_type'],
                'analyzed_level_number': root_user['analyzed_level_number'],
                'device_count': len(root_user.get('device_ids', [])),
                'ip_count': len(root_user.get('ips', [])),
                'device_ids': root_user.get('device_ids', []),
                'ips': root_user.get('ips', [])
            },
            'children': direct_children,
            'statistics': {
                'direct_children_count': len(direct_children),
                'total_descendants_count': sum(child.get('statistics', {}).get('total_descendants_count', 0) + 1 
                                             for child in direct_children),
                'level_distribution': {}
            }
        }
        
        # 计算层级分布统计
        level_dist = {}
        for child in direct_children:
            child_level = child.get('user_info', {}).get('analyzed_level_number', 0)
            level_dist[child_level] = level_dist.get(child_level, 0) + 1
            
            # 合并子树的层级分布
            child_level_dist = child.get('statistics', {}).get('level_distribution', {})
            for level, count in child_level_dist.items():
                level_dist[level] = level_dist.get(level, 0) + count
        
        tree_node['statistics']['level_distribution'] = level_dist
        
        return tree_node
        
    except Exception as e:
        logger.error(f"构建推荐树失败: {e}")
        return {
            'user_info': root_user,
            'children': [],
            'statistics': {'error': str(e)}
        }


def count_tree_nodes(tree: Dict[str, Any]) -> int:
    """
    递归计算树中的节点总数
    
    参数:
        tree: 树结构字典
        
    返回:
        节点总数
    """
    try:
        count = 1  # 当前节点
        children = tree.get('children', [])
        for child in children:
            count += count_tree_nodes(child)
        return count
    except Exception as e:
        logger.error(f"计算树节点数失败: {e}")
        return 1


def generate_pyramid_statistics(pyramid_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成金字塔结构的统计信息
    
    参数:
        pyramid_data: 金字塔数据结构
        
    返回:
        统计信息字典
    """
    try:
        bd_trees = pyramid_data.get('bd_trees', [])
        direct_customer_trees = pyramid_data.get('direct_customer_trees', [])
        
        if not bd_trees and not direct_customer_trees:
            return {
                'total_bd_trees': 0,
                'total_direct_customer_trees': 0,
                'total_users': 0,
                'error': '没有树数据'
            }
        
        # 基础统计
        total_bd_trees = len(bd_trees)
        total_direct_customer_trees = len(direct_customer_trees)
        total_bd_users = sum(count_tree_nodes(tree) for tree in bd_trees)
        total_dc_users = sum(count_tree_nodes(tree) for tree in direct_customer_trees)
        total_users = total_bd_users + total_dc_users
        
        # BD统计
        bd_statistics = []
        overall_level_distribution = {}
        
        for i, bd_tree in enumerate(bd_trees):
            bd_info = bd_tree.get('user_info', {})
            bd_stats = bd_tree.get('statistics', {})
            
            bd_stat = {
                'bd_rank': i + 1,
                'bd_name': bd_info.get('bd_name', ''),
                'bd_digital_id': bd_info.get('digital_id', ''),
                'bd_user_name': bd_info.get('user_name', ''),
                'total_team_size': count_tree_nodes(bd_tree),
                'direct_agents_count': bd_stats.get('direct_children_count', 0),
                'total_descendants_count': bd_stats.get('total_descendants_count', 0),
                'level_distribution': bd_stats.get('level_distribution', {}),
                'bd_device_count': bd_info.get('device_count', 0),
                'bd_ip_count': bd_info.get('ip_count', 0),
                'bd_account_count': bd_info.get('account_count', 1),
                'bd_all_digital_ids': bd_info.get('digital_ids', [bd_info.get('digital_id', '')])
            }
            bd_statistics.append(bd_stat)
            
            # 合并到总体分布
            level_dist = bd_stats.get('level_distribution', {})
            for level, count in level_dist.items():
                overall_level_distribution[level] = overall_level_distribution.get(level, 0) + count
        
        # 直客统计
        dc_statistics = []
        dc_level_distribution = {}
        
        for i, dc_tree in enumerate(direct_customer_trees):
            dc_info = dc_tree.get('user_info', {})
            dc_stats = dc_tree.get('statistics', {})
            
            dc_stat = {
                'dc_rank': i + 1,
                'dc_name': dc_info.get('user_name', ''),
                'dc_digital_id': dc_info.get('digital_id', ''),
                'total_team_size': count_tree_nodes(dc_tree),
                'direct_agents_count': dc_stats.get('direct_children_count', 0),
                'total_descendants_count': dc_stats.get('total_descendants_count', 0),
                'level_distribution': dc_stats.get('level_distribution', {}),
                'dc_device_count': dc_info.get('device_count', 0),
                'dc_ip_count': dc_info.get('ip_count', 0),
                'tree_type': '直客推荐体系'
            }
            dc_statistics.append(dc_stat)
            
            # 合并到直客总体分布
            level_dist = dc_stats.get('level_distribution', {})
            for level, count in level_dist.items():
                dc_level_distribution[level] = dc_level_distribution.get(level, 0) + count
        
        # 按团队规模排序BD
        bd_statistics.sort(key=lambda x: x['total_team_size'], reverse=True)
        for i, bd_stat in enumerate(bd_statistics):
            bd_stat['team_size_rank'] = i + 1
        
        # 按团队规模排序直客
        dc_statistics.sort(key=lambda x: x['total_team_size'], reverse=True)
        for i, dc_stat in enumerate(dc_statistics):
            dc_stat['team_size_rank'] = i + 1
        
        # 计算BD平均值
        bd_team_sizes = [bd['total_team_size'] for bd in bd_statistics]
        avg_bd_team_size = sum(bd_team_sizes) / len(bd_team_sizes) if bd_team_sizes else 0
        
        # 计算直客平均值
        dc_team_sizes = [dc['total_team_size'] for dc in dc_statistics]
        avg_dc_team_size = sum(dc_team_sizes) / len(dc_team_sizes) if dc_team_sizes else 0
        
        # 构建返回结果
        result = {
            'total_bd_trees': total_bd_trees,
            'total_direct_customer_trees': total_direct_customer_trees,
            'total_users': total_users,
            'total_bd_users': total_bd_users,
            'total_dc_users': total_dc_users,
            'overall_level_distribution': overall_level_distribution
        }
        
        # BD相关统计
        if bd_statistics:
            result.update({
                'average_bd_team_size': round(avg_bd_team_size, 2),
                'largest_bd_team_size': max(bd_team_sizes) if bd_team_sizes else 0,
                'smallest_bd_team_size': min(bd_team_sizes) if bd_team_sizes else 0,
                'bd_rankings': {
                    'by_team_size': bd_statistics[:10]  # 前10名
                },
                'bd_detailed_statistics': bd_statistics
            })
        
        # 直客相关统计
        if dc_statistics:
            result.update({
                'average_dc_team_size': round(avg_dc_team_size, 2),
                'largest_dc_team_size': max(dc_team_sizes) if dc_team_sizes else 0,
                'smallest_dc_team_size': min(dc_team_sizes) if dc_team_sizes else 0,
                'dc_level_distribution': dc_level_distribution,
                'dc_rankings': {
                    'by_team_size': dc_statistics[:10]  # 前10名
                },
                'dc_detailed_statistics': dc_statistics
            })
        
        return result
        
    except Exception as e:
        logger.error(f"生成金字塔统计信息失败: {e}")
        return {
            'total_bd_trees': 0,
            'total_direct_customer_trees': 0,
            'total_users': 0,
            'error': str(e)
        }