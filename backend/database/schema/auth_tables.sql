-- 鉴权系统数据库表结构
-- 必须严格按照此结构创建

-- 1. 鉴权用户表（必须严格按照此结构）
CREATE SEQUENCE IF NOT EXISTS auth_users_id_seq;
CREATE TABLE IF NOT EXISTS auth_users (
    id INTEGER PRIMARY KEY DEFAULT nextval('auth_users_id_seq'),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('viewer', 'admin')),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP NULL
);

-- 2. 会话表（必须严格按照此结构）
CREATE TABLE IF NOT EXISTS auth_user_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE
);

-- 3. 操作日志表（必须记录所有关键操作）
CREATE SEQUENCE IF NOT EXISTS auth_activity_logs_id_seq;
CREATE TABLE IF NOT EXISTS auth_user_activity_logs (
    id INTEGER PRIMARY KEY DEFAULT nextval('auth_activity_logs_id_seq'),
    user_id INTEGER NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(200),
    ip_address VARCHAR(45),
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. 系统配置表（会话超时等配置）
CREATE TABLE IF NOT EXISTS auth_system_config (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认系统配置
INSERT OR REPLACE INTO auth_system_config (key, value, description) VALUES
('session_timeout_hours', '24', '会话超时时间（小时）'),
('max_login_attempts', '5', '最大登录尝试次数'),
('account_lock_minutes', '30', '账户锁定时间（分钟）'),
('password_min_length', '6', '密码最小长度'),
('require_password_change', 'false', '是否要求定期修改密码');

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_auth_users_username ON auth_users(username);
CREATE INDEX IF NOT EXISTS idx_auth_users_role ON auth_users(role);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_user_id ON auth_user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_sessions_expires_at ON auth_user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_auth_activity_logs_user_id ON auth_user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_auth_activity_logs_created_at ON auth_user_activity_logs(created_at); 