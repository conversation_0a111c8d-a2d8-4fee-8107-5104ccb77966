<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>鉴权测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔐 鉴权系统测试页面</h1>
    
    <div class="test-container">
        <h2>1. 检查鉴权API响应</h2>
        <button class="test-button" onclick="testAuthCheck()">测试 /api/auth/check</button>
        <div id="authCheckResult" class="result"></div>
    </div>
    
    <div class="test-container">
        <h2>2. 测试受保护的API</h2>
        <button class="test-button" onclick="testProtectedAPI()">测试 /api/contract/algorithms</button>
        <div id="protectedAPIResult" class="result"></div>
    </div>
    
    <div class="test-container">
        <h2>3. 测试鉴权工具加载</h2>
        <button class="test-button" onclick="testAuthUtils()">测试 AuthUtils 加载</button>
        <div id="authUtilsResult" class="result"></div>
    </div>
    
    <div class="test-container">
        <h2>4. 手动触发鉴权检查</h2>
        <button class="test-button" onclick="manualAuthCheck()">手动鉴权检查</button>
        <div id="manualAuthResult" class="result"></div>
    </div>
    
    <div class="test-container">
        <h2>5. 控制台日志</h2>
        <p>请打开浏览器开发者工具的控制台查看详细日志</p>
    </div>

    <script>
        // 测试鉴权检查API
        async function testAuthCheck() {
            const resultDiv = document.getElementById('authCheckResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('/api/auth/check', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试受保护的API
        async function testProtectedAPI() {
            const resultDiv = document.getElementById('protectedAPIResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                const response = await fetch('/api/contract/algorithms', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                resultDiv.textContent = `状态: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
            } catch (error) {
                resultDiv.textContent = `错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 测试AuthUtils加载
        async function testAuthUtils() {
            const resultDiv = document.getElementById('authUtilsResult');
            resultDiv.textContent = '正在测试...';
            
            try {
                // 尝试从window获取
                let AuthUtils = window.AuthUtils;
                let source = 'window.AuthUtils';
                
                if (!AuthUtils) {
                    // 尝试动态导入
                    const module = await import('../shared/utils/auth-utils.js');
                    AuthUtils = module.default || module.AuthUtils;
                    source = 'ES6 import';
                }
                
                if (AuthUtils) {
                    resultDiv.textContent = `✅ AuthUtils 加载成功！\n来源: ${source}\n方法: ${Object.getOwnPropertyNames(AuthUtils).filter(name => typeof AuthUtils[name] === 'function').join(', ')}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = '❌ AuthUtils 加载失败';
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 加载错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 手动鉴权检查
        async function manualAuthCheck() {
            const resultDiv = document.getElementById('manualAuthResult');
            resultDiv.textContent = '正在检查...';
            
            try {
                // 获取AuthUtils
                let AuthUtils = window.AuthUtils;
                if (!AuthUtils) {
                    const module = await import('../shared/utils/auth-utils.js');
                    AuthUtils = module.default || module.AuthUtils;
                }
                
                if (!AuthUtils) {
                    throw new Error('AuthUtils 未找到');
                }
                
                console.log('开始手动鉴权检查...');
                const user = await AuthUtils.guardPage();
                
                if (user) {
                    resultDiv.textContent = `✅ 用户已登录\n用户: ${user.username}\n角色: ${user.role}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = '❌ 用户未登录，应该已重定向到登录页面';
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.textContent = `❌ 检查错误: ${error.message}`;
                resultDiv.className = 'result error';
                console.error('手动鉴权检查错误:', error);
            }
        }
        
        // 页面加载时自动运行一些测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 鉴权测试页面已加载');
            console.log('📍 当前URL:', window.location.href);
            console.log('📁 当前路径:', window.location.pathname);
            
            // 自动测试API
            testAuthCheck();
        });
    </script>
</body>
</html> 