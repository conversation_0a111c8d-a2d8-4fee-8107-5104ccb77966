<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>链路风险分析系统</title>
  <style>
/* 链路风险分析系统样式表 - 共享样式 */

/* 导航栏样式 */
.navbar-brand {
  font-weight: bold;
}

/* 卡片样式优化 */
.card {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: none;
  margin-bottom: 1.5rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  font-weight: 600;
}

/* 图表容器样式 */
.chart-container {
  height: 100%;
  min-height: 300px;
}

/* 风险等级样式 */
.risk-high {
  color: #dc3545;
}

.risk-medium {
  color: #fd7e14;
}

.risk-low {
  color: #20c997;
}

/* 关系图特定样式 */
#crossBDRelationshipChart {
  border: 1px solid #eee;
  border-radius: 4px;
}

/* 风险详情模态框内部样式 */
.risk-detail-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.risk-detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.risk-detail-section h6 {
  font-weight: 600;
  margin-bottom: 1rem;
}

.evidence-item {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

.evidence-item:last-child {
  margin-bottom: 0;
}

/* 网络图提示框样式 */
.network-tooltip {
  padding: 8px 12px;
  font-size: 14px;
}

.network-tooltip .title {
  font-weight: bold;
  margin-bottom: 5px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 5px;
}

.risk-title {
  color: #dc3545;
}

.tooltip-divider {
  margin: 5px 0;
  border-top: 1px dashed #ccc;
}

.tooltip-label {
  font-weight: 600;
  display: inline-block;
  min-width: 70px;
}

.tip {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
  margin-top: 5px;
}
</style>
  <style>
/* Bootstrap Minimal - 基础样式替代 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1rem;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
}

.container, .container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

.container {
    max-width: 1140px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col, .col-lg-6, .col-md-12 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

.col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
}

.col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,0.125);
    border-radius: 0.375rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0,0,0,0.03);
    border-bottom: 1px solid rgba(0,0,0,0.125);
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-3 {
    margin-bottom: 1rem;
}

.mb-0 {
    margin-bottom: 0;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;
    background-color: transparent;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

.btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.075);
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
}

.nav {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.nav-link {
    display: block;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: #0d6efd;
    border: 1px solid transparent;
}

.nav-tabs .nav-link {
    margin-bottom: -1px;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    margin-top: 1rem;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane.show {
    display: block;
}

.form-select {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
}

.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
    background-color: rgba(0,0,0,0.5);
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,0.2);
    border-radius: 0.3rem;
    outline: 0;
}

.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
}

.bi {
    display: inline-block;
    width: 1em;
    height: 1em;
    vertical-align: -0.125em;
    margin-right: 0.25rem;
}

.bi::before {
    content: "●";
    color: #6c757d;
}

.bi-diagram-3::before { content: "📊"; }
.bi-bar-chart::before { content: "📈"; }
.bi-shuffle::before { content: "🔀"; }
.bi-thermometer-half::before { content: "🌡️"; }
.bi-clock-history::before { content: "🕐"; }
.bi-play-circle::before { content: "▶️"; }
.bi-exclamation-circle::before { content: "⚠️"; }
.bi-info-circle::before { content: "ℹ️"; }
.bi-building::before { content: "🏢"; }
.bi-exclamation-triangle::before { content: "⚠️"; }

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.text-center {
    text-align: center;
}

.w-100 {
    width: 100%;
}

.h-100 {
    height: 100%;
}

@media (max-width: 768px) {
    .col-lg-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .container {
        max-width: 100%;
        padding-right: 10px;
        padding-left: 10px;
    }
}
  </style>
  <link rel="stylesheet" href="../styles/contract-integration.css">
</head>
<body>
  <!-- 导航栏容器 -->
  <div id="navigationContainer"></div>

  <!-- 主内容区 -->
  <div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1 class="mb-0">
        <i class="bi bi-graph-up"></i> 链路风险分析系统
      </h1>
      <div class="text-muted">
        <i class="bi bi-bezier2"></i> 分析跨团队交易链路风险
      </div>
    </div>

    <!-- 任务选择区 -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="bi bi-gear"></i> 数据源配置 (持久化存储)
        </h5>
      </div>
      <div class="card-body">
        <!-- 加载状态指示器 -->
        <div id="integrationStatus" class="alert alert-info" style="display: none;"></div>
        
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="contractTaskSelector" class="form-label">
                <i class="bi bi-file-earmark-text"></i> 选择合约分析任务
              </label>
              <select class="form-select" id="contractTaskSelector" onchange="contractIntegrationService.onContractTaskChange(this.value)">
                <option value="">正在加载合约分析任务...</option>
              </select>
              <div class="form-text">从持久化存储中选择已完成的合约风险分析任务</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="agentTaskSelector" class="form-label">
                <i class="bi bi-people"></i> 选择代理商分析任务
              </label>
              <select class="form-select" id="agentTaskSelector" onchange="contractIntegrationService.onAgentTaskChange(this.value)">
                <option value="">正在加载代理商分析任务...</option>
              </select>
              <div class="form-text">从持久化存储中选择已完成的代理商关系分析任务</div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-12 d-flex align-items-center justify-content-end">
            <div class="btn-group">
              <button id="refreshTasksBtn" class="btn btn-outline-secondary" onclick="contractIntegrationService.refreshTasks()">
                <i class="bi bi-arrow-clockwise"></i> 刷新
              </button>
              <button id="startIntegrationBtn" class="btn btn-primary" onclick="contractIntegrationService.handleStartIntegration()">
                <i class="bi bi-play-circle"></i> 开始整合分析
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- 加载状态 -->
    <div id="loadingIndicator" class="card mb-4" style="display: none;">
      <div class="card-body text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <div class="mt-3">
          <h6>正在整合合约风险分析与BD关系数据...</h6>
          <p class="text-muted mb-0">这可能需要几分钟时间，请耐心等待</p>
        </div>
      </div>
    </div>

    <!-- 分析结果区域 -->
    <div id="analysisResults" style="display: none;">
      
      <!-- 概览统计 -->
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="bi bi-speedometer2"></i> 整合分析概览
          </h5>
          <div id="dataSourceIndicator" class="badge bg-secondary">
            <i class="bi bi-database"></i> 数据源: 检查中...
          </div>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-md-3">
              <div class="border-end">
                <h3 class="text-danger mb-1" id="totalContractRisks">0</h3>
                <small class="text-muted">合约风险总数</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="border-end">
                <h3 class="text-primary mb-1" id="linkedMembers">0</h3>
                <small class="text-muted">关联用户数</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="border-end">
                <h3 class="text-purple mb-1" id="involvedBDs">0</h3>
                <small class="text-muted">涉及BD数量</small>
              </div>
            </div>
            <div class="col-md-3">
              <h3 class="text-warning mb-1" id="crossBDPatterns">0</h3>
              <small class="text-muted">跨BD风险模式</small>
            </div>
          </div>
        </div>
      </div>

      <!-- BD数据筛选控制区域 -->
      <div class="card mb-4" id="bdFilterCard" style="display: none;">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="bi bi-funnel"></i> BD数据筛选控制
          </h6>
        </div>
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-6">
              <div class="input-group">
                <span class="input-group-text">
                  <i class="bi bi-building"></i>
                </span>
                <select class="form-select" id="bdSelectFilter">
                  <option value="">选择BD团队进行详细查看</option>
                  <!-- 动态填充BD选项 -->
                </select>
                <button class="btn btn-outline-secondary" type="button" id="clearBDFilter">
                  <i class="bi bi-x-circle"></i> 清除
                </button>
              </div>
            </div>

            <div class="col-md-3">
              <div class="btn-group w-100" role="group">
                <button type="button" class="btn btn-outline-primary btn-sm" id="showAllBDs">显示全部</button>
                <button type="button" class="btn btn-outline-success btn-sm" id="showSelectedBD">查看选中BD</button>
              </div>
            </div>
          </div>
          <div class="row mt-3" id="bdFilterStatus" style="display: none;">
            <div class="col-12">
              <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="bi bi-info-circle"></i>
                <span id="bdFilterStatusText">当前筛选状态</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要分析区域 -->
      <!-- BD金字塔风险链路图 - 全宽显示 -->
      <div class="row">
        <div class="col-12">
          <div class="card mb-4">
            <div class="card-header">
              <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                  <i class="bi bi-diagram-3"></i> BD金字塔风险链路图
                </h6>
                <div class="btn-toolbar" role="toolbar">
                  <!-- 缩放控制组 -->
                  <div class="btn-group btn-group-sm me-2" role="group" aria-label="缩放控制">
                    <button type="button" class="btn btn-outline-primary" onclick="contractIntegrationService.resetChartZoom()" title="重置视图">
                      <i class="bi bi-house"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="contractIntegrationService.zoomIn()" title="放大">
                      <i class="bi bi-zoom-in"></i>
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="contractIntegrationService.zoomOut()" title="缩小">
                      <i class="bi bi-zoom-out"></i>
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="contractIntegrationService.fitToContent()" title="适应内容">
                      <i class="bi bi-arrows-fullscreen"></i>
                    </button>
                  </div>
                  <!-- 连接线长度控制组 -->
                  <div class="btn-group btn-group-sm" role="group" aria-label="连接线控制">
                    <button type="button" class="btn btn-outline-secondary" onclick="contractIntegrationService.adjustLineLength('compact')" title="紧凑连接线">
                      <i class="bi bi-arrows-collapse-vertical"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary active" onclick="contractIntegrationService.adjustLineLength('normal')" title="正常连接线">
                      <i class="bi bi-arrows-vertical"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="contractIntegrationService.adjustLineLength('loose')" title="宽松连接线">
                      <i class="bi bi-arrows-expand-vertical"></i>
                    </button>
                  </div>
                </div>
              </div>
              <small class="text-muted">
                <i class="bi bi-info-circle"></i> 
                支持鼠标滚轮缩放、拖拽平移，点击节点查看详情。右上角可调整连接线长度。节点颜色表示风险等级：
                <span class="badge bg-success ms-1">绿色-无风险</span>
                <span class="badge bg-warning ms-1">黄色-低风险</span>
                <span class="badge bg-warning text-dark ms-1">橙色-中风险</span>
                <span class="badge bg-danger ms-1">红色-高风险</span>
              </small>
            </div>
            <div class="card-body p-0">
              <div id="contractBDNetworkChart" style="height: 800px;"></div>
            </div>
          </div>
        </div>
      </div>



      <!-- 详细分析标签页 -->
      <div class="card mb-4">
        <div class="card-header">
          <ul class="nav nav-tabs card-header-tabs" id="detailAnalysisTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="bd-stats-tab" data-bs-toggle="tab" data-bs-target="#bd-stats" type="button" role="tab">
                <i class="bi bi-bar-chart"></i> BD详细统计
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="cross-bd-tab" data-bs-toggle="tab" data-bs-target="#cross-bd" type="button" role="tab">
                <i class="bi bi-shuffle"></i> 跨BD风险模式
              </button>
            </li>

          </ul>
        </div>
        <div class="card-body">
          <div class="tab-content" id="detailAnalysisTabContent">
            
            <!-- BD详细统计 -->
            <div class="tab-pane fade show active" id="bd-stats" role="tabpanel">
              <div class="table-responsive">
                <table class="table table-striped table-hover" id="bdDetailStatsTable">
                  <thead>
                    <tr>
                      <th>BD名称</th>
                      <th>总风险数</th>
                      <th>高风险</th>
                      <th>中风险</th>
                      <th>低风险</th>
                      <th>涉及用户数</th>
                      <th>平均风险评分</th>
                      <th>主要风险类型</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- 动态填充 -->
                  </tbody>
                </table>
              </div>
            </div>

            <!-- 跨BD风险模式 -->
            <div class="tab-pane fade" id="cross-bd" role="tabpanel">
              <div id="crossBDPatternsContainer">
                <!-- 动态填充跨BD风险模式 -->
              </div>
            </div>



          </div>
        </div>
      </div>

    </div>

    <!-- 错误提示 -->
    <div id="errorAlert" class="alert alert-danger" style="display: none;" role="alert">
      <h6 class="alert-heading">
        <i class="bi bi-exclamation-triangle"></i> 分析错误
      </h6>
      <p class="mb-0" id="errorMessage"></p>
      <hr>
      <button type="button" class="btn btn-sm btn-outline-danger" onclick="hideError()">关闭</button>
    </div>

  </div>

  <!-- 风险详情模态框 -->
  <div class="modal fade" id="riskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-info-circle"></i> 风险详情
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="riskDetailContent">
          <!-- 动态填充风险详情 -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- BD详情模态框 -->
  <div class="modal fade" id="bdDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-building"></i> BD团队详情分析
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="bdDetailContent">
          <!-- 动态填充BD详情 -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入必要的JS库 -->
  <script>
/* Bootstrap Minimal JavaScript - 基础功能替代 */
(function() {
    'use strict';

    // 标签页功能
    class Tab {
        constructor(element) {
            this.element = element;
            this.target = document.querySelector(element.getAttribute('data-bs-target'));
            this.init();
        }

        init() {
            this.element.addEventListener('click', (e) => {
                e.preventDefault();
                this.show();
            });
        }

        show() {
            // 隐藏其他标签页内容
            const tabPane = this.target;
            const tabContent = tabPane.parentElement;
            const allPanes = tabContent.querySelectorAll('.tab-pane');
            const allTabs = document.querySelectorAll('[data-bs-toggle="tab"]');
            
            allPanes.forEach(pane => {
                pane.classList.remove('show', 'active');
            });
            
            allTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示当前标签页
            tabPane.classList.add('show', 'active');
            this.element.classList.add('active');

            // 触发自定义事件
            const event = new CustomEvent('shown.bs.tab', {
                detail: { target: this.target }
            });
            this.element.dispatchEvent(event);
        }
    }

    // 模态框功能
    class Modal {
        constructor(element) {
            this.element = element;
            this.init();
        }

        init() {
            // 关闭按钮
            const closeButtons = this.element.querySelectorAll('[data-bs-dismiss="modal"]');
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => this.hide());
            });

            // 背景点击关闭
            this.element.addEventListener('click', (e) => {
                if (e.target === this.element) {
                    this.hide();
                }
            });
        }

        show() {
            this.element.style.display = 'block';
            document.body.style.overflow = 'hidden';
            setTimeout(() => {
                this.element.style.opacity = '1';
            }, 10);
        }

        hide() {
            this.element.style.opacity = '0';
            setTimeout(() => {
                this.element.style.display = 'none';
                document.body.style.overflow = '';
            }, 150);
        }
    }

    // 初始化组件
    function initComponents() {
        // 初始化标签页
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(element => {
            new Tab(element);
        });

        // 初始化模态框
        document.querySelectorAll('.modal').forEach(element => {
            new Modal(element);
        });
    }

    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initComponents);
    } else {
        initComponents();
    }

    // 全局API
    window.bootstrap = {
        Tab: Tab,
        Modal: Modal
    };

})();
  </script>
  <!-- <script src="/dist/js/contractIntegrationPage.js"></script> -->
  

  

</body>
</html> 