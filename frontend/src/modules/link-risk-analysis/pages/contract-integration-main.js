// Main entry point for contract-integration.html page

// Import Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css';
// Import Bootstrap Icons CSS
import 'bootstrap-icons/font/bootstrap-icons.css';

// Import Bootstrap JS
import 'bootstrap'; 

// Import navigation component and styles
import NavigationComponent from '../../../shared/components/navigation.js';
import '../../../shared/css/navigation.css';

// Import ECharts
import * as echarts from 'echarts';
// Make ECharts globally available
window.echarts = echarts;

// Import page-specific styles
import '../styles/contract-integration.css';

// Import service layer
import ContractIntegrationService from '../services/contract-integration-service.js';

// 检查库加载状态
if (typeof echarts !== 'object' || typeof echarts?.init !== 'function') {
    console.error('ECharts库加载失败');
}

// 全局初始化检查函数
window.checkLibraries = function() {
    const hasBootstrap = typeof bootstrap !== 'undefined';
    const hasEcharts = typeof echarts !== 'undefined';
    
    if (!hasBootstrap || !hasEcharts) {
        console.error('必要库加载失败:', { bootstrap: hasBootstrap, echarts: hasEcharts });
        return false;
    }
    
    if (typeof echarts !== 'undefined') {
        try {
            // 创建一个临时div进行测试
            const testDiv = document.createElement('div');
            testDiv.style.width = '100px';
            testDiv.style.height = '100px';
            document.body.appendChild(testDiv);
            
            // 尝试初始化echarts
            const testChart = echarts.init(testDiv);
            testChart.dispose();
            document.body.removeChild(testDiv);
            return true;
        } catch (e) {
            console.error('ECharts初始化测试失败:', e);
            return false;
        }
    }
    return false;
};

// 导入鉴权工具
import '../../../shared/utils/auth-utils.js';

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 链路分析页面开始加载，进行鉴权检查...');
    
    // 1. 检查用户登录状态（必须）
    const user = await AuthUtils.guardPage();
    if (!user) {
        console.log('❌ 用户未登录，已跳转到登录页面');
        return; // 如果未登录，guardPage会自动跳转
    }
    
    // 2. 初始化页面权限控制（必须）
    AuthUtils.initPagePermissions();
    
    console.log('✅ 用户已登录:', user.username, '角色:', user.role);
    
    // 3. 原有的页面初始化逻辑
    // 初始化导航栏
    NavigationComponent.init('navigationContainer');
    
    // 检查库加载状态
    if (!window.checkLibraries()) {
        return;
    }
    
    // 初始化合约整合服务
    if (ContractIntegrationService && typeof ContractIntegrationService.init === 'function') {
        try {
            // 使用async/await处理异步初始化
            ContractIntegrationService.init().catch(error => {
                console.error('合约整合服务初始化失败:', error);
            });
        } catch (error) {
            console.error('合约整合服务初始化失败:', error);
        }
    } else {
        console.error('合约整合服务未找到或init方法不存在');
    }
});

// 调试工具函数
window.debugIntegration = {
    reloadTasks: () => window.contractIntegrationService?.loadAvailableTasks(),
    checkService: () => window.contractIntegrationService
}; 