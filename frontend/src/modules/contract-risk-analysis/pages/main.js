// Main entry point for contract_analysis.html page

// Import Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css';
// Import Bootstrap Icons CSS
import 'bootstrap-icons/font/bootstrap-icons.css';

// Import Element Plus CSS (if used)
import 'element-plus/dist/index.css';

// Import Bootstrap JS (ensure Popper.js is available if not using Bootstrap bundle with Popper)
import 'bootstrap'; 

// Import navigation component and styles
import NavigationComponent from '../../../shared/components/navigation.js';
import '../../../shared/css/navigation.css';

// Import page-specific styles
import '../../../shared/contract-analysis-page-styles.css';

// Import ECharts
import * as echarts from 'echarts';
// Make ECharts globally available if older scripts depend on this
window.echarts = echarts;

// 检查ECharts加载状态
if (typeof echarts !== 'object' || typeof echarts?.init !== 'function') {
    console.error('ECharts库加载失败');
}

// 全局初始化检查函数
window.checkLibraries = function() {
    const hasBootstrap = typeof bootstrap !== 'undefined';
    const hasEcharts = typeof echarts !== 'undefined';
    const hasJQuery = typeof jQuery !== 'undefined';
    
    if (!hasBootstrap || !hasEcharts) {
        console.error('必要库加载失败:', { bootstrap: hasBootstrap, echarts: hasEcharts, jquery: hasJQuery });
        return false;
    }
    
    if (typeof echarts !== 'undefined') {
        try {
            // 创建一个临时div进行测试
            const testDiv = document.createElement('div');
            testDiv.style.width = '100px';
            testDiv.style.height = '100px';
            document.body.appendChild(testDiv);
            
            // 尝试初始化echarts
            const testChart = echarts.init(testDiv);
            testChart.dispose();
            document.body.removeChild(testDiv);
            return true;
        } catch (e) {
            console.error('ECharts初始化测试失败:', e);
            return false;
        }
    }
    return false;
};

// 等待DOM加载完成后执行检查
document.addEventListener('DOMContentLoaded', async () => {
    console.log('🚀 页面开始加载，进行鉴权检查...');
    
    // ========== 鉴权检查（必须第一个执行）==========
    try {
        console.log('📥 尝试导入鉴权工具...');
        
        // 多种方式尝试获取AuthUtils
        let AuthUtils = window.AuthUtils;
        
        if (!AuthUtils) {
            console.log('🔄 从模块导入AuthUtils...');
            try {
                const authUtilsModule = await import('../../../shared/utils/auth-utils.js');
                AuthUtils = authUtilsModule.default || authUtilsModule.AuthUtils;
                console.log('✅ AuthUtils模块导入成功');
            } catch (importError) {
                console.error('❌ AuthUtils模块导入失败:', importError);
                // 尝试直接加载脚本
                const script = document.createElement('script');
                script.src = '/frontend/src/shared/utils/auth-utils.js';
                script.onload = () => {
                    console.log('✅ AuthUtils脚本加载成功');
                    AuthUtils = window.AuthUtils;
                    performAuthCheck(AuthUtils);
                };
                script.onerror = () => {
                    console.error('❌ AuthUtils脚本加载失败');
                    window.location.href = '/login.html';
                };
                document.head.appendChild(script);
                return;
            }
        }
        
        await performAuthCheck(AuthUtils);
        
    } catch (error) {
        console.error('❌ 鉴权检查失败:', error);
        console.log('🔄 直接重定向到登录页面');
        window.location.href = '/login.html';
        return;
    }
});

async function performAuthCheck(AuthUtils) {
    if (!AuthUtils) {
        console.error('❌ AuthUtils未定义，重定向到登录页面');
        window.location.href = '/login.html';
        return;
    }
    
    console.log('🔍 开始检查用户登录状态...');
    
    try {
        // 检查用户登录状态（如果未登录会自动跳转）
        const user = await AuthUtils.guardPage();
        if (!user) {
            console.log('❌ 用户未登录，已触发跳转');
            return; // 如果未登录，guardPage会自动跳转
        }
        
        console.log('✅ 鉴权检查通过:', user.username, '角色:', user.role);
        
        // 初始化页面权限控制
        AuthUtils.initPagePermissions();
        
        // ========== 原有初始化逻辑 ==========
        console.log('🔧 开始初始化页面组件...');
        
        // 初始化导航栏
        NavigationComponent.init('navigationContainer');
        
        // 检查库加载状态
        if (!window.checkLibraries()) {
            console.warn('⚠️ 必要库加载失败');
            return;
        }
        
        console.log('🎉 页面初始化完成');
        
    } catch (error) {
        console.error('❌ 鉴权检查过程中出错:', error);
        window.location.href = '/login.html';
    }
}

// Import page-specific JavaScript
import '../services/contract-analysis-service.js';

// Import Element Plus JS (if used)
import ElementPlus from 'element-plus';
// If you are using Vue, you might initialize it like this:
// import { createApp } from 'vue';
// const app = createApp({});
// app.use(ElementPlus);
// For non-Vue usage, Element Plus components might be used differently or its JS might not be needed directly if only CSS is used.

 