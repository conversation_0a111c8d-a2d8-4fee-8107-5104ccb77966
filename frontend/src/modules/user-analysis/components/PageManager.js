/**
 * 页面管理器
 * 负责用户分析页面的初始化、鉴权检查、服务管理等核心功能
 */

import NavigationComponent from '../../../shared/components/navigation.js';
import UserAnalysisService from '../services/user-analysis-service.js';
import UserBehaviorService from '../services/user-behavior-service.js';
import '../../../shared/utils/auth-utils.js';

class PageManager {
    constructor() {
        this.userAnalysisService = null;
        this.userBehaviorService = null;
        this.initialized = false;
    }

    /**
     * 初始化页面
     */
    async initialize() {
        if (this.initialized) {
            console.warn('页面已经初始化过了');
            return;
        }

        console.log('🚀 用户分析页面开始加载，进行鉴权检查...');
        
        try {
            // 1. 检查用户登录状态（必须）
            const user = await AuthUtils.guardPage();
            if (!user) {
                console.log('❌ 用户未登录，已跳转到登录页面');
                return false;
            }
            
            // 2. 初始化页面权限控制（必须）
            AuthUtils.initPagePermissions();
            
            console.log('✅ 用户已登录:', user.username, '角色:', user.role);
            
            // 3. 初始化导航栏
            NavigationComponent.init('navigationContainer');
            
            // 4. 初始化服务
            await this.initializeServices();
            
            this.initialized = true;
            return true;
            
        } catch (error) {
            console.error('页面初始化失败:', error);
            throw new Error('页面初始化失败: ' + error.message);
        }
    }

    /**
     * 初始化服务
     */
    async initializeServices() {
        try {
            // 初始化用户分析服务
            this.userAnalysisService = new UserAnalysisService();
            window.userAnalysisService = this.userAnalysisService; // 全局暴露
            
            // 初始化用户行为分析服务
            this.userBehaviorService = new UserBehaviorService();
            window.userBehaviorService = this.userBehaviorService; // 全局暴露
            
            console.log('✅ 服务初始化完成');
            
        } catch (error) {
            console.error('服务初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取用户分析服务
     */
    getUserAnalysisService() {
        return this.userAnalysisService;
    }

    /**
     * 获取用户行为分析服务
     */
    getUserBehaviorService() {
        return this.userBehaviorService;
    }

    /**
     * 检查是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 重置页面状态
     */
    reset() {
        this.initialized = false;
        this.userAnalysisService = null;
        this.userBehaviorService = null;
    }
}

// 创建单例实例
const pageManager = new PageManager();

export default pageManager; 