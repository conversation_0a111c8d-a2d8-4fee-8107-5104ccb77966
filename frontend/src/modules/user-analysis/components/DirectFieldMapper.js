/**
 * 🚀 直接字段映射器 - 简化版本
 * 目标：废弃复杂的basic_metrics合并，直接从API响应映射到DOM
 */

class DirectFieldMapper {
    constructor() {
        // 🎯 字段映射配置：定义每个DOM元素从哪个API字段获取数据
        this.fieldMappings = {
            // 🔧 基础交易指标 - 修正为正确的API数据路径（来自trading_scale_metrics）
            'totalTradesMetric': { 
                path: [
                    'complete_data.trading_scale_metrics.total_trades',
                    'complete_data.trading_scale_metrics.total_positions',
                    'complete_data.transaction_details.total_transactions'
                ], 
                format: 'number', unit: '笔' 
            },
            'totalVolumeMetric': { 
                path: [
                    'complete_data.trading_scale_metrics.total_volume',
                    'complete_data.transaction_details.total_volume'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            'avgTradeSizeMetric': { 
                path: [
                    'complete_data.trading_scale_metrics.avg_trade_size'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            'maxTradeSize': { 
                path: [
                    'complete_data.trading_scale_metrics.max_single_trade',
                    'complete_data.trading_scale_metrics.max_trade_amount'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            'minTradeSize': { 
                path: [
                    'complete_data.trading_scale_metrics.min_single_trade',
                    'complete_data.trading_scale_metrics.min_trade_amount'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            
            // 🔧 盈亏指标 - 修正为正确的API数据路径（来自pnl_metrics）
            'profitTrades': { 
                path: [
                    'complete_data.pnl_metrics.profitable_trades',
                    'complete_data.basic_metrics.profitable_count'
                ], 
                format: 'number', unit: '笔' 
            },
            'lossTrades': { 
                path: [
                    'complete_data.pnl_metrics.loss_trades',
                    'complete_data.basic_metrics.loss_count'
                ], 
                format: 'number', unit: '笔' 
            },
            'totalProfitAmount': { 
                path: [
                    'complete_data.pnl_metrics.total_profit',
                    'complete_data.basic_metrics.total_profit'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            'totalLossAmount': { 
                path: [
                    'complete_data.pnl_metrics.total_loss',
                    'complete_data.basic_metrics.total_loss'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            'totalPnL': { 
                path: [
                    'complete_data.pnl_metrics.total_pnl',
                    'complete_data.basic_metrics.total_pnl'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            'totalFees': { 
                path: [
                    'complete_data.basic_metrics.total_fees',
                    'complete_data.basic_metrics.total_commission',
                    'complete_data.pnl_metrics.total_fees',
                    'complete_data.trading_scale_metrics.total_commission'
                ], 
                format: 'currency', unit: ' USDT' 
            },
            
            // ✅ 衍生比率指标（来自pnl_metrics或derived_analysis）
            'derivedWinRate': { 
                path: [
                    'complete_data.pnl_metrics.win_rate',
                    'complete_data.derived_analysis.profitability.win_rate'
                ], 
                format: 'percentage' 
            },
            'derivedProfitLossRatio': { 
                path: [
                    'complete_data.pnl_metrics.profit_loss_ratio',
                    'complete_data.derived_analysis.profitability.profit_loss_ratio'
                ], 
                format: 'ratio' 
            },
            
            // 持仓时间指标（来自holding_time_metrics）
            'avgProfitDuration': { 
                path: [
                    'complete_data.holding_time_metrics.avg_profit_duration_minutes',
                    'complete_data.holding_time_metrics.avg_profit_duration'
                ], 
                format: 'number', unit: '分钟' 
            },
            'avgLossDuration': { 
                path: [
                    'complete_data.holding_time_metrics.avg_loss_duration_minutes',
                    'complete_data.holding_time_metrics.avg_loss_duration'
                ], 
                format: 'number', unit: '分钟' 
            },
            'maxHoldingTime': { 
                path: [
                    'complete_data.holding_time_metrics.max_holding_time'
                ], 
                format: 'number', unit: '分钟' 
            },
            'minHoldingTime': { 
                path: [
                    'complete_data.holding_time_metrics.min_holding_time'
                ], 
                format: 'number', unit: '分钟' 
            },
            'durationRatio': { 
                path: [
                    'complete_data.holding_time_metrics.duration_ratio',
                    'complete_data.derived_analysis.trading_behavior.duration_ratio'
                ], 
                format: 'ratio' 
            },
            
            // 订单类型指标（来自order_type_metrics）
            'marketOrderRatio': { 
                path: [
                    'complete_data.order_type_metrics.market_order_ratio',
                    'complete_data.order_type_indicators.market_order_ratio'
                ], 
                format: 'percentage' 
            },
            'limitOrderRatio': { 
                path: [
                    'complete_data.order_type_metrics.limit_order_ratio',
                    'complete_data.order_type_indicators.limit_order_ratio'
                ], 
                format: 'percentage' 
            },
            'openMarketOrders': { 
                path: [
                    'complete_data.basic_metrics.market_orders_open',
                    'complete_data.order_type_metrics.market_orders_open',
                    'complete_data.order_type_metrics.open_market_orders'
                ], 
                format: 'number', unit: '笔' 
            },
            'openLimitOrders': { 
                path: [
                    'complete_data.basic_metrics.limit_orders_open',
                    'complete_data.order_type_metrics.limit_orders_open',
                    'complete_data.order_type_metrics.open_limit_orders'
                ], 
                format: 'number', unit: '笔' 
            },
            
            // 风险控制指标（来自risk_control_metrics或leverage_indicators）
            'avgLeverageMetric': { 
                path: [
                    'complete_data.risk_control_metrics.avg_leverage',
                    'complete_data.leverage_indicators.avg_leverage',
                    'complete_data.derived_analysis.risk_control.avg_leverage'
                ], 
                format: 'number', unit: 'x' 
            },
            'maxLeverage': { 
                path: [
                    'complete_data.risk_control_metrics.max_leverage',
                    'complete_data.leverage_indicators.max_leverage',
                    'complete_data.derived_analysis.risk_control.max_leverage'
                ], 
                format: 'number', unit: 'x' 
            },
            'leverageStability': { 
                path: [
                    'complete_data.risk_control_metrics.leverage_stability',
                    'complete_data.leverage_indicators.leverage_stability',
                    'complete_data.derived_analysis.risk_control.leverage_stability'
                ], 
                format: 'percentage' 
            },
            
            // 资金规模指标（来自fund_scale_metrics）
            'fundScaleMetric': { 
                path: [
                    'complete_data.fund_scale_metrics.fund_scale_category',
                    'complete_data.professional_dashboard.fund_scale_category'
                ], 
                format: 'text' 
            },
            'realTradingVolume': {
                path: [
                    'complete_data.trading_scale_metrics.total_volume',
                    'complete_data.fund_scale_metrics.real_trading_volume',
                    'complete_data.abnormal_analysis.real_trading_scale',
                    'complete_data.pnl_metrics.total_volume'
                ],
                format: 'currency', unit: ' USDT'
            },
            
            // 🎯 交易规模分布（来自fund_scale_metrics或basic_metrics）
            'smallTrades': { 
                path: [
                    'complete_data.basic_metrics.small_trades',
                    'complete_data.fund_scale_metrics.small_trades',
                    'complete_data.trading_scale_metrics.small_trades'
                ], 
                format: 'number', unit: '笔' 
            },
            'mediumTrades': { 
                path: [
                    'complete_data.basic_metrics.medium_trades',
                    'complete_data.fund_scale_metrics.medium_trades',
                    'complete_data.trading_scale_metrics.medium_trades'
                ], 
                format: 'number', unit: '笔' 
            },
            'largeTrades': { 
                path: [
                    'complete_data.basic_metrics.large_trades',
                    'complete_data.fund_scale_metrics.large_trades',
                    'complete_data.trading_scale_metrics.large_trades'
                ], 
                format: 'number', unit: '笔' 
            },
            'smallTradesRatio': { 
                path: [
                    'complete_data.basic_metrics.small_trades_ratio',
                    'complete_data.fund_scale_metrics.small_trades_ratio',
                    'complete_data.trading_scale_metrics.small_trades_ratio'
                ], 
                format: 'percentage' 
            },
            'mediumTradesRatio': { 
                path: [
                    'complete_data.basic_metrics.medium_trades_ratio',
                    'complete_data.fund_scale_metrics.medium_trades_ratio',
                    'complete_data.trading_scale_metrics.medium_trades_ratio'
                ], 
                format: 'percentage' 
            },
            'largeTradesRatio': { 
                path: [
                    'complete_data.basic_metrics.large_trades_ratio',
                    'complete_data.fund_scale_metrics.large_trades_ratio',
                    'complete_data.trading_scale_metrics.large_trades_ratio'
                ], 
                format: 'percentage' 
            },
            
            // 🎯 仓位一致性（来自fund_scale_metrics或derived_analysis）
            'positionConsistency': { 
                path: [
                    'complete_data.fund_scale_metrics.position_consistency',
                    'complete_data.derived_analysis.trading_behavior.position_consistency'
                ], 
                format: 'percentage' 
            },
            
            // ✅ 衍生分析评分（来自derived_analysis）
            'derivedWinRateScore': { 
                path: [
                    'complete_data.derived_analysis.profitability.win_rate_score'
                ], 
                format: 'score' 
            },
            'derivedProfitLossRatioScore': { 
                path: [
                    'complete_data.derived_analysis.profitability.profit_loss_ratio_score'
                ], 
                format: 'score' 
            },
            'derivedAvgLeverageScore': { 
                path: [
                    'complete_data.derived_analysis.risk_control.avg_leverage_score'
                ], 
                format: 'score' 
            },
            'derivedPositionConsistencyScore': { 
                path: [
                    'complete_data.derived_analysis.trading_behavior.position_consistency_score'
                ], 
                format: 'score' 
            },
            
            // 🆕 添加其他衍生分析字段
            'derivedProfitFactor': {
                path: [
                    'complete_data.derived_analysis.profitability.profit_factor',
                    'complete_data.pnl_metrics.profit_factor'
                ],
                format: 'ratio'
            },
            'derivedProfitFactorScore': {
                path: [
                    'complete_data.derived_analysis.profitability.profit_factor_score'
                ],
                format: 'score'
            },
            'derivedProfitConsistency': {
                path: [
                    'complete_data.derived_analysis.profitability.profit_consistency',
                    'complete_data.pnl_metrics.consistency'
                ],
                format: 'percentage'
            },
            'derivedProfitConsistencyScore': {
                path: [
                    'complete_data.derived_analysis.profitability.consistency_score'
                ],
                format: 'score'
            },
            'derivedMaxLeverage': {
                path: [
                    'complete_data.derived_analysis.risk_control.max_leverage',
                    'complete_data.risk_control_metrics.max_leverage'
                ],
                format: 'number', unit: 'x'
            },
            'derivedMaxLeverageScore': {
                path: [
                    'complete_data.derived_analysis.risk_control.max_leverage_score'
                ],
                format: 'score'
            },
            'derivedLeverageStability': {
                path: [
                    'complete_data.derived_analysis.risk_control.leverage_stability',
                    'complete_data.risk_control_metrics.leverage_stability'
                ],
                format: 'percentage'
            },
            'derivedLeverageStabilityScore': {
                path: [
                    'complete_data.derived_analysis.risk_control.leverage_stability_score'
                ],
                format: 'score'
            },
            'derivedMaxSingleLoss': {
                path: [
                    'complete_data.derived_analysis.risk_control.max_single_loss',
                    'complete_data.risk_control_metrics.max_single_loss'
                ],
                format: 'currency', unit: ' USDT'
            },
            'derivedMaxSingleLossScore': {
                path: [
                    'complete_data.derived_analysis.risk_control.max_single_loss_score'
                ],
                format: 'score'
            },
            
            // 🆕 币种分析相关字段（来自coin_analysis）
            'advantageCoins': {
                path: [
                    'complete_data.coin_analysis.advantage_coins'
                ],
                format: 'array_join'
            },
            'coinExpertiseSummary': {
                path: [
                    'complete_data.coin_analysis.coin_expertise_summary',
                    'complete_data.coin_win_rate_analysis.coin_expertise_summary',
                    'complete_data.coin_analysis.summary',
                    'complete_data.trading_preferences.coin_preference.summary'
                ],
                format: 'text'
            },
            'avgCoinWinRate': {
                path: [
                    'complete_data.coin_analysis.avg_coin_win_rate',
                    'complete_data.coin_win_rate_analysis.avg_coin_win_rate'
                ],
                format: 'percentage'
            },

            // 🆕 币种胜率分析概览统计字段
            'analysisCoinsCount': {
                path: [
                    'complete_data.coin_analysis.analysis_coins_count',
                    'complete_data.coin_analysis.total_analyzed_coins'
                ],
                format: 'number'
            },
            'avgCoinWinRateDisplay': {
                path: [
                    'complete_data.coin_analysis.avg_coin_win_rate',
                    'complete_data.coin_win_rate_analysis.avg_coin_win_rate'
                ],
                format: 'percentage'
            },
            'expertCoinsCount': {
                path: [
                    'complete_data.coin_analysis.expert_coins_count'
                ],
                format: 'number'
            },
            'advantageCoinsCountDisplay': {
                path: [
                    'complete_data.coin_analysis.advantage_coins_count'
                ],
                format: 'number'
            },
            
            // 🆕 对冲统计相关字段（来自hedge_statistics）
            'concurrentPositionsCount': {
                path: [
                    'complete_data.hedge_statistics.concurrent_positions_count',
                    'complete_data.hedge_statistics.max_concurrent_positions'
                ],
                format: 'number', unit: '笔'
            },
            'hedgeContracts': {
                path: [
                    'complete_data.hedge_statistics.hedge_contracts'
                ],
                format: 'array_join'
            },
            'hedgeContractsCount': {
                path: [
                    'complete_data.hedge_statistics.hedge_contracts'
                ],
                format: 'array_length', unit: '个'
            },
            'hedgePositionsCount': {
                path: [
                    'complete_data.hedge_statistics.hedge_positions_count'
                ],
                format: 'number', unit: '对'
            },
            
            // 🆕 风险汇总相关字段（来自risk_summary）
            'riskCategories': {
                path: [
                    'complete_data.risk_summary.risk_categories'
                ],
                format: 'array_join'
            },
            'totalRiskTransactions': {
                path: [
                    'complete_data.abnormal_analysis.risk_events_count',
                    'complete_data.risk_summary.total_risks'
                ],
                format: 'number', unit: '笔'
            },
            
            // 🆕 其他缺失的字段（来自trading_scale_metrics和holding_time_metrics）
            'tradingDaysMetric': {
                path: [
                    'complete_data.trading_scale_metrics.trading_days',
                    'complete_data.basic_metrics.total_trading_days'
                ],
                format: 'number', unit: '天'
            },
            'tradingFrequency': {
                path: [
                    'complete_data.holding_time_metrics.trading_frequency',
                    'complete_data.derived_analysis.trading_behavior.trading_frequency'
                ],
                format: 'number', unit: '笔/天'
            },
            'closeMarketOrders': {
                path: [
                    'complete_data.basic_metrics.market_orders_close',
                    'complete_data.order_type_metrics.market_orders_close',
                    'complete_data.order_type_metrics.close_market_orders'
                ],
                format: 'number', unit: '笔'
            },
            'closeLimitOrders': {
                path: [
                    'complete_data.basic_metrics.limit_orders_close',
                    'complete_data.order_type_metrics.limit_orders_close',
                    'complete_data.order_type_metrics.close_limit_orders'
                ],
                format: 'number', unit: '笔'
            },
            'lowLeverageTrades': {
                path: [
                    'complete_data.basic_metrics.low_leverage_trades',
                    'complete_data.risk_control_metrics.low_leverage_trades'
                ],
                format: 'number', unit: '笔'
            },
            'mediumLeverageTrades': {
                path: [
                    'complete_data.basic_metrics.medium_leverage_trades',
                    'complete_data.risk_control_metrics.medium_leverage_trades'
                ],
                format: 'number', unit: '笔'
            },
            'maxSingleLoss': {
                path: [
                    'complete_data.derived_analysis.risk_control.max_single_loss',
                    'complete_data.risk_control_metrics.max_single_loss'
                ],
                format: 'currency', unit: ' USDT'
            },

            // 🚀 专业度评分字段 - 新增
            'totalScore': {
                path: [
                    'complete_data.professional_scores.total_score',
                    'complete_data.professional_dashboard.total_score'
                ],
                format: 'number'
            },
            'profitabilityScore': {
                path: [
                    'complete_data.professional_scores.profitability_score',
                    'complete_data.professional_dashboard.profitability_score'
                ],
                format: 'number'
            },
            'riskControlScore': {
                path: [
                    'complete_data.professional_scores.risk_control_score',
                    'complete_data.professional_dashboard.risk_control_score'
                ],
                format: 'number'
            },
            'tradingBehaviorScore': {
                path: [
                    'complete_data.professional_scores.trading_behavior_score',
                    'complete_data.professional_dashboard.trading_behavior_score'
                ],
                format: 'number'
            },
            'marketUnderstandingScore': {
                path: [
                    'complete_data.professional_scores.market_understanding_score',
                    'complete_data.professional_dashboard.market_understanding_score'
                ],
                format: 'number'
            },
            'traderType': {
                path: [
                    'complete_data.professional_scores.trader_type',
                    'complete_data.professional_dashboard.trader_type'
                ],
                format: 'text'
            },

            // 🚀 专业度评分区域的资金规模字段 - 新增
            'fundScale': {
                path: [
                    'complete_data.fund_scale_metrics.fund_scale_category',
                    'complete_data.professional_dashboard.fund_scale_category',
                    'complete_data.professional_scores.fund_scale_category'
                ],
                format: 'text'
            }
        };
    }
    
    /**
     * 🚀 核心方法：直接映射API数据到DOM
     * @param {Object} apiResponse - 完整的API响应数据
     */
    mapAndDisplay(apiResponse) {
        console.log('🚀 开始直接字段映射显示');
        console.log('API响应数据:', apiResponse);

        const data = apiResponse; // 不需要额外提取complete_data，直接使用原始响应
        let successCount = 0;
        let failureCount = 0;
        const missingFields = [];

        // 遍历所有字段映射
        for (const [elementId, mapping] of Object.entries(this.fieldMappings)) {
            try {
                const value = this.extractValue(data, mapping.path);

                if (value !== null && value !== undefined) {
                    const formattedValue = this.formatValue(value, mapping.format, mapping.unit);
                    this.updateElement(elementId, formattedValue);
                    successCount++;
                    console.log(`✅ ${elementId}: ${formattedValue}`);
                } else {
                    missingFields.push({ elementId, path: mapping.path });
                    failureCount++;
                    console.log(`❌ ${elementId}: 数据不存在 (路径: ${this.formatPath(mapping.path)})`);
                }
            } catch (error) {
                console.error(`❌ 处理字段 ${elementId} 时出错:`, error);
                failureCount++;
            }
        }

        // 🚀 特殊处理：专业度评分进度条
        this.updateProfessionalScoreBars(data);

        console.log(`\n📊 映射结果统计:`);
        console.log(`✅ 成功映射: ${successCount} 个字段`);
        console.log(`❌ 失败字段: ${failureCount} 个字段`);

        if (missingFields.length > 0) {
            console.log(`\n🔍 缺失字段详情:`);
            missingFields.forEach(field => {
                console.log(`- ${field.elementId}: ${this.formatPath(field.path)}`);
            });
        }

        return { successCount, failureCount, missingFields };
    }
    
    /**
     * 从数据对象中提取值，支持多路径查找
     * @param {Object} data - 数据对象
     * @param {string|Array} path - 字段路径或路径数组
     */
    extractValue(data, path) {
        // 如果是数组路径，依次尝试每个路径
        if (Array.isArray(path)) {
            for (const singlePath of path) {
                const value = this.getNestedValue(data, singlePath);
                if (value !== null && value !== undefined) {
                    return value;
                }
            }
            return null;
        }
        
        // 单个路径
        return this.getNestedValue(data, path);
    }
    
    /**
     * 获取嵌套对象的值
     * @param {Object} obj - 对象
     * @param {string} path - 点分隔的路径，如 'basic_data.total_trades'
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    }
    
    /**
     * 格式化值
     * @param {*} value - 原始值
     * @param {string} format - 格式类型
     * @param {string} unit - 单位
     */
    formatValue(value, format, unit = '') {
        if (value === null || value === undefined) return '--';
        
        switch (format) {
            case 'number':
                return Number(value).toLocaleString() + unit;
            
            case 'currency':
                return Number(value).toLocaleString('zh-CN', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }) + unit;
            
            case 'percentage':
                const percentage = typeof value === 'number' && value <= 1 ? value * 100 : value;
                return percentage.toFixed(2) + '%';
            
            case 'ratio':
                return Number(value).toFixed(2);
            
            case 'score':
                return `+${Number(value).toFixed(2)}分`;
            
            case 'array_join':
                if (Array.isArray(value)) {
                    return value.slice(0, 5).join(', ') + unit;
                } else if (value && typeof value === 'object') {
                    try {
                        if (value.advantage_coins && Array.isArray(value.advantage_coins)) {
                            return value.advantage_coins.slice(0, 5).join(', ') + unit;
                        } else if (value.contracts && Array.isArray(value.contracts)) {
                            return value.contracts.slice(0, 5).join(', ') + unit;
                        } else {
                            const arrayValues = Object.values(value).filter(v => Array.isArray(v));
                            if (arrayValues.length > 0) {
                                return arrayValues[0].slice(0, 5).join(', ') + unit;
                            }
                        }
                    } catch (e) {
                        console.warn('数组格式化失败:', e);
                    }
                }
                return String(value) + unit;
            
            case 'array_length':
                if (Array.isArray(value)) {
                    return value.length + unit;
                } else if (value && typeof value === 'object') {
                    try {
                        if (value.advantage_coins && Array.isArray(value.advantage_coins)) {
                            return value.advantage_coins.length + unit;
                        } else if (value.contracts && Array.isArray(value.contracts)) {
                            return value.contracts.length + unit;
                        } else {
                            const arrayValues = Object.values(value).filter(v => Array.isArray(v));
                            if (arrayValues.length > 0) {
                                return arrayValues[0].length + unit;
                            }
                        }
                    } catch (e) {
                        console.warn('数组长度计算失败:', e);
                    }
                }
                return '0' + unit;
            
            case 'text':
            default:
                return String(value) + unit;
        }
    }
    
    /**
     * 更新DOM元素
     * @param {string} elementId - 元素ID
     * @param {string} value - 格式化后的值
     */
    updateElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        } else {
            console.warn(`⚠️ DOM元素不存在: ${elementId}`);
        }
    }

    /**
     * 🚀 更新专业度评分进度条
     * @param {Object} data - API响应数据
     */
    updateProfessionalScoreBars(data) {
        try {
            // 获取专业度评分数据
            const professionalScores = this.extractValue(data, [
                'complete_data.professional_scores',
                'complete_data.professional_dashboard'
            ]);

            if (!professionalScores) {
                console.warn('⚠️ 专业度评分数据不存在');
                return;
            }

            console.log('🚀 更新专业度评分进度条:', professionalScores);

            // 更新各维度进度条
            const scoreMappings = [
                { scoreKey: 'profitability_score', barId: 'profitabilityBar' },
                { scoreKey: 'risk_control_score', barId: 'riskControlBar' },
                { scoreKey: 'trading_behavior_score', barId: 'tradingBehaviorBar' },
                { scoreKey: 'market_understanding_score', barId: 'marketUnderstandingBar' }
            ];

            scoreMappings.forEach(({ scoreKey, barId }) => {
                const score = professionalScores[scoreKey] || 0;
                this.updateScoreBar(barId, score);
                console.log(`✅ 更新进度条 ${barId}: ${score}%`);
            });

        } catch (error) {
            console.error('❌ 更新专业度评分进度条失败:', error);
        }
    }

    /**
     * 更新评分进度条
     * @param {string} barId - 进度条ID
     * @param {number} score - 评分值
     */
    updateScoreBar(barId, score) {
        const barElement = document.getElementById(barId);
        if (barElement) {
            const percentage = Math.min(100, Math.max(0, score));
            barElement.style.width = `${percentage}%`;
        } else {
            console.warn(`⚠️ 进度条元素不存在: ${barId}`);
        }
    }
    
    /**
     * 格式化路径显示
     * @param {string|Array} path - 路径
     */
    formatPath(path) {
        if (Array.isArray(path)) {
            return path.join(' 或 ');
        }
        return path;
    }
    
    /**
     * 🧪 测试方法：使用模拟数据测试映射
     */
    testWithMockData() {
        const mockData = {
            complete_data: {
                basic_data: {
                    total_trades: 18,
                    total_volume: 1844486.8,
                    avg_trade_size: 102471.49,
                    max_single_trade: 653635.78,
                    min_single_trade: 2141.64,
                    position_consistency: 0.4775  // 直接在basic_data中
                },
                trading_scale_metrics: {
                    small_trades: 1,
                    medium_trades: 3,
                    large_trades: 14,
                    small_trades_ratio: 0.0556,
                    medium_trades_ratio: 0.1667,
                    large_trades_ratio: 0.7778
                },
                pnl_metrics: {
                    profitable_trades: 9,
                    loss_trades: 9,
                    total_profit: 113320.84,
                    total_loss: 67590.83,
                    win_rate: 0.5,
                    profit_loss_ratio: 1.6766
                },
                holding_time_metrics: {
                    avg_profit_duration: 1264,
                    avg_loss_duration: 1023,
                    duration_ratio: 1.2347,
                    max_holding_time: 3426,
                    min_holding_time: 0
                },
                order_type_metrics: {
                    market_order_ratio: 1.0,
                    limit_order_ratio: 0.0
                },
                risk_control_metrics: {
                    avg_leverage: 1.0,
                    max_leverage: 1.0,
                    leverage_stability: 1.0
                },
                fund_scale_metrics: {
                    fund_scale_category: "大户",
                    real_trading_volume: 1844486.8
                }
            }
        };
        
        console.log('🧪 使用模拟数据测试映射...');
        return this.mapAndDisplay(mockData);
    }
}

// 🌍 暴露到全局作用域供调试使用
window.DirectFieldMapper = DirectFieldMapper;

export default DirectFieldMapper; 