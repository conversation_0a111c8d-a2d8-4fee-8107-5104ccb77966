/**
 * 工具函数
 * 包含格式化、样式类获取等通用功能
 */

/**
 * 格式化数字
 */
export function formatNumber(num) {
    if (typeof num !== 'number') return '0';
    return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(num);
}

/**
 * 格式化百分比（从小数转换）
 */
export function formatPercentage(value) {
    if (typeof value !== 'number') return '0%';
    // 直接返回后端格式化数据或原值，不做前端格式化
    return value?.percentage_display || value || '--';
}

/**
 * 格式化已经是百分比数值的数据（后端已经乘以100）
 */
export function formatDirectPercentage(value) {
    if (typeof value !== 'number') return '0%';
    return value.toFixed(1) + '%';
}

/**
 * 格式化交易量
 */
export function formatVolume(volume) {
    if (typeof volume !== 'number') return '0.00';
    // 直接返回后端格式化数据或原值，不做前端格式化
    return volume?.volume_display || volume || '--';
}

/**
 * 获取评分等级文本
 */
export function getScoreLevel(score) {
    if (score >= 80) return '优秀';
    if (score >= 70) return '良好';
    if (score >= 60) return '一般';
    if (score >= 40) return '较差';
    return '很差';
}

/**
 * 获取评分颜色
 */
export function getScoreColor(score) {
    if (score >= 75) return '#28a745';
    if (score >= 50) return '#ffc107';
    return '#dc3545';
}

/**
 * 获取评分徽章样式类
 */
export function getScoreBadgeClass(score) {
    if (score >= 75) return 'high';
    if (score >= 50) return 'medium';
    return 'low';
}

/**
 * 获取排名徽章样式类
 */
export function getRankBadgeClass(rank) {
    if (rank === 1) return 'gold';
    if (rank === 2) return 'silver';
    if (rank === 3) return 'bronze';
    return '';
}

/**
 * 获取胜率样式类
 */
export function getWinRateClass(winRate) {
    if (winRate >= 0.6) return 'high';
    if (winRate >= 0.45) return 'medium';
    return 'low';
}

/**
 * 获取性能评分样式类
 */
export function getPerformanceScoreClass(score) {
    if (score >= 70) return 'high';
    if (score >= 50) return 'medium';
    return 'low';
}

/**
 * 获取优势类型样式类
 */
export function getAdvantageTypeClass(advantageType) {
    if (advantageType === '高胜率型') return 'high-win-rate';
    if (advantageType === '高盈利型') return 'high-profit';
    if (advantageType === '综合优势型') return 'comprehensive';
    return 'high-win-rate';
}

/**
 * 获取专业度等级样式类
 */
export function getExpertiseClass(level) {
    if (level.includes('专家')) return 'expert';
    if (level.includes('熟练')) return 'skilled';
    if (level.includes('一般')) return 'average';
    return 'weak';
}

/**
 * 获取风险级别文本
 */
export function getRiskLevelText(severity) {
    if (!severity) return '未知风险';
    
    // 转换为小写进行匹配，支持大小写不敏感
    const severityLower = severity.toString().toLowerCase();
    
    const severityTexts = {
        'high': '高风险',
        'medium': '中等风险',
        'low': '低风险',
        'critical': '严重风险',
        'moderate': '中等风险',
        'minimal': '轻微风险',
        'very_high': '极高风险',
        'very_low': '极低风险'
    };
    
    return severityTexts[severityLower] || `${severity}风险`;
}

/**
 * 获取检测类型名称
 */
export function getDetectionTypeName(detectionType) {
    const typeNames = {
        'wash_trading': '对敲交易',
        'suspected_wash_trading': '疑似对敲交易',
        'high_frequency_trading': '高频交易',
        'large_volume_trading': '大额交易',
        'abnormal_profit': '异常盈利',
        'market_manipulation': '市场操纵',
        'abnormal_trading_pattern': '异常交易模式',
        'cross_platform_arbitrage': '跨平台套利',
        'funding_rate_arbitrage': '资金费率套利',
        'brush_trading': '刷量交易',
        'loop_trading': '循环交易',
        'position_manipulation': '持仓操纵',
        'unknown': '未知类型'
    };
    return typeNames[detectionType] || detectionType || '未知类型';
}

/**
 * 获取风险级别文本（重复定义，保持兼容性）
 */
export function getSeverityText(severity) {
    return getRiskLevelText(severity);
}

/**
 * 根据风险分数确定风险等级
 */
export function getRiskLevel(score) {
    if (score >= 80) return '极高风险';
    if (score >= 60) return '高风险';
    if (score >= 40) return '中等风险';
    if (score >= 20) return '低风险';
    return '正常';
}

/**
 * 获取风险等级样式类
 */
export function getRiskLevelClass(score) {
    if (score >= 80) return 'critical';
    if (score >= 60) return 'high';
    if (score >= 40) return 'medium';
    if (score >= 20) return 'low';
    return 'safe';
}

/**
 * 获取评分等级样式类
 */
export function getScoreLevelClass(score) {
    if (score >= 80) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 60) return 'average';
    if (score >= 40) return 'poor';
    return 'very-poor';
}

/**
 * 获取交易者类型
 */
export function getTraderType(score) {
    if (score >= 90) return '专业交易者';
    if (score >= 80) return '进阶交易者';
    if (score >= 60) return '中级交易者';
    if (score >= 40) return '初级交易者';
    if (score >= 20) return '新手交易者';
    return '--';
}

/**
 * 获取交易者类型样式类
 */
export function getTraderTypeClass(traderType) {
    const typeMap = {
        // 前端计算的术语
        '专业交易者': 'professional',
        '进阶交易者': 'advanced',
        '中级交易者': 'intermediate',
        '初级交易者': 'beginner',
        '新手交易者': 'beginner',
        '--': 'insufficient-data',
        // 后端返回的术语（兼容性）
        '专业交易员': 'professional',
        '半专业交易员': 'semi-professional',
        '普通散户': 'retail',
        '新用户': 'new-user',
        '数据不足': 'insufficient-data'
    };
    return typeMap[traderType] || 'insufficient-data';
}

/**
 * 获取交易者类型颜色
 */
export function getTraderTypeColor(traderType) {
    const colorMap = {
        // 前端计算的术语
        '专业交易者': '#28a745',
        '进阶交易者': '#17a2b8',
        '中级交易者': '#ffc107',
        '初级交易者': '#fd7e14',
        '新手交易者': '#fd7e14',
        '--': '#6c757d',
        // 后端返回的术语（兼容性）
        '专业交易员': '#28a745',
        '半专业交易员': '#ffc107',
        '普通散户': '#fd7e14',
        '新用户': '#17a2b8',
        '数据不足': '#6c757d'
    };
    return colorMap[traderType] || '#6c757d';
}

/**
 * 获取资金规模
 */
export function getFundScale(metrics) {
    // 🚀 严格验证：检查数据是否有效
    if (!metrics || !metrics.total_volume) return '--';

    // 直接使用后端提供的数据，不做前端解析
    const volume = metrics.total_volume_display || metrics.total_volume || '0';

    // 🚀 严格验证：交易量必须大于最小阈值
    if (volume < 1000) return '--';  // 至少需要1000 USDT交易量

    if (volume >= 10000000) return '大户'; // 1000万以上
    if (volume >= 1000000) return '中户';  // 100万以上
    if (volume >= 100000) return '小户';   // 10万以上
    if (volume >= 10000) return '散户';    // 1万以上

    // 🚀 修改：交易量太小时返回"--"而不是"散户"
    return '--';
}

/**
 * 获取资金规模样式类
 */
export function getFundScaleClass(fundScale) {
    const scaleMap = {
        '大户': 'whale',
        '中户': 'medium',
        '小户': 'small',
        '散户': 'retail',
        '--': 'insufficient-data'
    };
    return scaleMap[fundScale] || 'insufficient-data';
}

/**
 * 格式化时间
 */
export function formatTime(timeStr) {
    if (!timeStr) return '-';
    try {
        const date = new Date(timeStr);
        return date.toLocaleString('zh-CN');
    } catch (error) {
        return timeStr;
    }
}

/**
 * 更新进度条
 * @param {string} elementId - 进度条元素ID
 * @param {number} value - 进度值，可以是0-1的比例或0-100的百分比
 */
export function updateProgressBar(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        // 如果值小于等于1，认为是比例值，需要乘以100
        // 如果值大于1，认为是百分比值，直接使用
        const percentage = value <= 1 ? value * 100 : value;
        element.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
    }
}

/**
 * 更新评分条
 */
export function updateScoreBar(barId, score) {
    const bar = document.getElementById(barId);
    if (bar) {
        const fill = bar.querySelector('.score-fill');
        if (fill) {
            fill.style.width = `${score}%`;
            fill.className = `score-fill ${getScoreLevelClass(score)}`;
        }
    }
}

/**
 * 获取指标标签
 */
export function getIndicatorLabel(key) {
    const labels = {
        'trading_volume': '交易量',
        'trading_frequency': '交易频率',
        'duration': '持续时间',
        'is_taker_ratio': '吃单比例',
        'profit_pattern': '盈利模式',
        'pattern_count': '模式数量',
        'avg_sync_score': '同步评分',
        'counterparty_count': '对手方数',
        'position_lifecycle_accuracy': '持仓生命周期准确性',
        'bilateral_confirmation': '双向确认'
    };
    return labels[key] || key;
}

/**
 * 获取指标颜色
 */
export function getIndicatorColor(percentage) {
    if (percentage >= 80) return '#ff4757';
    if (percentage >= 60) return '#ffa502';
    if (percentage >= 40) return '#f1c40f';
    return '#2ed573';
}

/**
 * 解析交易时间
 */
export function parseTransactionTime(timeRange) {
    if (!timeRange || typeof timeRange !== 'string') return null;
    
    try {
        // 处理时间范围格式，取开始时间
        const parts = timeRange.split(' - ');
        const startTime = parts[0].trim();
        return new Date(startTime);
    } catch (e) {
        console.warn('无法解析交易时间:', timeRange);
        return null;
    }
}

/**
 * 计算时间集中度 - 注释掉计算，保留字段映射
 */
export function calculateTimeConcentration(transaction) {

    return transaction.time_concentration || '等待后端计算';
}

/**
 * 评估交易规律性 - 注释掉前端计算，保留字段映射
 */
export function assessTradingRegularity(transaction) {

    return transaction.trading_regularity || '等待后端计算';
}

/**
 * 识别异常时间窗口 - 注释掉前端计算，保留字段映射
 */
export function identifyAbnormalTimeWindows(transaction) {
   
    // 直接返回后端计算的结果或占位符
    return transaction.abnormal_time_window || '等待后端计算';
}

/**
 * 显示提示消息
 */
export function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 12px 20px;
        border-radius: 4px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

/**
 * 复制到剪贴板
 */
export function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToast('已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        showToast('复制失败');
    });
}

/**
 * 防抖函数
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
export function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深度克隆对象
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * 生成唯一ID
 */
export function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * 显示加载状态
 */
export function showLoading(show = true) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'block' : 'none';
    }
}

/**
 * 显示错误消息
 */
export function showError(message) {
    const errorElement = document.getElementById('errorMessage');
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
    
    // 隐藏加载指示器
    showLoading(false);
    
    // 3秒后自动隐藏错误消息
    setTimeout(() => {
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }, 5000);
}

/**
 * 隐藏错误消息
 */
export function hideError() {
    const errorElement = document.getElementById('errorMessage');
    if (errorElement) {
        errorElement.style.display = 'none';
    }
}

/**
 * 显示结果区域
 */
export function showResults() {
    const resultsElement = document.getElementById('analysisResults');
    if (resultsElement) {
        resultsElement.style.display = 'block';
    }
    
    // 隐藏错误消息
    const errorElement = document.getElementById('errorMessage');
    if (errorElement) {
        errorElement.style.display = 'none';
    }
    
    // 隐藏加载指示器
    showLoading(false);
}

/**
 * 隐藏结果区域
 */
export function hideResults() {
    const resultsElement = document.getElementById('analysisResults');
    if (resultsElement) {
        resultsElement.style.display = 'none';
    }
} 