/**
 * 币种胜率分析组件
 * 显示用户在各个币种的交易表现和胜率分析
 */

import { formatPercentage, formatNumber } from './utils.js';

class CoinWinRateAnalysis {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.coinData = null;
        this.sortField = 'performance_score';
        this.sortDirection = 'desc';
        this.chart = null;
        
        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        if (!this.container) {
            console.error('币种胜率分析容器未找到');
            return;
        }

        this.createLayout();
        this.bindEvents();
    }

    /**
     * 创建布局
     */
    createLayout() {
        this.container.innerHTML = `
            <div class="coin-analysis-container">
                <!-- 概览统计 -->
                <div class="coin-overview-stats">
                    <div class="overview-card">
                        <div class="card-icon">📊</div>
                        <div class="card-content">
                            <div class="card-title">分析币种数</div>
                            <div class="card-value" id="totalCoinsCount">0</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">🏆</div>
                        <div class="card-content">
                            <div class="card-title">优势币种数</div>
                            <div class="card-value" id="advantageCoinsCount">0</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">📈</div>
                        <div class="card-content">
                            <div class="card-title">平均胜率</div>
                            <div class="card-value" id="avgWinRate">0%</div>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">⭐</div>
                        <div class="card-content">
                            <div class="card-title">专家级币种</div>
                            <div class="card-value" id="expertCoinsCount">0</div>
                        </div>
                    </div>
                </div>

                <!-- 优势币种展示 -->
                <div class="advantage-coins-section">
                    <h4>🏆 优势币种识别</h4>
                    <div class="advantage-coins-grid" id="advantageCoinsGrid">
                        <!-- 动态生成优势币种卡片 -->
                    </div>
                </div>

                <!-- 币种表现排名表格 -->
                <div class="coin-ranking-section">
                    <div class="section-header">
                        <h4>📊 币种表现排名</h4>
                        <div class="table-controls">
                            <select id="sortFieldSelect" class="sort-select">
                                <option value="performance_score">综合评分</option>
                                <option value="win_rate">胜率</option>
                                <option value="net_pnl">净盈亏</option>
                                <option value="total_trades">交易笔数</option>
                                <option value="profit_factor">盈利因子</option>
                            </select>
                            <button id="sortDirectionBtn" class="sort-btn" title="切换排序方向">
                                <i class="bi bi-sort-down"></i>
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="coin-ranking-table" id="coinRankingTable">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>币种</th>
                                    <th>专业等级</th>
                                    <th>胜率</th>
                                    <th>交易笔数</th>
                                    <th>净盈亏</th>
                                    <th>盈利因子</th>
                                    <th>综合评分</th>
                                </tr>
                            </thead>
                            <tbody id="coinRankingBody">
                                <!-- 动态生成表格内容 -->
                            </tbody>
                        </table>
                    </div>
                </div>


            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 排序字段选择
        const sortFieldSelect = document.getElementById('sortFieldSelect');
        if (sortFieldSelect) {
            sortFieldSelect.addEventListener('change', (e) => {
                this.sortField = e.target.value;
                this.updateRankingTable();
            });
        }

        // 排序方向切换
        const sortDirectionBtn = document.getElementById('sortDirectionBtn');
        if (sortDirectionBtn) {
            sortDirectionBtn.addEventListener('click', () => {
                this.sortDirection = this.sortDirection === 'desc' ? 'asc' : 'desc';
                this.updateSortButton();
                this.updateRankingTable();
            });
        }
    }

    /**
     * 更新币种分析数据
     * @param {Object} coinAnalysisData - 币种分析数据
     */
    updateData(coinAnalysisData) {
        if (!coinAnalysisData) {
            this.showNoDataMessage();
            return;
        }

        this.coinData = coinAnalysisData;
        
        // 更新各个部分
        this.updateOverviewStats();
        this.updateAdvantageCoins();
        this.updateRankingTable();
    }

    /**
     * 更新概览统计
     */
    updateOverviewStats() {
        const data = this.coinData;
        console.log('🔍 币种胜率分析 - 更新概览统计，数据:', data);

        // 更新demo风格的统计卡片
        const analysisCoinsCount = document.getElementById('analysisCoinsCount');
        const avgCoinWinRateDisplay = document.getElementById('avgCoinWinRateDisplay');
        const expertCoinsCount = document.getElementById('expertCoinsCount');
        const advantageCoinsCountDisplay = document.getElementById('advantageCoinsCountDisplay');

        // 分析币种数量 - 优先使用analysis_coins_count，备选total_analyzed_coins
        if (analysisCoinsCount) {
            const count = data.analysis_coins_count || data.total_analyzed_coins || 0;
            analysisCoinsCount.textContent = count;
            console.log('✅ 分析币种数量:', count);
        }

        // 平均胜率 - 格式化为百分比显示
        if (avgCoinWinRateDisplay) {
            const winRate = data.avg_coin_win_rate || 0;
            // 如果是小数值（如0.498），转换为百分比；如果已经是百分比格式，直接使用
            const formattedWinRate = typeof winRate === 'number' && winRate <= 1 ?
                (winRate * 100).toFixed(1) + '%' :
                winRate;
            avgCoinWinRateDisplay.textContent = formattedWinRate;
            console.log('✅ 平均胜率:', formattedWinRate);
        }

        // 专家级币种数量 - 优先使用expert_coins_count，备选从coin_expertise_summary计算
        if (expertCoinsCount) {
            const count = data.expert_coins_count !== undefined ?
                data.expert_coins_count :
                (data.coin_expertise_summary?.expert_coins || []).length;
            expertCoinsCount.textContent = count;
            console.log('✅ 专家级币种数量:', count);
        }

        // 优势币种数量 - 优先使用advantage_coins_count，备选从advantage_coins计算
        if (advantageCoinsCountDisplay) {
            const count = data.advantage_coins_count !== undefined ?
                data.advantage_coins_count :
                (data.advantage_coins || []).length;
            advantageCoinsCountDisplay.textContent = count;
            console.log('✅ 优势币种数量:', count);
        }

        // 兼容旧版本元素
        const totalCoinsCount = document.getElementById('totalCoinsCount');
        const advantageCoinsCount = document.getElementById('advantageCoinsCount');
        const avgWinRate = document.getElementById('avgWinRate');

        if (totalCoinsCount) {
            totalCoinsCount.textContent = data.analysis_coins_count || data.total_analyzed_coins || 0;
        }
        if (advantageCoinsCount) {
            const count = data.advantage_coins_count !== undefined ?
                data.advantage_coins_count :
                (data.advantage_coins || []).length;
            advantageCoinsCount.textContent = count;
        }
        if (avgWinRate) {
            const winRate = data.avg_coin_win_rate || 0;
            const formattedWinRate = typeof winRate === 'number' && winRate <= 1 ?
                (winRate * 100).toFixed(1) + '%' :
                winRate;
            avgWinRate.textContent = formattedWinRate;
        }
    }

    /**
     * 更新优势币种展示
     */
    updateAdvantageCoins() {
        const advantageCoinsGrid = document.getElementById('advantageCoinsGrid');
        const advantageCoins = this.coinData.advantage_coins || [];

        if (!advantageCoinsGrid) {
            console.warn('优势币种网格容器未找到');
            return;
        }

        if (advantageCoins.length === 0) {
            advantageCoinsGrid.innerHTML = '<div class="no-advantage-coins">暂无识别到优势币种</div>';
            return;
        }

        // 根据优势类型确定卡片样式类
        const getCardClass = (advantageType) => {
            if (advantageType.includes('高胜率') || advantageType.includes('胜率')) return 'high-win-rate';
            if (advantageType.includes('高盈利') || advantageType.includes('盈利')) return 'high-profit';
            return 'comprehensive';
        };

        // 根据优势类型确定徽章文本
        const getBadgeText = (advantageType) => {
            if (advantageType.includes('高胜率') || advantageType.includes('胜率')) return '高胜率型';
            if (advantageType.includes('高盈利') || advantageType.includes('盈利')) return '高盈利型';
            return '综合型';
        };

        advantageCoinsGrid.innerHTML = advantageCoins.map(coin => `
            <div class="advantage-coin-card ${getCardClass(coin.advantage_type)}">
                <div class="advantage-header">
                    <span class="coin-name">${coin.contract}</span>
                    <span class="advantage-badge">${getBadgeText(coin.advantage_type)}</span>
                </div>
                <div class="advantage-metrics">
                    <div class="metric-item">
                        <label>胜率</label>
                        <span class="metric-value">${this.formatWinRate(coin.win_rate)}</span>
                    </div>
                    <div class="metric-item">
                        <label>交易笔数</label>
                        <span class="metric-value">${coin.total_trades}笔</span>
                    </div>
                    <div class="metric-item">
                        <label>净盈亏</label>
                        <span class="metric-value ${coin.net_pnl >= 0 ? 'positive' : ''}">${this.formatNumber(coin.net_pnl)} USDT</span>
                    </div>
                    <div class="metric-item">
                        <label>显著性评分</label>
                        <span class="metric-value">${coin.significance_score_display || coin.significance_score || '--'}</span>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 更新排名表格
     */
    updateRankingTable() {
        // 支持新的demo风格表格
        const rankingBody = document.getElementById('coinRankingTableBody') || document.getElementById('coinRankingBody');
        const coinPerformanceRanking = this.coinData.coin_performance_ranking || [];

        if (!rankingBody) {
            console.warn('币种排名表格容器未找到');
            return;
        }

        if (coinPerformanceRanking.length === 0) {
            rankingBody.innerHTML = '<tr><td colspan="8" class="no-data">暂无币种数据</td></tr>';
            return;
        }

        // 排序数据
        const sortedData = [...coinPerformanceRanking].sort((a, b) => {
            const aValue = a[this.sortField] || 0;
            const bValue = b[this.sortField] || 0;

            if (this.sortDirection === 'desc') {
                return bValue - aValue;
            } else {
                return aValue - bValue;
            }
        });

        // 获取排名徽章
        const getRankBadge = (index) => {
            if (index === 0) return '<span class="rank-badge gold">1</span>';
            if (index === 1) return '<span class="rank-badge silver">2</span>';
            if (index === 2) return '<span class="rank-badge bronze">3</span>';
            return `<span class="rank-badge">${index + 1}</span>`;
        };

        // 获取胜率样式类
        const getWinRateClass = (winRate) => {
            if (winRate >= 0.7) return 'high';
            if (winRate >= 0.6) return 'medium';
            return 'low';
        };

        // 获取专业度等级样式
        const getExpertiseClass = (level) => {
            const classMap = {
                '专家级': 'expert',
                '熟练级': 'skilled',
                '一般级': 'average',
                '新手级': 'weak'
            };
            return classMap[level] || 'weak';
        };

        // 获取性能评分样式类
        const getPerformanceScoreClass = (score) => {
            if (score >= 80) return 'high';
            if (score >= 60) return 'medium';
            return 'low';
        };

        rankingBody.innerHTML = sortedData.map((coin, index) => `
            <tr class="rank-${index + 1}">
                <td>${getRankBadge(index)}</td>
                <td><strong>${coin.contract}</strong></td>
                <td><span class="win-rate ${getWinRateClass(coin.win_rate)}">${this.formatWinRate(coin.win_rate)}</span></td>
                <td>${coin.total_trades}</td>
                <td><span class="pnl ${coin.net_pnl >= 0 ? 'positive' : 'negative'}">${coin.net_pnl >= 0 ? '+' : ''}${this.formatNumber(coin.net_pnl)}</span></td>
                <td>${coin.profit_factor_display || coin.profit_factor || '--'}</td>
                <td><span class="expertise ${getExpertiseClass(coin.expertise_level)}">${coin.expertise_level}</span></td>
                <td><span class="performance-score ${getPerformanceScoreClass(coin.performance_score)}">${coin.performance_score_display || coin.performance_score || '--'}</span></td>
            </tr>
        `).join('');
    }







    /**
     * 获取胜率对应的颜色
     */
    getWinRateColor(winRate) {
        if (winRate >= 0.7) return '#28a745';
        if (winRate >= 0.6) return '#ffc107';
        if (winRate >= 0.5) return '#fd7e14';
        return '#dc3545';
    }

    /**
     * 获取专业等级对应的CSS类
     */
    getExpertiseLevelClass(level) {
        const classMap = {
            '专家级': 'expertise-expert',
            '熟练级': 'expertise-skilled',
            '一般级': 'expertise-average',
            '新手级': 'expertise-weak'
        };
        return classMap[level] || 'expertise-default';
    }

    /**
     * 更新排序按钮
     */
    updateSortButton() {
        const sortBtn = document.getElementById('sortDirectionBtn');
        if (sortBtn) {
            const icon = sortBtn.querySelector('i');
            icon.className = this.sortDirection === 'desc' ? 'bi bi-sort-down' : 'bi bi-sort-up';
        }
    }

    /**
     * 显示无数据消息
     */
    showNoDataMessage() {
        this.container.innerHTML = `
            <div class="no-coin-data">
                <div class="no-data-icon">📊</div>
                <div class="no-data-title">暂无币种分析数据</div>
                <div class="no-data-subtitle">该用户的交易数据不足以进行币种胜率分析</div>
            </div>
        `;
    }

    /**
     * 格式化胜率
     */
    formatWinRate(winRate) {
        if (typeof winRate !== 'number') return '--';
        // 如果是小数值（如0.498），转换为百分比；如果已经是百分比格式，直接使用
        return winRate <= 1 ? (winRate * 100).toFixed(1) + '%' : winRate.toFixed(1) + '%';
    }

    /**
     * 格式化数字
     */
    formatNumber(value) {
        if (typeof value !== 'number') return '--';
        return value.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.chart) {
            this.chart.destroy();
        }
    }
}

// ES6 模块导出
export default CoinWinRateAnalysis; 