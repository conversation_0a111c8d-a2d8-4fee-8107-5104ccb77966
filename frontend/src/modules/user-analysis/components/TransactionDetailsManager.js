/**
 * 交易详情管理器
 * 负责显示交易列表、筛选、分页、排序等功能
 */

import { formatVolume, formatNumber, showToast, getRiskLevel, getRiskLevelClass, getSeverityText, getDetectionTypeName } from './utils.js';

class TransactionDetailsManager {
    constructor(domManager) {
        this.elements = domManager.elements;
        this.allTransactions = [];
        this.filteredTransactions = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'risk_score';
        this.sortDirection = 'desc';
        this.currentFilters = {};
        
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        const elements = this.elements;

        // 风险类型筛选
        if (elements.riskTypeFilter) {
            elements.riskTypeFilter.addEventListener('change', () => {
                this.filterTransactions();
            });
        }

        // 交易搜索
        if (elements.searchTransactions) {
            elements.searchTransactions.addEventListener('input', () => {
                this.filterTransactions();
            });
        }

        // 排序
        if (elements.sortBy) {
            elements.sortBy.addEventListener('change', () => {
                this.sortTransactions();
            });
        }

        // 分页按钮
        if (elements.prevPage) {
            elements.prevPage.addEventListener('click', () => {
                this.goToPreviousPage();
            });
        }

        if (elements.nextPage) {
            elements.nextPage.addEventListener('click', () => {
                this.goToNextPage();
            });
        }
    }

    /**
     * 显示交易详情
     */
    display(transactions) {
        if (!transactions || !Array.isArray(transactions)) {
            console.warn('交易详情数据为空或格式错误');
            this.showNoDataMessage();
            return;
        }

        console.log('显示交易详情:', transactions);
        this.allTransactions = transactions;
        this.filteredTransactions = [...transactions];
        this.currentPage = 1;

        // 应用筛选和排序
        this.applyFiltersAndSort();
        
        // 显示交易列表
        this.displayTransactions();
        
        // 更新分页信息
        this.updatePaginationInfo();
    }

    /**
     * 筛选交易
     */
    filterTransactions() {
        const filters = this.getCurrentFilters();
        this.currentFilters = filters;

        this.filteredTransactions = this.allTransactions.filter(transaction => {
            return this.matchesFilters(transaction, filters);
        });

        this.currentPage = 1; // 重置到第一页
        this.sortTransactions();
    }

    /**
     * 获取当前筛选条件
     */
    getCurrentFilters() {
        const filters = {};
        const elements = this.elements;

        // 风险类型筛选
        if (elements.riskTypeFilter) {
            const riskType = elements.riskTypeFilter.value.trim();
            if (riskType) {
                filters.riskType = riskType;
            }
        }

        // 搜索关键词
        if (elements.searchTransactions) {
            const searchTerm = elements.searchTransactions.value.trim();
            if (searchTerm) {
                filters.searchTerm = searchTerm.toLowerCase();
            }
        }

        return filters;
    }

    /**
     * 检查交易是否匹配筛选条件
     */
    matchesFilters(transaction, filters) {
        // 风险类型筛选
        if (filters.riskType) {
            const detectionType = transaction.detection_type || '';
            if (!detectionType.includes(filters.riskType)) {
                return false;
            }
        }

        // 搜索关键词筛选
        if (filters.searchTerm) {
            const searchableFields = [
                transaction.counterparty_id,
                transaction.detection_type,
                transaction.currency,
                transaction.trading_pair
            ].filter(Boolean).map(field => field.toString().toLowerCase());

            const matchesSearch = searchableFields.some(field => 
                field.includes(filters.searchTerm)
            );

            if (!matchesSearch) {
                return false;
            }
        }

        return true;
    }

    /**
     * 排序交易
     */
    sortTransactions() {
        const sortBy = this.elements.sortBy?.value || this.sortField;
        const [field, direction] = sortBy.split('_');
        
        this.sortField = field;
        this.sortDirection = direction || 'desc';

        this.filteredTransactions.sort((a, b) => {
            let aValue = this.getSortValue(a, field);
            let bValue = this.getSortValue(b, field);

            // 处理数字比较
            if (typeof aValue === 'string' && !isNaN(parseFloat(aValue))) {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            }

            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            if (aValue > bValue) comparison = 1;

            return this.sortDirection === 'desc' ? -comparison : comparison;
        });

        this.displayTransactions();
        this.updatePaginationInfo();
    }

    /**
     * 获取排序值
     */
    getSortValue(transaction, field) {
        switch (field) {
            case 'risk':
                return transaction.risk_score || 0;
            case 'volume':
                return parseFloat(transaction.volume) || 0;
            case 'time':
                return new Date(transaction.time_range || 0).getTime();
            case 'frequency':
                return transaction.frequency || 0;
            default:
                return transaction[field] || '';
        }
    }

    /**
     * 显示当前页交易
     */
    displayTransactions() {
        const container = this.elements.transactionsList;
        if (!container) {
            console.warn('交易列表容器不存在');
            return;
        }

        if (this.filteredTransactions.length === 0) {
            this.showNoDataMessage();
            return;
        }

        // 计算当前页的交易
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const currentTransactions = this.filteredTransactions.slice(startIndex, endIndex);

        // 生成交易HTML
        const transactionsHTML = currentTransactions.map((transaction, index) => {
            return this.createTransactionElement(transaction, startIndex + index);
        }).join('');

        container.innerHTML = transactionsHTML;
    }

    /**
     * 创建交易元素HTML
     */
    createTransactionElement(transaction, index) {
        const riskLevel = getRiskLevel(transaction.risk_score || 0);
        const riskClass = getRiskLevelClass(transaction.risk_score || 0);
        const consistencyIssues = this.checkDataConsistency(transaction);

        return `
            <div class="transaction-item ${riskClass}" data-index="${index}">
                <div class="transaction-header" onclick="toggleDetails(${index})">
                    <div class="transaction-basic-info">
                        <div class="transaction-id">
                            <strong>交易对手: ${transaction.counterparty_id || 'N/A'}</strong>
                            <span class="detection-type">${getDetectionTypeName(transaction.detection_type)}</span>
                        </div>
                        <div class="transaction-meta">
                            <span class="currency">${transaction.currency || 'N/A'}</span>
                            <span class="trading-pair">${transaction.trading_pair || 'N/A'}</span>
                            <span class="time-range">${this.formatTimeRange(transaction.time_range)}</span>
                        </div>
                    </div>
                    
                    <div class="transaction-metrics">
                        <div class="metric-item">
                            <label>风险评分</label>
                            <span class="risk-score ${riskClass}">${transaction.risk_score || 0}</span>
                        </div>
                        <div class="metric-item">
                            <label>交易量</label>
                            <span class="volume">${formatVolume(transaction.volume || 0)}</span>
                        </div>
                        <div class="metric-item">
                            <label>频次</label>
                            <span class="frequency">${transaction.frequency || 0}</span>
                        </div>
                    </div>
                    
                    <div class="transaction-actions">
                        <button class="btn btn-sm btn-outline-info" onclick="showCounterpartyDetails(${index})">
                            <i class="bi bi-info-circle"></i> 详情
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="exportTransactionDetail(${index})">
                            <i class="bi bi-download"></i> 导出
                        </button>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                
                ${consistencyIssues.length > 0 ? this.createConsistencyWarning(consistencyIssues) : ''}
                
                <div class="transaction-details" id="details-${index}" style="display: none;">
                    ${this.createTransactionDetailsContent(transaction, index)}
                </div>
            </div>
        `;
    }

    /**
     * 创建交易详情内容
     */
    createTransactionDetailsContent(transaction, index) {
        return `
            <div class="details-content">
                ${this.createBasicInfoSection(transaction)}
                ${this.createDetailsSection(transaction)}
                ${this.createRiskAssessmentSection(transaction)}
                ${this.createAdvancedAnalysisSection(transaction, index)}
            </div>
        `;
    }

    /**
     * 创建基本信息部分
     */
    createBasicInfoSection(transaction) {
        return `
            <div class="details-section">
                <h4><i class="bi bi-info-circle"></i> 基本信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>交易对手ID:</label>
                        <span>${transaction.counterparty_id || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>检测类型:</label>
                                                    <span class="detection-type-badge">${getDetectionTypeName(transaction.detection_type)}</span>
                    </div>
                    <div class="info-item">
                        <label>交易币种:</label>
                        <span>${transaction.currency || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>交易对:</label>
                        <span>${transaction.trading_pair || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>时间范围:</label>
                        <span>${this.formatTimeRange(transaction.time_range)}</span>
                    </div>
                    <div class="info-item">
                        <label>数据来源:</label>
                        <span>${transaction.data_source || '系统检测'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建详细信息部分
     */
    createDetailsSection(transaction) {
        return `
            <div class="details-section">
                <h4><i class="bi bi-bar-chart"></i> 交易详情</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <label>交易总量</label>
                        <span class="metric-value">${formatVolume(transaction.volume || 0)}</span>
                    </div>
                    <div class="metric-card">
                        <label>交易频次</label>
                        <span class="metric-value">${transaction.frequency || 0}</span>
                    </div>
                    <div class="metric-card">
                        <label>平均单笔</label>
                        <span class="metric-value">${this.calculateAverageAmount(transaction)}</span>
                    </div>
                    <div class="metric-card">
                        <label>风险评分</label>
                        <span class="metric-value risk-score">${transaction.risk_score || 0}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建风险评估部分
     */
    createRiskAssessmentSection(transaction) {
        const indicators = transaction.risk_indicators || {};
        const indicatorItems = Object.entries(indicators).map(([key, value]) => {
            const percentage = Math.round(value * 100);
            const colorClass = this.getIndicatorColor(percentage);
            const label = this.getIndicatorLabel(key);
            
            return `
                <div class="indicator-item">
                    <div class="indicator-header">
                        <span class="indicator-label">${label}</span>
                        <span class="indicator-value ${colorClass}">${percentage}%</span>
                    </div>
                    <div class="indicator-bar">
                        <div class="indicator-fill ${colorClass}" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="details-section">
                <h4><i class="bi bi-shield-exclamation"></i> 风险评估</h4>
                <div class="risk-indicators">
                    ${indicatorItems || '<p class="no-data">暂无风险指标数据</p>'}
                </div>
                <div class="risk-summary">
                    <div class="risk-level">
                        <label>风险等级:</label>
                        <span class="risk-badge ${getRiskLevelClass(transaction.risk_score)}">${getRiskLevel(transaction.risk_score)}</span>
                    </div>
                    <div class="severity">
                        <label>严重程度:</label>
                        <span class="severity-badge">${getSeverityText(transaction.severity)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建高级分析部分
     */
    createAdvancedAnalysisSection(transaction, index) {
        return `
            <div class="details-section">
                <h4><i class="bi bi-graph-up"></i> 高级分析</h4>
                <div class="analysis-actions">
                    <button class="btn btn-sm btn-outline-secondary" onclick="showTimePatternModal(${index})">
                        <i class="bi bi-clock"></i> 时间模式分析
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="showNetworkVisualization(${index})">
                        <i class="bi bi-diagram-3"></i> 网络关系图
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="analyzeCounterpartyRelation('${transaction.counterparty_id}')">
                        <i class="bi bi-people"></i> 关联分析
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 创建数据一致性警告
     */
    createConsistencyWarning(issues) {
        const issueItems = issues.map(issue => `<li>${issue}</li>`).join('');
        
        return `
            <div class="consistency-warning">
                <div class="warning-header">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span>数据一致性警告</span>
                </div>
                <ul class="warning-list">
                    ${issueItems}
                </ul>
            </div>
        `;
    }

    /**
     * 检查数据一致性
     */
    checkDataConsistency(transaction) {
        const issues = [];

        // 检查交易量和频次的合理性
        if (transaction.volume && transaction.frequency) {
            const avgAmount = parseFloat(transaction.volume) / transaction.frequency;
            if (avgAmount < 0.01) {
                issues.push('平均单笔交易金额异常低');
            }
            if (avgAmount > 1000000) {
                issues.push('平均单笔交易金额异常高');
            }
        }

        // 检查风险评分的合理性
        if (transaction.risk_score > 100 || transaction.risk_score < 0) {
            issues.push('风险评分超出正常范围');
        }

        // 检查时间范围格式
        if (transaction.time_range && !this.isValidTimeRange(transaction.time_range)) {
            issues.push('时间范围格式异常');
        }

        return issues;
    }

    /**
     * 分页相关方法
     */
    goToPreviousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.displayTransactions();
            this.updatePaginationInfo();
        }
    }

    goToNextPage() {
        const totalPages = Math.ceil(this.filteredTransactions.length / this.pageSize);
        if (this.currentPage < totalPages) {
            this.currentPage++;
            this.displayTransactions();
            this.updatePaginationInfo();
        }
    }

    updatePaginationInfo() {
        const pageInfo = this.elements.pageInfo;
        if (!pageInfo) return;

        const totalItems = this.filteredTransactions.length;
        const totalPages = Math.ceil(totalItems / this.pageSize);
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, totalItems);

        pageInfo.textContent = `第 ${startItem}-${endItem} 项，共 ${totalItems} 项 (第 ${this.currentPage}/${totalPages} 页)`;

        // 更新分页按钮状态
        if (this.elements.prevPage) {
            this.elements.prevPage.disabled = this.currentPage === 1;
        }
        if (this.elements.nextPage) {
            this.elements.nextPage.disabled = this.currentPage === totalPages;
        }
    }

    /**
     * 工具方法 - 注释掉前端计算，保留字段映射
     */
    calculateAverageAmount(transaction) {
        // 注释掉前端计算逻辑，直接返回后端数据或占位符
        // if (!transaction.volume || !transaction.frequency) return 'N/A';
        // const avg = parseFloat(transaction.volume) / transaction.frequency;
        // return formatVolume(avg);
        
        // 直接返回后端计算的结果或占位符
        return transaction.average_amount || '等待后端计算';
    }

    formatTimeRange(timeRange) {
        if (!timeRange) return 'N/A';
        return timeRange;
    }

    isValidTimeRange(timeRange) {
        // 简单的时间范围格式验证
        return timeRange && typeof timeRange === 'string' && timeRange.length > 0;
    }



    getIndicatorLabel(key) {
        const labelMap = {
            'volume_anomaly': '交易量异常',
            'frequency_anomaly': '频次异常',
            'time_concentration': '时间集中度',
            'pattern_similarity': '模式相似度',
            'network_centrality': '网络中心性'
        };
        return labelMap[key] || key;
    }

    getIndicatorColor(percentage) {
        if (percentage >= 80) return 'critical';
        if (percentage >= 60) return 'high';
        if (percentage >= 40) return 'medium';
        if (percentage >= 20) return 'low';
        return 'safe';
    }

    /**
     * 显示无数据消息
     */
    showNoDataMessage() {
        const container = this.elements.transactionsList;
        if (container) {
            container.innerHTML = `
                <div class="no-data-message">
                    <i class="bi bi-inbox"></i>
                    <h3>暂无交易数据</h3>
                    <p>当前筛选条件下没有找到相关交易记录</p>
                </div>
            `;
        }
    }

    /**
     * 应用筛选和排序
     */
    applyFiltersAndSort() {
        this.filterTransactions();
    }

    /**
     * 清空显示
     */
    clear() {
        this.allTransactions = [];
        this.filteredTransactions = [];
        this.currentPage = 1;
        this.currentFilters = {};

        if (this.elements.transactionsList) {
            this.elements.transactionsList.innerHTML = '<p class="no-data">暂无数据</p>';
        }

        if (this.elements.pageInfo) {
            this.elements.pageInfo.textContent = '';
        }
    }

    /**
     * 获取当前数据
     */
    getCurrentTransactions() {
        return this.filteredTransactions;
    }

    getAllTransactions() {
        return this.allTransactions;
    }

    getCurrentPage() {
        return this.currentPage;
    }

    getPageSize() {
        return this.pageSize;
    }
}

// 全局函数，保持向后兼容
window.toggleDetails = function(index) {
    const detailsElement = document.getElementById(`details-${index}`);
    const toggleIcon = document.querySelector(`[data-index="${index}"] .toggle-icon`);
    
    if (detailsElement) {
        const isVisible = detailsElement.style.display !== 'none';
        detailsElement.style.display = isVisible ? 'none' : 'block';
        
        if (toggleIcon) {
            toggleIcon.className = isVisible ? 'bi bi-chevron-down toggle-icon' : 'bi bi-chevron-up toggle-icon';
        }
    }
};

window.showCounterpartyDetails = function(index) {
    console.log('显示交易对手详情:', index);
    // 这里可以实现具体的详情显示逻辑
};

export default TransactionDetailsManager; 