/**
 * 模态框管理器
 * 负责管理各种模态框的显示和关闭
 */

import { formatVolume, formatNumber, copyToClipboard, showToast, getRiskLevel, getRiskLevelClass, getSeverityText, getDetectionTypeName } from './utils.js';

class ModalManager {
    constructor() {
        this.activeModals = new Set();
        this.currentTransactionData = null;
        
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 监听ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        // 监听点击模态框外部关闭
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });

        // 监听关闭按钮点击
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-close') || e.target.classList.contains('close')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    this.closeModal(modal.id);
                }
            }
        });
    }

    /**
     * 显示交易对手详情模态框
     */
    showCounterpartyDetails(transaction) {
        if (!transaction) {
            console.warn('交易数据为空');
            return;
        }

        this.currentTransactionData = transaction;
        
        const modalHTML = this.createCounterpartyModalHTML(transaction);
        this.showModal('counterpartyModal', modalHTML);
    }

    /**
     * 创建交易对手模态框HTML
     */
    createCounterpartyModalHTML(transaction) {
        return `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title">
                            <i class="bi bi-person-circle"></i> 
                            交易对手详情 - ${transaction.counterparty_id}
                        </h4>
                        <button type="button" class="modal-close" aria-label="关闭">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        ${this.createCounterpartyDetailsContent(transaction)}
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary modal-close">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="modalManager.exportCounterpartyData()">
                            <i class="bi bi-download"></i> 导出数据
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建交易对手详情内容
     */
    createCounterpartyDetailsContent(transaction) {
        return `
            <div class="counterparty-details">
                <div class="row">
                    <div class="col-md-6">
                        <div class="detail-section">
                            <h5><i class="bi bi-info-circle"></i> 基本信息</h5>
                            <div class="detail-item">
                                <label>交易对手ID:</label>
                                <span class="copyable" onclick="copyToClipboard('${transaction.counterparty_id}')">
                                    ${transaction.counterparty_id}
                                    <i class="bi bi-clipboard"></i>
                                </span>
                            </div>
                            <div class="detail-item">
                                <label>交易币种:</label>
                                <span>${transaction.currency || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <label>交易对:</label>
                                <span>${transaction.trading_pair || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <label>检测类型:</label>
                                <span class="detection-type-badge">${getDetectionTypeName(transaction.detection_type)}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="detail-section">
                            <h5><i class="bi bi-bar-chart"></i> 交易统计</h5>
                            <div class="detail-item">
                                <label>交易总量:</label>
                                <span class="volume-value">${formatVolume(transaction.volume || 0)}</span>
                            </div>
                            <div class="detail-item">
                                <label>交易频次:</label>
                                <span class="frequency-value">${formatNumber(transaction.frequency || 0)}</span>
                            </div>
                            <div class="detail-item">
                                <label>平均单笔:</label>
                                <span class="avg-amount">${this.calculateAverageAmount(transaction)}</span>
                            </div>
                            <div class="detail-item">
                                <label>时间范围:</label>
                                <span>${transaction.time_range || 'N/A'}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="detail-section">
                            <h5><i class="bi bi-shield-exclamation"></i> 风险评估</h5>
                            <div class="risk-assessment">
                                <div class="risk-score-display">
                                    <div class="score-circle ${getRiskLevelClass(transaction.risk_score)}">
                                        <span class="score-value">${transaction.risk_score || 0}</span>
                                        <span class="score-label">风险评分</span>
                                    </div>
                                    <div class="risk-details">
                                        <div class="risk-level">
                                            <label>风险等级:</label>
                                            <span class="risk-badge ${getRiskLevelClass(transaction.risk_score)}">${getRiskLevel(transaction.risk_score)}</span>
                                        </div>
                                        <div class="severity">
                                            <label>严重程度:</label>
                                            <span class="severity-badge">${getSeverityText(transaction.severity)}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 显示模态框
     */
    showModal(modalId, content) {
        // 创建或更新模态框
        let modal = document.getElementById(modalId);
        if (!modal) {
            modal = document.createElement('div');
            modal.id = modalId;
            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        modal.innerHTML = content;
        modal.style.display = 'block';
        
        // 添加到活动模态框集合
        this.activeModals.add(modalId);
        
        // 添加body类以防止滚动
        document.body.classList.add('modal-open');
        
        console.log(`显示模态框: ${modalId}`);
    }

    /**
     * 关闭模态框
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            this.activeModals.delete(modalId);
            
            // 如果没有活动模态框，移除body类
            if (this.activeModals.size === 0) {
                document.body.classList.remove('modal-open');
            }
            
            console.log(`关闭模态框: ${modalId}`);
        }
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        this.activeModals.forEach(modalId => {
            this.closeModal(modalId);
        });
        this.activeModals.clear();
        document.body.classList.remove('modal-open');
        
        console.log('关闭所有模态框');
    }

    /**
     * 模态框操作方法
     */
    exportCounterpartyData() {
        if (!this.currentTransactionData) {
            showToast('暂无数据可导出', 'warning');
            return;
        }

        const data = {
            counterparty_id: this.currentTransactionData.counterparty_id,
            detection_type: this.currentTransactionData.detection_type,
            currency: this.currentTransactionData.currency,
            trading_pair: this.currentTransactionData.trading_pair,
            volume: this.currentTransactionData.volume,
            frequency: this.currentTransactionData.frequency,
            risk_score: this.currentTransactionData.risk_score,
            time_range: this.currentTransactionData.time_range,
            export_time: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `counterparty_${this.currentTransactionData.counterparty_id}_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showToast('数据导出成功', 'success');
    }

    /**
     * 工具方法 - 注释掉前端计算，保留字段映射
     */
    calculateAverageAmount(transaction) {
        // 注释掉前端计算逻辑，直接返回后端数据或占位符
        // if (!transaction.volume || !transaction.frequency) return 'N/A';
        // const avg = parseFloat(transaction.volume) / transaction.frequency;
        // return formatVolume(avg);
        
        // 直接返回后端计算的结果或占位符
        return transaction.average_amount || '等待后端计算';
    }

    /**
     * 检查是否有活动模态框
     */
    hasActiveModals() {
        return this.activeModals.size > 0;
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.closeAllModals();
        this.currentTransactionData = null;
    }
}

// 创建全局实例
const modalManager = new ModalManager();
window.modalManager = modalManager;

export default ModalManager; 