/**
 * 风险分析显示组件
 * 负责显示风险摘要、关联分析、风险类别图表等
 */

import Chart from 'chart.js/auto';
import { formatVolume, formatNumber, getScoreLevelClass, getRiskLevelClass } from './utils.js';

class RiskAnalysisDisplay {
    constructor(domManager) {
        this.elements = domManager.elements;
        this.riskChart = null;
        this.currentSummary = null;
        this.currentAssociations = null;
    }

    /**
     * 显示风险摘要
     */
    displayRiskSummary(summary) {
        if (!summary) {
            console.warn('风险摘要数据为空');
            return;
        }

        console.log('显示风险摘要:', summary);
        this.currentSummary = summary;

        // 显示统计数据
        this.displayRiskStatistics(summary);
        
        // 创建风险类别图表
        if (summary.risk_categories && summary.risk_categories.length > 0) {
            this.createRiskCategoriesChart(summary.risk_categories);
            this.updateRiskTypeFilter(summary.risk_categories);
        }
    }

    /**
     * 显示风险统计数据
     */
    displayRiskStatistics(summary) {
        const elements = this.elements;

        // 风险事件总数
        if (elements.summaryTotalRisks) {
            const totalRisks = summary.total_risks || 0;
            elements.summaryTotalRisks.textContent = totalRisks;
            elements.summaryTotalRisks.className = `risk-stat ${getRiskLevelClass(totalRisks)}`;
        }

        // 最高风险评分
        if (elements.summaryMaxScore) {
            const maxScore = summary.highest_risk_score || summary.max_score || 0;
            // 直接使用后端格式化数据，不做前端格式化
        elements.summaryMaxScore.textContent = riskData.max_score_display || maxScore || '--';
            elements.summaryMaxScore.className = `risk-score ${getScoreLevelClass(maxScore)}`;
        }

        // 涉及总金额 - 从异常交易数据中获取
        if (elements.summaryTotalVolume) {
            const totalVolume = summary.total_abnormal_volume || summary.total_volume || 0;
            elements.summaryTotalVolume.textContent = formatVolume(totalVolume);
            elements.summaryTotalVolume.className = `volume-stat ${this.getVolumeLevelClass(totalVolume)}`;
        }

        // 风险类型数量
        if (elements.summaryRiskTypes) {
            const riskTypes = (summary.risk_categories && summary.risk_categories.length) || summary.risk_types || 0;
            elements.summaryRiskTypes.textContent = riskTypes;
            elements.summaryRiskTypes.className = `type-count ${this.getTypeCountClass(riskTypes)}`;
        }
    }

    /**
     * 创建风险类别饼图
     */
    createRiskCategoriesChart(categories) {
        const canvas = this.elements.riskCategoriesCanvas;
        if (!canvas) {
            console.warn('风险类别图表画布不存在');
            return;
        }

        // 销毁现有图表
        if (this.riskChart) {
            this.riskChart.destroy();
            this.riskChart = null;
        }

        try {
            const ctx = canvas.getContext('2d');
            
            // 准备图表数据
            const chartData = this.prepareChartData(categories);
            
            this.riskChart = new Chart(ctx, {
                type: 'doughnut',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    // 直接使用后端格式化数据，不做前端格式化
            const percentage = data.percentage_display || value || '--';
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%',
                    elements: {
                        arc: {
                            borderWidth: 2,
                            borderColor: '#fff'
                        }
                    }
                }
            });

            console.log('风险类别图表创建成功');
        } catch (error) {
            console.error('创建风险类别图表失败:', error);
        }
    }

    /**
     * 准备图表数据
     */
    prepareChartData(categories) {
        const labels = [];
        const data = [];
        const backgroundColor = [];
        const borderColor = [];

        // 风险类型颜色映射
        const colorMap = {
            '洗钱风险': '#ff6b6b',
            '高频交易': '#4ecdc4',
            '异常交易': '#45b7d1',
            '关联风险': '#f9ca24',
            '资金流向': '#6c5ce7',
            '其他风险': '#a0a0a0'
        };

        categories.forEach(category => {
            labels.push(category.name || '未知类型');
            data.push(category.count || 0);
            
            const color = colorMap[category.name] || '#a0a0a0';
            backgroundColor.push(color);
            borderColor.push(this.darkenColor(color, 0.2));
        });

        return {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                borderWidth: 2
            }]
        };
    }

    /**
     * 更新风险类型筛选器
     */
    updateRiskTypeFilter(categories) {
        const filterElement = this.elements.riskTypeFilter;
        if (!filterElement) return;

        // 清空现有选项（保留"全部"选项）
        const allOption = filterElement.querySelector('option[value=""]');
        filterElement.innerHTML = '';
        if (allOption) {
            filterElement.appendChild(allOption);
        } else {
            const defaultOption = document.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '全部风险类型';
            filterElement.appendChild(defaultOption);
        }

        // 添加风险类型选项
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.name;
            option.textContent = `${category.name} (${category.count})`;
            filterElement.appendChild(option);
        });
    }

    /**
     * 显示关联分析
     */
    displayAssociationAnalysis(associations) {
        if (!associations) {
            console.warn('关联分析数据为空');
            return;
        }

        console.log('显示关联分析:', associations);
        this.currentAssociations = associations;

        // 显示关联统计
        this.displayAssociationStatistics(associations);
        
        // 显示关联用户列表
        this.displayAssociatedUserLists(associations);
    }

    /**
     * 显示关联统计
     */
    displayAssociationStatistics(associations) {
        const elements = this.elements;

        // 相同IP用户数
        if (elements.sameIpCount) {
            const count = associations.same_ip_count || 0;
            elements.sameIpCount.textContent = count;
            elements.sameIpCount.className = `association-count ${this.getAssociationLevelClass(count)}`;
        }

        // 相同设备用户数
        if (elements.sameDeviceCount) {
            const count = associations.same_device_count || 0;
            elements.sameDeviceCount.textContent = count;
            elements.sameDeviceCount.className = `association-count ${this.getAssociationLevelClass(count)}`;
        }

        // 同时共享IP和设备的用户数
        if (elements.bothSharedCount) {
            const count = associations.both_shared_count || 0;
            elements.bothSharedCount.textContent = count;
            elements.bothSharedCount.className = `association-count ${this.getAssociationLevelClass(count, true)}`;
        }
    }

    /**
     * 显示关联用户列表
     */
    displayAssociatedUserLists(associations) {
        // 显示相同IP用户
        if (associations.same_ip_users && this.elements.sameIpUsers) {
            this.displayAssociatedUsers(
                this.elements.sameIpUsers, 
                associations.same_ip_users, 
                'IP'
            );
        }

        // 显示相同设备用户
        if (associations.same_device_users && this.elements.sameDeviceUsers) {
            this.displayAssociatedUsers(
                this.elements.sameDeviceUsers, 
                associations.same_device_users, 
                '设备'
            );
        }

        // 显示同时共享IP和设备的用户
        if (associations.both_shared_users && this.elements.bothSharedUsers) {
            this.displayAssociatedUsers(
                this.elements.bothSharedUsers, 
                associations.both_shared_users, 
                'IP+设备'
            );
        }
    }

    /**
     * 显示关联用户
     */
    displayAssociatedUsers(container, users, sharedType) {
        if (!container) return;

        if (!users || users.length === 0) {
            container.innerHTML = `<p class="no-data">暂无共享${sharedType}的关联用户</p>`;
            return;
        }

        const userHtml = users.map(user => this.createAssociatedUserHTML(user, sharedType)).join('');
        container.innerHTML = userHtml;
    }

    /**
     * 创建关联用户HTML
     */
    createAssociatedUserHTML(user, sharedType) {
        const riskLevel = this.getUserRiskLevel(user.risk_score || 0);
        const riskClass = getRiskLevelClass(user.risk_score || 0);

        return `
            <div class="associated-user-item ${riskClass}">
                <div class="user-info">
                    <div class="user-id">
                        <strong>${user.member_id || user.user_id}</strong>
                        ${user.digital_id ? `<span class="digital-id">(${user.digital_id})</span>` : ''}
                    </div>
                    <div class="shared-info">
                        <span class="shared-type">共享${sharedType}</span>
                        ${user.risk_score ? `<span class="risk-score">风险评分: ${user.risk_score}</span>` : ''}
                    </div>
                    ${user.last_activity ? `<div class="last-activity">最近活动: ${this.formatDate(user.last_activity)}</div>` : ''}
                </div>
                <div class="user-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="searchRelatedUser('${user.member_id || user.user_id}')">
                        <i class="bi bi-search"></i> 查看详情
                    </button>
                    ${user.risk_score > 70 ? '<span class="high-risk-badge">高风险</span>' : ''}
                </div>
            </div>
        `;
    }

    /**
     * 切换关联分析标签
     */
    switchAssociationTab(tabName) {
        const elements = this.elements;
        
        // 更新标签按钮状态
        if (elements.associationTabs) {
            elements.associationTabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.tab === tabName) {
                    tab.classList.add('active');
                }
            });
        }
        
        // 更新内容显示
        if (elements.associationTabContent) {
            elements.associationTabContent.forEach(content => {
                content.classList.remove('active');
                if (content.id === tabName) {
                    content.classList.add('active');
                }
            });
        }
    }

    /**
     * 样式类获取方法
     */
    getRiskLevelClass(count) {
        if (count === 0) return 'safe';
        if (count <= 5) return 'low';
        if (count <= 15) return 'medium';
        return 'high';
    }



    getVolumeLevelClass(volume) {
        if (volume >= 10000000) return 'massive';
        if (volume >= 1000000) return 'large';
        if (volume >= 100000) return 'medium';
        return 'small';
    }

    getTypeCountClass(count) {
        if (count >= 5) return 'diverse';
        if (count >= 3) return 'multiple';
        if (count >= 1) return 'single';
        return 'none';
    }

    getAssociationLevelClass(count, isBoth = false) {
        const threshold = isBoth ? [0, 1, 3] : [0, 2, 5];
        if (count === 0) return 'none';
        if (count <= threshold[1]) return 'low';
        if (count <= threshold[2]) return 'medium';
        return 'high';
    }

    getUserRiskLevel(score) {
        if (score >= 80) return '极高风险';
        if (score >= 60) return '高风险';
        if (score >= 40) return '中等风险';
        if (score >= 20) return '低风险';
        return '正常';
    }

    /**
     * 工具方法
     */
    darkenColor(color, factor) {
        // 简单的颜色加深方法
        const hex = color.replace('#', '');
        const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));
        const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));
        const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));
        return `rgb(${r}, ${g}, ${b})`;
    }

    formatDate(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        } catch (error) {
            return dateString;
        }
    }

    /**
     * 清空显示
     */
    clear() {
        // 清空统计数据
        const statElements = [
            'summaryTotalRisks', 'summaryMaxScore', 'summaryTotalVolume', 'summaryRiskTypes',
            'sameIpCount', 'sameDeviceCount', 'bothSharedCount'
        ];

        statElements.forEach(elementKey => {
            const element = this.elements[elementKey];
            if (element) {
                element.textContent = '0';
                element.className = element.className.split(' ')[0];
            }
        });

        // 清空关联用户列表
        const listElements = ['sameIpUsers', 'sameDeviceUsers', 'bothSharedUsers'];
        listElements.forEach(elementKey => {
            const element = this.elements[elementKey];
            if (element) {
                element.innerHTML = '<p class="no-data">暂无数据</p>';
            }
        });

        // 销毁图表
        if (this.riskChart) {
            this.riskChart.destroy();
            this.riskChart = null;
        }

        this.currentSummary = null;
        this.currentAssociations = null;
    }

    /**
     * 获取当前数据
     */
    getCurrentSummary() {
        return this.currentSummary;
    }

    getCurrentAssociations() {
        return this.currentAssociations;
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.riskChart) {
            this.riskChart.destroy();
            this.riskChart = null;
        }
    }
}

export default RiskAnalysisDisplay; 