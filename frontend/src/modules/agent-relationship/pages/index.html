<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理关系分析工具</title>
    <!-- Bootstrap CSS CDN link removed, Webpack will handle it -->
    <!-- Inline styles removed, Webpack will handle them from src/css/index-styles.css -->
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="navigationContainer"></div>
    
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">代理关系分析工具</h2>
            <div class="text-muted">
                <i class="bi bi-diagram-3"></i> 分析用户代理关系，发现潜在风险
            </div>
        </div>
        
        <!-- 上传区域（保持原有虚线框样式） -->
        <div class="upload-container" id="uploadContainer">
            <h4>上传CSV文件</h4>
            <p>拖拽文件到这里，或者点击选择文件</p>
            <input type="file" id="fileUpload" accept=".csv" class="d-none">
            <button class="btn btn-primary" id="selectFileBtn">选择文件</button>
        </div>

        <!-- 任务恢复功能 -->
        <div class="row mt-3 pt-3 border-top justify-content-center">
            <div class="col-md-8">
                <div class="text-center mb-3">
                    <label for="taskIdSelector" class="form-label">
                        <i class="bi bi-arrow-repeat"></i> 恢复分析任务
                    </label>
                    <div class="input-group justify-content-center">
                        <select class="form-select" id="taskIdSelector" style="max-width: 400px;">
                            <option value="">手动选择任务...</option>
                        </select>
                        <button class="btn btn-outline-secondary" type="button" id="loadTaskBtn" disabled>
                            <i class="bi bi-arrow-clockwise"></i> 加载
                        </button>
                        <button class="btn btn-outline-info" type="button" id="refreshTasksBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="form-text mt-2">
                        <i class="bi bi-info-circle-fill me-1"></i>
                        选择之前的分析任务ID，点击加载按钮恢复结果（无需重新上传文件）
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增SQL展示区域（独立于上传区域） -->
        <div class="sql-display-container mt-4">
            <div class="text-center">
                <button id="showSqlBtn" class="btn btn-outline-primary mb-2">
                    <i class="bi bi-code me-2"></i>显示SQL查询语句
                </button>
                
                <div id="sqlContainer" class="d-none bg-light p-3 rounded border mx-auto" style="max-width: 800px; max-height: 500px; overflow-y: auto;">
                    <textarea id="sqlTextarea" class="form-control w-100 mb-2" style="font-family: monospace; font-size: 14px; white-space: pre; height: auto; min-height: 300px;" readonly></textarea>
                    <button id="copySqlBtn" class="btn btn-sm btn-success">
                        <i class="bi bi-clipboard me-1"></i>复制到剪贴板
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 加载中的显示 -->
        <div class="row" id="processing" style="display: none;">
            <div class="col-md-12">
                <div class="processing">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>正在处理数据，请稍候...</p>
                </div>
            </div>
        </div>
        
        <!-- 过滤状态显示区域 -->
        <!-- This div will be dynamically created by FilterManager.showFilterStatus if needed -->
        <!-- <div id="filterStatus" class="alert alert-info mb-3" style="display: none;">
            <span id="filterStatusText"></span>
            <button type="button" class="btn-close float-end" aria-label="Close" id="clearBdFilter"></button>
        </div> -->
        
        <!-- 结果展示区域 -->
        <div class="results-container" id="resultsContainer" style="display: none;">
            <!-- 数据摘要 -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card summary-card">
                        <div class="card-body">
                            <h5 class="card-title">共享设备关系</h5>
                            <p class="card-text" id="deviceSharedCount">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card summary-card">
                        <div class="card-body">
                            <h5 class="card-title">共享IP关系</h5>
                            <p class="card-text" id="ipSharedCount">0</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card summary-card">
                        <div class="card-body">
                            <h5 class="card-title">两者都共享关系</h5>
                            <p class="card-text" id="bothSharedCount">0</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="filterUnassignedBD" checked>
                        <label class="form-check-label" for="filterUnassignedBD">不显示BD未分配的数据</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="filterOurbitInternal" checked>
                        <label class="form-check-label" for="filterOurbitInternal">不显示Ourbit_Internal的数据</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="搜索用户ID..." id="searchInput">
                        <button class="btn btn-primary" id="searchBtn">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <button class="btn btn-outline-secondary" id="clearSearchBtn" style="display: none;">
                            <i class="bi bi-x-circle"></i> 清除搜索
                        </button>
                    </div>
                </div>
                <div class="col-md-2 text-end">
                    <button class="btn btn-success" id="exportBtn">
                        <i class="bi bi-download"></i> 导出CSV
                    </button>
                </div>
            </div>
            
            <!-- Tab导航 -->
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="analysis-tab" data-bs-toggle="tab" data-bs-target="#analysisTab" type="button" role="tab">数据分析</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="device-tab" data-bs-toggle="tab" data-bs-target="#deviceTab" type="button" role="tab">共享设备</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="ip-tab" data-bs-toggle="tab" data-bs-target="#ipTab" type="button" role="tab">共享IP</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="both-tab" data-bs-toggle="tab" data-bs-target="#bothTab" type="button" role="tab">两者都共享</button>
                </li>
            </ul>

            <!-- Tab内容 -->
            <div class="tab-content" id="myTabContent">
                <!-- 数据分析标签页 -->
                <div class="tab-pane fade show active" id="analysisTab" role="tabpanel">
                    <!-- 子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="analysisSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="analysis-device-tab" data-bs-toggle="pill" data-bs-target="#analysisDeviceTab" type="button" role="tab">共享设备排名</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="analysis-ip-tab" data-bs-toggle="pill" data-bs-target="#analysisIpTab" type="button" role="tab">共享IP排名</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="analysis-both-tab" data-bs-toggle="pill" data-bs-target="#analysisBothTab" type="button" role="tab">两者都共享排名</button>
                        </li>
                    </ul>

                    <!-- 子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="analysisDeviceTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">共享设备BD团队排名</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="deviceRankingTable">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>BD团队</th>
                                                    <th>数量</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="analysisIpTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">共享IP BD团队排名</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="ipRankingTable">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>BD团队</th>
                                                    <th>数量</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="analysisBothTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">两者都共享BD团队排名</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="bothRankingTable">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>BD团队</th>
                                                    <th>数量</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 共享设备标签页 -->
                <div class="tab-pane fade" id="deviceTab" role="tabpanel">
                    <!-- 设备子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="deviceSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="device-same-tab" data-bs-toggle="pill" data-bs-target="#deviceSameTab" type="button" role="tab">同一BD团队</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="device-diff-tab" data-bs-toggle="pill" data-bs-target="#deviceDiffTab" type="button" role="tab">不同BD团队</button>
                        </li>
                    </ul>

                    <!-- 设备子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="deviceSameTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">同一BD团队 - 共享设备</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="deviceSameBdTable">
                                            <thead>
                                                <tr>
                                                    <th>BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="deviceSameBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="deviceDiffTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">不同BD团队 - 共享设备</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="deviceDiffBdTable">
                                            <thead>
                                                <tr>
                                                    <th>1号BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号BD团队</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="deviceDiffBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 共享IP标签页 -->
                <div class="tab-pane fade" id="ipTab" role="tabpanel">
                    <!-- IP子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="ipSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="ip-same-tab" data-bs-toggle="pill" data-bs-target="#ipSameTab" type="button" role="tab">同一BD团队</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ip-diff-tab" data-bs-toggle="pill" data-bs-target="#ipDiffTab" type="button" role="tab">不同BD团队</button>
                        </li>
                    </ul>

                    <!-- IP子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="ipSameTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">同一BD团队 - 共享IP</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="ipSameBdTable">
                                            <thead>
                                                <tr>
                                                    <th>BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="ipSameBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="ipDiffTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">不同BD团队 - 共享IP</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="ipDiffBdTable">
                                            <thead>
                                                <tr>
                                                    <th>1号BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号BD团队</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="ipDiffBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 两者都共享标签页 -->
                <div class="tab-pane fade" id="bothTab" role="tabpanel">
                    <!-- 两者都共享子Tab导航 -->
                    <ul class="nav nav-pills mt-3 mb-3" id="bothSubTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="both-same-tab" data-bs-toggle="pill" data-bs-target="#bothSameTab" type="button" role="tab">同一BD团队</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="both-diff-tab" data-bs-toggle="pill" data-bs-target="#bothDiffTab" type="button" role="tab">不同BD团队</button>
                        </li>
                    </ul>

                    <!-- 两者都共享子Tab内容 -->
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="bothSameTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">同一BD团队 - 两者都共享</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="bothSameBdTable">
                                            <thead>
                                                <tr>
                                                    <th>BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="bothSameBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="bothDiffTab" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">不同BD团队 - 两者都共享</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped" id="bothDiffBdTable">
                                            <thead>
                                                <tr>
                                                    <th>1号BD团队</th>
                                                    <th>1号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>2号BD团队</th>
                                                    <th>2号用户ID</th>
                                                    <th>名称(层级)</th>
                                                    <th>共享设备ID</th>
                                                    <th>共享IP地址</th>
                                                    <th>匹配次数</th>
                                                    <th>1号最后活跃时间</th>
                                                    <th>2号最后活跃时间</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                        <div class="pagination-container" id="bothDiffBdPagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Bootstrap JS bundle (includes Popper) CDN link removed, Webpack will handle it -->
    <!-- Inline script block removed, Webpack will handle it from src/js/index-page-scripts.js -->
    
    <!-- 合约分析弹窗 (Modal for contract analysis) -->
    <div class="modal fade" id="contractAnalysisModal" tabindex="-1" aria-labelledby="contractAnalysisModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contractAnalysisModalLabel">合约分析上传</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="upload-container mb-3" id="contractUploadContainer">
                        <h5>上传合约交易数据</h5>
                        <p>支持.xlsx格式文件，拖拽文件到这里或点击选择文件</p>
                        <input type="file" id="contractFileUpload" accept=".xlsx" class="d-none">
                        <button class="btn btn-primary" id="contractSelectFileBtn">选择文件</button>
                    </div>
                    <div id="contractProcessing" style="display: none;">
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                        <p class="text-center mt-2">正在处理合约数据，请稍候...</p>
                        <div class="progress mt-2">
                            <div id="contractProgressBar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                    </div>
                    <div id="contractResults" style="display: none;">
                        <h5>分析结果</h5>
                        <div class="alert alert-info">
                            <p>检测到 <span id="contractAbnormalCount" class="fw-bold">0</span> 个异常交易行为</p>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="contractAnalysisTable">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>交易对</th>
                                        <th>时间</th>
                                        <th>异常交易量(USDT)</th>
                                        <th>总持仓量(USDT)</th>
                                        <th>异常原因</th>
                                        <th>风险等级</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" id="contractExportBtn" style="display: none;">导出结果</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Script for contract-analysis.js removed, Webpack will handle it via main.js -->
    <!-- HtmlWebpackPlugin will inject bundled JS here -->
</body>
</html>