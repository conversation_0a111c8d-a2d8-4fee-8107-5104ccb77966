document.addEventListener('DOMContentLoaded', function() {
    
    // 直接在这里添加导航HTML
    const navigationContainer = document.getElementById('navigationContainer');
    if (navigationContainer) {
        const navigationHTML = `
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                <div class="container-fluid">
                    <a class="navbar-brand" href="/">
                        <i class="bi bi-diagram-3"></i> 代理关系分析系统
                    </a>
                    <div class="navbar-nav ms-auto">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link" href="/agent_relationship.html">
                                    <i class="bi bi-diagram-3"></i> 代理分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/contract_analysis.html">
                                    <i class="bi bi-file-earmark-bar-graph"></i> 合约分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/contract_integration.html">
                                    <i class="bi bi-graph-up"></i> 链路分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/user_analysis.html">
                                    <i class="bi bi-person-check"></i> 用户分析
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        `;
        navigationContainer.innerHTML = navigationHTML;
    }
    
    const fileUploadInput = document.getElementById('fileUpload');
    const selectFileBtn = document.getElementById('selectFileBtn');
    const uploadContainer = document.getElementById('uploadContainer');
    const processingDiv = document.getElementById('processing');
    const resultsContainer = document.getElementById('resultsContainer');
    const exportBtn = document.getElementById('exportBtn');
    
    // 新增的任务加载相关元素
    const taskIdSelector = document.getElementById('taskIdSelector');
    const refreshTasksBtn = document.getElementById('refreshTasksBtn');
    const loadTaskBtn = document.getElementById('loadTaskBtn');
    
    let analysisResults = null;
    let currentAgentTaskId = null;
    const ITEMS_PER_PAGE = 50;  // 前端分页显示，每页50条
    
    // 页面加载时初始化
    init();
    
    async function init() {
        try {
            
            // 加载任务列表
            await loadTaskList();
            
            // 绑定事件
            bindEvents();
            
            // URL中的任务ID不再自动预选，用户需要手动选择
        } catch (error) {
            console.error('初始化失败:', error);
            showAlert('页面初始化失败: ' + error.message, 'danger');
        }
    }
    
    // 加载任务列表
    async function loadTaskList() {
        try {
            
            const response = await fetch('/api/agent/tasks', {
            credentials: 'include'
        });
            const data = await response.json();
            
            if (data.status === 'success') {
                const tasks = data.tasks || [];
                updateTaskSelector(tasks);
            } else {
                throw new Error(data.error || '获取任务列表失败');
            }
        } catch (error) {
            console.error('加载任务列表失败:', error);
            updateTaskSelector([], '加载失败，请刷新重试');
            showAlert('加载任务列表失败: ' + error.message, 'warning');
        }
    }
    
    // 更新任务选择器
    function updateTaskSelector(tasks, errorMessage = null) {
        if (!taskIdSelector) return;
        
        // 清空现有选项
        taskIdSelector.innerHTML = '';
        
        if (errorMessage) {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = errorMessage;
            option.disabled = true;
            taskIdSelector.appendChild(option);
            return;
        }
        
        // 添加默认选项
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = tasks.length > 0 ? '请手动选择要恢复的任务...' : '暂无可用任务';
        taskIdSelector.appendChild(defaultOption);
        
        // 添加任务选项
        tasks.forEach(task => {
            const option = document.createElement('option');
            option.value = task.task_id;
            
            // 格式化显示文本
            const createdDate = new Date(task.created_at).toLocaleString();
            const memberCount = task.total_members || 0;
            const filename = task.filename || '未知文件';
            
            option.textContent = `${filename} (${memberCount}个用户, ${createdDate})`;
            taskIdSelector.appendChild(option);
        });
    }
    
    // 加载指定任务
    async function loadTask(taskId) {
        if (!taskId) {
            showAlert('请选择一个任务', 'warning');
            return;
        }
        
        try {
            
            // 显示加载状态
            if (uploadContainer) uploadContainer.style.display = 'none';
            if (processingDiv) processingDiv.style.display = 'block';
            
            showAlert(`正在加载任务 ${taskId}...`, 'info');
            
            // 获取任务结果 - 后端返回所有数据，前端进行分页显示
            const response = await fetch(`/api/agent/result/${taskId}`, {
                credentials: 'include'
            });
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            if (!data.result) {
                throw new Error('任务结果为空');
            }
            
            // 保存结果并显示
            analysisResults = data.result;
            currentAgentTaskId = taskId;
            
            // 更新URL
            updateUrlWithTaskId(taskId);
            
            // 显示结果
            displayResults(data.result);
            
            if (processingDiv) processingDiv.style.display = 'none';
            
            showAlert(`任务 ${taskId} 加载成功`, 'success');
            
        } catch (error) {
            console.error('加载任务失败:', error);
            showAlert('加载任务失败: ' + error.message, 'danger');
            
            if (uploadContainer) uploadContainer.style.display = 'block';
            if (processingDiv) processingDiv.style.display = 'none';
        }
    }
    
    // 绑定事件
    function bindEvents() {
        // 原有的文件上传事件
        if (selectFileBtn) {
            selectFileBtn.addEventListener('click', function() {
                fileUploadInput.click();
            });
        }
        
        if (fileUploadInput) {
            fileUploadInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    uploadFile(file);
                }
            });
        }
        
        // 新增的任务加载事件
        if (taskIdSelector) {
            taskIdSelector.addEventListener('change', function() {
                const selectedTaskId = this.value;
                if (loadTaskBtn) {
                    loadTaskBtn.disabled = !selectedTaskId;
                }
            });
        }
        
        if (loadTaskBtn) {
            loadTaskBtn.addEventListener('click', function() {
                const selectedTaskId = taskIdSelector.value;
                loadTask(selectedTaskId);
            });
        }
        
        if (refreshTasksBtn) {
            refreshTasksBtn.addEventListener('click', function() {
                loadTaskList();
            });
        }
        
        // 其他原有事件
        if (exportBtn) {
            exportBtn.addEventListener('click', exportResults);
        }
        
        // 绑定搜索和筛选事件
        bindSearchAndFilterEvents();
    }
    
    // 绑定搜索和筛选事件（原有功能）
    function bindSearchAndFilterEvents() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        const filterUnassignedBD = document.getElementById('filterUnassignedBD');
        const filterOurbitInternal = document.getElementById('filterOurbitInternal');
        
        if (searchBtn) {
            searchBtn.addEventListener('click', performSearch);
        }
        
        if (searchInput) {
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }
        
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', clearSearch);
        }
        
        if (filterUnassignedBD) {
            filterUnassignedBD.addEventListener('change', applyFilters);
        }
        
        if (filterOurbitInternal) {
            filterOurbitInternal.addEventListener('change', applyFilters);
        }
    }

    // 搜索功能
    function performSearch() {
        const searchInput = document.getElementById('searchInput');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        
        if (searchInput) {
            const searchTerm = searchInput.value.trim();
            
            // 显示/隐藏清除按钮
            if (clearSearchBtn) {
                clearSearchBtn.style.display = searchTerm ? 'inline-block' : 'none';
            }
            
            // 如果有FilterManager，使用它来处理搜索
            if (typeof FilterManager !== 'undefined' && FilterManager.reapplyFilters) {
                FilterManager.currentSearchTerm = searchTerm;
                FilterManager.reapplyFilters();
            } else {
                // 降级处理：直接重新显示结果
                if (analysisResults) {
                    displayResults(analysisResults);
                }
            }
        }
    }

    // 清除搜索
    function clearSearch() {
        const searchInput = document.getElementById('searchInput');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        
        if (searchInput) {
            searchInput.value = '';
        }
        
        if (clearSearchBtn) {
            clearSearchBtn.style.display = 'none';
        }
        
        // 如果有FilterManager，使用它来清除筛选
        if (typeof FilterManager !== 'undefined' && FilterManager.reapplyFilters) {
            FilterManager.currentSearchTerm = '';
            FilterManager.currentBdFilter = '';
            FilterManager.showFilterStatus();
            FilterManager.reapplyFilters();
        } else {
            // 降级处理：直接重新显示结果
            if (analysisResults) {
                displayResults(analysisResults);
            }
        }
    }

    // 应用筛选
    function applyFilters() {
        // 如果有FilterManager，使用它来处理筛选
        if (typeof FilterManager !== 'undefined' && FilterManager.reapplyFilters) {
            FilterManager.reapplyFilters();
        } else {
            // 降级处理：直接重新显示结果
            if (analysisResults) {
                displayResults(analysisResults);
            }
        }
    }
    
    // 移除了自动预选功能，用户需要手动选择任务
    
    // 更新URL中的任务ID
    function updateUrlWithTaskId(taskId) {
        try {
            const url = new URL(window.location);
            if (taskId) {
                url.searchParams.set('task_id', taskId);
            } else {
                url.searchParams.delete('task_id');
            }
            window.history.replaceState({}, '', url);
        } catch (error) {
            console.warn('更新URL失败:', error);
        }
    }

    // 旧的轮询相关函数已移除，现在使用新的任务选择和加载机制
    
    function showAlert(message, type = 'info') {
        let alertDiv = document.getElementById('dynamicAlert');
        if (!alertDiv) {
            alertDiv = document.createElement('div');
            alertDiv.id = 'dynamicAlert';
            alertDiv.className = 'alert alert-dismissible fade show';
            alertDiv.innerHTML = `
                <span id="alertMessage"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            const container = document.querySelector('.container-fluid');
            if (container) {
                container.insertBefore(alertDiv, container.firstChild);
            }
        }
        
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        const messageSpan = document.getElementById('alertMessage');
        if (messageSpan) {
            messageSpan.textContent = message;
        }
        
        if (type === 'success') {
            setTimeout(() => {
                if (alertDiv && alertDiv.parentNode) {
                    alertDiv.classList.remove('show');
                    setTimeout(() => {
                        if (alertDiv && alertDiv.parentNode) {
                            alertDiv.parentNode.removeChild(alertDiv);
                        }
                    }, 150);
                }
            }, 3000);
        }
    }
    
    // 原有的初始化调用已被移除，现在使用新的init()函数
    
    const tableData = {
        deviceSameBd: { data: [], currentPage: 1 },
        deviceDiffBd: { data: [], currentPage: 1 },
        ipSameBd: { data: [], currentPage: 1 },
        ipDiffBd: { data: [], currentPage: 1 },
        bothSameBd: { data: [], currentPage: 1 },
        bothDiffBd: { data: [], currentPage: 1 }
    };
    
    const FilterManager = {
        currentBdFilter: '',
        currentSearchTerm: '',
        initialized: false,
        tabEventsInitialized: false,
        init: function() {
            if (this.initialized) {
                return;
            }
            this.initialized = true;
            
            document.getElementById('filterUnassignedBD').addEventListener('change', () => this.reapplyFilters());
            document.getElementById('filterOurbitInternal').addEventListener('change', () => this.reapplyFilters());
            document.getElementById('searchBtn').addEventListener('click', () => {
                const searchTerm = document.getElementById('searchInput').value.trim();
                this.currentSearchTerm = searchTerm;
                if (searchTerm) document.getElementById('clearSearchBtn').style.display = 'inline-block';
                this.reapplyFilters();
            });
            document.getElementById('clearSearchBtn').addEventListener('click', () => {
                document.getElementById('searchInput').value = '';
                this.currentSearchTerm = '';
                this.currentBdFilter = '';
                this.showFilterStatus();
                this.reapplyFilters();
            });
            this.initTabEvents();
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('copyable-id') || e.target.parentElement.classList.contains('copyable-id')) {
                    const element = e.target.classList.contains('copyable-id') ? e.target : e.target.parentElement;
                    const textToCopy = element.dataset.id;
                    const idType = element.dataset.type;
                    navigator.clipboard.writeText(textToCopy)
                        .then(() => this.showTooltip(element, `${idType}已复制`))
                        .catch(err => {
                            console.error('复制失败:', err);
                            this.showTooltip(element, '复制失败');
                        });
                }
            });
        },
        filterData: function(data) { /* ... Placeholder from previous analysis ... */ return data; }, // This function seems to be unused as per new logic
        showTooltip: function(element, message) {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = message;
            document.body.appendChild(tooltip);
            const rect = element.getBoundingClientRect();
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
            tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
            setTimeout(() => tooltip.classList.add('show'), 10);
            setTimeout(() => {
                tooltip.classList.remove('show');
                setTimeout(() => document.body.removeChild(tooltip), 300);
            }, 2000);
        },
        initTabEvents: function() {
            // 避免重复绑定事件监听器
            if (this.tabEventsInitialized) {
                return;
            }
            
            const allTabs = document.querySelectorAll('[data-bs-toggle="tab"], [data-bs-toggle="pill"]');
            
            // 使用事件委托的方式绑定事件，避免重复绑定
            document.addEventListener('shown.bs.tab', (event) => {
                // 只在有BD筛选时才重新应用筛选器
                if (this.currentBdFilter) {
                    this.reapplyFilters();
                }
            });
            
            this.tabEventsInitialized = true;
        },
        filterByBdTeam: function(bdName, sourceTableId) {
            this.currentBdFilter = bdName;
            if (bdName === '未分配') document.getElementById('filterUnassignedBD').checked = false;
            this.showFilterStatus();
            let targetTabId;
            if (sourceTableId === 'deviceRankingTable') targetTabId = 'device-tab';
            else if (sourceTableId === 'ipRankingTable') targetTabId = 'ip-tab';
            else if (sourceTableId === 'bothRankingTable') targetTabId = 'both-tab';
            
            if (targetTabId) {
                const targetTabElement = document.getElementById(targetTabId);
                if (targetTabElement) {
                    const mainTab = bootstrap.Tab.getInstance(targetTabElement) || new bootstrap.Tab(targetTabElement);
                    mainTab.show();
                    // Listener on shown.bs.tab in initTabEvents should handle reapplyFilters after tab switch
                }
            } else {
                this.reapplyFilters();
            }
        },
        showFilterStatus: function() {
            let filterStatusDiv = document.getElementById('filterStatus');
            if (!filterStatusDiv) {
                const div = document.createElement('div');
                div.id = 'filterStatus';
                div.className = 'alert alert-info mb-3';
                div.style.display = 'none';
                div.innerHTML = `<span id="filterStatusText"></span><button type="button" class="btn-close float-end" aria-label="Close" id="clearBdFilter"></button>`;
                const resultsContainerElement = document.getElementById('resultsContainer');
                if (resultsContainerElement) resultsContainerElement.insertBefore(div, resultsContainerElement.firstChild);
                else console.error("#resultsContainer not found for filterStatusDiv");
                
                const clearBdFilterBtn = document.getElementById('clearBdFilter');
                if (clearBdFilterBtn) clearBdFilterBtn.addEventListener('click', () => {
                    this.currentBdFilter = '';
                    this.showFilterStatus();
                    this.reapplyFilters();
                });
                filterStatusDiv = div; // Assign the created div
            }
            const filterStatusText = document.getElementById('filterStatusText');
            if (this.currentBdFilter) {
                if(filterStatusDiv) filterStatusDiv.style.display = 'block';
                if(filterStatusText) filterStatusText.textContent = `当前只显示 ${this.currentBdFilter} 团队的数据`;
            } else {
                if(filterStatusDiv) filterStatusDiv.style.display = 'none';
            }
        },
        reapplyFilters: function() {
            if (!analysisResults) {
                return;
            }
            if(processingDiv) processingDiv.style.display = 'block';
            const searchTerm = document.getElementById('searchInput').value.trim();
            const filterUnassignedBD = document.getElementById('filterUnassignedBD').checked;
            const filterOurbitInternal = document.getElementById('filterOurbitInternal').checked;
            this.searchWithBackend(searchTerm, this.currentBdFilter, filterUnassignedBD, filterOurbitInternal);
        },
        searchWithBackend: function(searchTerm, filterBd, filterUnassignedBD, filterOurbitInternal) {
            if(processingDiv) processingDiv.style.display = 'block';
            const requestData = {
                device_shared: analysisResults.device_shared,
                ip_shared: analysisResults.ip_shared,
                both_shared: analysisResults.both_shared,
                device_ranking: analysisResults.device_ranking,
                ip_ranking: analysisResults.ip_ranking,
                both_ranking: analysisResults.both_ranking,
                search_term: searchTerm,
                filter_bd: filterBd,
                filter_unassigned_bd: filterUnassignedBD,
                filter_ourbit_internal: filterOurbitInternal
            };
            fetch('/api/agent/search', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) return response.json().then(err => { throw new Error(err.error || '搜索失败'); });
                return response.json();
            })
            .then(data => this.updateTablesWithFilteredData(data))
            .catch(error => {
                console.error('搜索错误:', error);
                alert('搜索错误: ' + error.message);
            })
            .finally(() => { if(processingDiv) processingDiv.style.display = 'none'; });
        },
        updateTablesWithFilteredData: function(data) {
            
            document.getElementById('deviceSharedCount').textContent = data.summary.device_shared_count || 0;
            document.getElementById('ipSharedCount').textContent = data.summary.ip_shared_count || 0;
            document.getElementById('bothSharedCount').textContent = data.summary.both_shared_count || 0;
            const sortUserPairs = (arr) => arr.sort((a, b) => String(a.user_a_mid).localeCompare(String(b.user_a_mid)) || String(a.user_b_mid).localeCompare(String(b.user_b_mid)));
            tableData.deviceSameBd.data = sortUserPairs(data.device_same_bd || []); tableData.deviceSameBd.currentPage = 1;
            tableData.deviceDiffBd.data = sortUserPairs(data.device_diff_bd || []); tableData.deviceDiffBd.currentPage = 1;
            tableData.ipSameBd.data = sortUserPairs(data.ip_same_bd || []); tableData.ipSameBd.currentPage = 1;
            tableData.ipDiffBd.data = sortUserPairs(data.ip_diff_bd || []); tableData.ipDiffBd.currentPage = 1;
            tableData.bothSameBd.data = sortUserPairs(data.both_same_bd || []); tableData.bothSameBd.currentPage = 1;
            tableData.bothDiffBd.data = sortUserPairs(data.both_diff_bd || []); tableData.bothDiffBd.currentPage = 1;
            ['deviceSameBdTable', 'deviceDiffBdTable', 'ipSameBdTable', 'ipDiffBdTable', 'bothSameBdTable', 'bothDiffBdTable'].forEach(displayTablePage);
            ['deviceSameBdTable', 'deviceDiffBdTable', 'ipSameBdTable', 'ipDiffBdTable', 'bothSameBdTable', 'bothDiffBdTable'].forEach(createPagination);
            populateRankingTable(data.device_ranking, 'deviceRankingTable');
            populateRankingTable(data.ip_ranking, 'ipRankingTable');
            populateRankingTable(data.both_ranking, 'bothRankingTable');
        }
    };

    // DOM元素检查已在bindEvents()中完成，这里删除重复的事件绑定代码

    // 轮询相关变量
    let pollingInterval = null;
    
    // 开始轮询任务状态
    function startTaskPolling(taskId) {
        
        // 清除之前的轮询
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
        
        // 立即检查一次
        checkTaskStatus(taskId);
        
        // 每3秒检查一次任务状态
        pollingInterval = setInterval(() => {
            checkTaskStatus(taskId);
        }, 3000);
    }
    
    // 检查任务状态
    async function checkTaskStatus(taskId) {
        try {
            const response = await fetch(`/api/agent/result/${taskId}`, {
                credentials: 'include'
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'completed') {
                // 任务完成，停止轮询并自动加载结果
                stopTaskPolling();
                
                // 显示成功消息
                showAlert('分析完成，正在加载结果...', 'success');
                
                // 自动加载并显示结果
                await loadTask(taskId);
                
                // 隐藏处理状态，显示结果
                if(processingDiv) processingDiv.style.display = 'none';
                if(uploadContainer) uploadContainer.style.display = 'block';
                
            } else if (data.status === 'failed') {
                // 任务失败
                console.error('任务处理失败:', data.message);
                stopTaskPolling();
                showAlert(`任务处理失败: ${data.message || '未知错误'}`, 'danger');
                
                // 恢复上传界面
                if(processingDiv) processingDiv.style.display = 'none';
                if(uploadContainer) uploadContainer.style.display = 'block';
                
            } else {
                // 任务仍在处理中，显示进度信息
                const message = data.message || '正在处理中...';
                showAlert(`正在处理中: ${message}`, 'info');
            }
            
        } catch (error) {
            console.error('检查任务状态失败:', error);
            // 不停止轮询，继续尝试
        }
    }
    
    // 停止轮询
    function stopTaskPolling() {
        if (pollingInterval) {
            clearInterval(pollingInterval);
            pollingInterval = null;
        }
    }

    function uploadFile(file) {
        if(uploadContainer) uploadContainer.style.display = 'none';
        if(processingDiv) processingDiv.style.display = 'block';
        
        showAlert('正在上传文件并创建代理商任务...', 'info');
        
        const formData = new FormData();
        formData.append('file', file);
        
        fetch('/api/agent/upload', { 
            method: 'POST', 
            body: formData,
            credentials: 'include'
        })
            .then(response => {
                if (!response.ok) return response.json().then(err => { throw new Error(err.error || '处理失败'); });
                return response.json();
            })
            .then(data => { 
                if (data.task_id) {
                    // 任务已创建，开始轮询等待完成后自动加载
        
                    showAlert(`代理商任务已创建，正在处理中，完成后将自动显示结果...`, 'info');
                    
                    // 开始轮询任务状态，完成后自动加载
                    startTaskPolling(data.task_id);
                    
                } else {
                    // 旧的同步模式 - 直接显示结果

                    analysisResults = data; 
                    displayResults(data);
                    if(processingDiv) processingDiv.style.display = 'none';
                }
            })
            .catch(error => { 
                console.error('上传文件失败:', error);
                showAlert('错误: ' + error.message, 'danger'); 
                if(uploadContainer) uploadContainer.style.display = 'block';
                if(processingDiv) processingDiv.style.display = 'none';
            });
    }

    function displayResults(data) {
        // 先设置 analysisResults，确保其他函数可以访问到数据
        analysisResults = data;
        
        const sqlContainer = document.getElementById('sqlContainer');
        const showSqlBtn = document.getElementById('showSqlBtn');
        if(sqlContainer) sqlContainer.classList.add('hidden');
        if(showSqlBtn) showSqlBtn.style.display = 'none';
        if(resultsContainer) resultsContainer.style.display = 'block';
        clearTables();
        // 根据数据模式选择正确的统计数据源
        let deviceCount, ipCount, bothCount;
        
        if (data._pagination_mode && data.pagination) {
            // 分页模式：从pagination中获取总记录数
            deviceCount = data.pagination.device?.total_records || 0;
            ipCount = data.pagination.ip?.total_records || 0;
            bothCount = data.pagination.both?.total_records || 0;
        } else if (data.summary) {
            // 传统模式：从summary中获取
            deviceCount = data.summary.device_shared_count || 0;
            ipCount = data.summary.ip_shared_count || 0;
            bothCount = data.summary.both_shared_count || 0;
        } else {
            // 备用方案：直接计算数组长度
            deviceCount = (data.device_shared && data.device_shared.length) || 0;
            ipCount = (data.ip_shared && data.ip_shared.length) || 0;
            bothCount = (data.both_shared && data.both_shared.length) || 0;
        }
        
        document.getElementById('deviceSharedCount').textContent = deviceCount;
        document.getElementById('ipSharedCount').textContent = ipCount;
        document.getElementById('bothSharedCount').textContent = bothCount;
        
        // 📄 检测是否为客户端分页模式
        if (data._pagination_mode === 'client_side') {
            
            // 保存分页信息到全局变量
            window.currentAgentPagination = data.pagination;
            window.currentAgentPagination._pagination_mode = 'client_side';
            window.currentAgentTaskId = currentAgentTaskId;
            
            // 显示分页提示信息
            const deviceTotal = data.pagination?.device?.total_records || 0;
            const deviceLoaded = data.pagination?.device?.loaded_records || 0;
            const ipTotal = data.pagination?.ip?.total_records || 0;
            const ipLoaded = data.pagination?.ip?.loaded_records || 0;
            const bothTotal = data.pagination?.both?.total_records || 0;
            const bothLoaded = data.pagination?.both?.loaded_records || 0;
            
            showAlert(`
                📄 客户端分页模式已启用！
                📊 数据统计：设备共享 ${deviceLoaded}/${deviceTotal} 条，IP共享 ${ipLoaded}/${ipTotal} 条，两者共享 ${bothLoaded}/${bothTotal} 条。
                💡 所有数据已加载，使用前端分页浏览，搜索和筛选功能可查看完整数据。
            `, 'info');
            
            // 在导出按钮附近添加分页模式提示
            if (exportBtn) {
                const exportContainer = exportBtn.parentElement;
                if (exportContainer && !exportContainer.querySelector('.pagination-notice')) {
                    const notice = document.createElement('div');
                    notice.className = 'pagination-notice alert alert-success mt-2';
                    notice.innerHTML = `
                        <small>
                            <i class="bi bi-check-circle"></i> 
                            客户端分页：所有数据已加载，前端分页显示每页50条。筛选和搜索可查看完整数据。
                        </small>
                    `;
                    exportContainer.appendChild(notice);
                }
            }
        }
        
        const processItems = (items) => items.forEach(item => {
            item.user_a_bd = item.user_a_bd || item.top_kol_bd_name || '未分配';
            item.user_b_bd = item.user_b_bd || item.top_kol_bd_name || '未分配';
            item.same_bd = (item.user_a_bd === item.user_b_bd) && item.user_a_bd !== '未分配';
        });
        if (data.device_shared) { processItems(data.device_shared); data.device_shared.forEach(item => item.shared_type = '共享设备'); }
        if (data.ip_shared)    { processItems(data.ip_shared);    data.ip_shared.forEach(item => item.shared_type = '共享IP'); }
        if (data.both_shared)  { processItems(data.both_shared);  data.both_shared.forEach(item => item.shared_type = '两者都共享'); }
        
        FilterManager.init();
        
        // 检查是否有当前的BD筛选状态，如果有，则立即应用筛选
        if (FilterManager.currentBdFilter) {
            FilterManager.showFilterStatus();
            FilterManager.reapplyFilters();
            return; // 不显示原始数据，等待筛选结果
        }
        
        // 只有在没有BD筛选的情况下才显示原始数据
        populateSharedTable(data.device_shared, 'deviceSameBdTable', 'deviceDiffBdTable');
        populateSharedTable(data.ip_shared, 'ipSameBdTable', 'ipDiffBdTable');
        populateSharedTable(data.both_shared, 'bothSameBdTable', 'bothDiffBdTable');
        populateRankingTable(data.device_ranking, 'deviceRankingTable');
        populateRankingTable(data.ip_ranking, 'ipRankingTable');
        populateRankingTable(data.both_ranking, 'bothRankingTable');
    }

    function populateSharedTable(data, sameBdTableId, diffBdTableId) {
        if (!data) return;
        data.forEach(item => {
            item.user_a_level = item.user_a_level || item.user_agent_level || '直客';
            item.user_b_level = item.user_b_level || item.user_agent_level || '直客';
        });
        let sameData = data.filter(item => item.same_bd);
        let diffData = data.filter(item => !item.same_bd);
        
        const sortUserPairs = (arr) => arr.sort((a, b) => String(a.user_a_mid).localeCompare(String(b.user_a_mid)) || String(a.user_b_mid).localeCompare(String(b.user_b_mid)));
        sameData = sortUserPairs(sameData);
        diffData = sortUserPairs(diffData);

        const sameTableKey = sameBdTableId.replace('Table', '');
        tableData[sameTableKey].data = sameData; tableData[sameTableKey].currentPage = 1;
        const diffTableKey = diffBdTableId.replace('Table', '');
        tableData[diffTableKey].data = diffData; tableData[diffTableKey].currentPage = 1;
        
        displayTablePage(sameBdTableId);
        displayTablePage(diffBdTableId);
        createPagination(sameBdTableId);
        createPagination(diffBdTableId);
    }

    function displayTablePage(tableId) {
        const tableKey = tableId.replace('Table', '');
        const tbody = document.querySelector(`#${tableId} tbody`);
        if(!tbody) return;
        tbody.innerHTML = '';
        const { data, currentPage } = tableData[tableKey];
        if (!data || data.length === 0) return;
        const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
        const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, data.length);
        for (let i = startIndex; i < endIndex; i++) {
            const item = data[i];
            const row = document.createElement('tr');
            const formatUserId = (id, idType = '用户ID') => {
                id = id ? String(id) : '';
                return id && id.length > 10 ? 
                    `<span class="copyable-id" data-id="${id}" data-type="${idType}">${id.slice(0, 4)}...${id.slice(-6)}</span>` : 
                    (id || '');
            };
            const formatDeviceId = (id) => {
                id = id ? String(id) : '';
                return id && id.length > 10 ? 
                    `<span class="copyable-id" data-id="${id}" data-type="设备ID">${id.slice(0, 4)}...${id.slice(-6)}</span>` : 
                    (id || '');
            };
            const formatIp = (ip) => ip ? `<span class="copyable-id" data-id="${ip}" data-type="IP地址">${ip}</span>` : '';
            const formatTime = (timeStr) => {
                if (!timeStr) return '';
                const parts = timeStr.split(' ');
                if (parts.length !== 2) return timeStr;
                const dateParts = parts[0].split('-');
                if (dateParts.length !== 3) return timeStr;
                return `${dateParts[1]}-${dateParts[2]} ${parts[1]}`;
            };
            let userALevel = String(item.user_a_level || '').trim();
            let userBLevel = String(item.user_b_level || '').trim();
            let userAName = item.user_a_name || '';
            let userBName = item.user_b_name || '';
            if (userALevel) userAName += ` (${userALevel})`;
            if (userBLevel) userBName += ` (${userBLevel})`;

            if (tableId.includes('Same')) {
                if (tableId.includes('device')) {
                    row.innerHTML = `<td>${item.user_a_bd || ''}</td><td>${formatUserId(item.user_a_mid)}</td><td>${userAName}</td><td>${formatUserId(item.user_b_mid)}</td><td>${userBName}</td><td>${formatDeviceId(item.shared_value || '')}</td><td><span class="badge bg-primary">${item.match_count || 0}</span></td><td>${formatTime(item.user_a_time || '')}</td><td>${formatTime(item.user_b_time || '')}</td>`;
                } else if (tableId.includes('ip')) {
                    row.innerHTML = `<td>${item.user_a_bd || ''}</td><td>${formatUserId(item.user_a_mid)}</td><td>${userAName}</td><td>${formatUserId(item.user_b_mid)}</td><td>${userBName}</td><td>${formatIp(item.shared_value || '')}</td><td><span class="badge bg-primary">${item.match_count || 0}</span></td><td>${formatTime(item.user_a_time || '')}</td><td>${formatTime(item.user_b_time || '')}</td>`;
                } else if (tableId.includes('both')) {
                    row.innerHTML = `<td>${item.user_a_bd || ''}</td><td>${formatUserId(item.user_a_mid)}</td><td>${userAName}</td><td>${formatUserId(item.user_b_mid)}</td><td>${userBName}</td><td>${formatDeviceId(item.shared_device_id || '')}</td><td>${formatIp(item.shared_ip || '')}</td><td><span class="badge bg-primary">${item.match_count || 0}</span></td><td>${formatTime(item.user_a_time || '')}</td><td>${formatTime(item.user_b_time || '')}</td>`;
                }
            } else {
                if (tableId.includes('device')) {
                    row.innerHTML = `<td>${item.user_a_bd || ''}</td><td>${formatUserId(item.user_a_mid)}</td><td>${userAName}</td><td>${item.user_b_bd || ''}</td><td>${formatUserId(item.user_b_mid)}</td><td>${userBName}</td><td>${formatDeviceId(item.shared_value || '')}</td><td><span class="badge bg-primary">${item.match_count || 0}</span></td><td>${formatTime(item.user_a_time || '')}</td><td>${formatTime(item.user_b_time || '')}</td>`;
                } else if (tableId.includes('ip')) {
                    row.innerHTML = `<td>${item.user_a_bd || ''}</td><td>${formatUserId(item.user_a_mid)}</td><td>${userAName}</td><td>${item.user_b_bd || ''}</td><td>${formatUserId(item.user_b_mid)}</td><td>${userBName}</td><td>${formatIp(item.shared_value || '')}</td><td><span class="badge bg-primary">${item.match_count || 0}</span></td><td>${formatTime(item.user_a_time || '')}</td><td>${formatTime(item.user_b_time || '')}</td>`;
                } else if (tableId.includes('both')) {
                    row.innerHTML = `<td>${item.user_a_bd || ''}</td><td>${formatUserId(item.user_a_mid)}</td><td>${userAName}</td><td>${item.user_b_bd || ''}</td><td>${formatUserId(item.user_b_mid)}</td><td>${userBName}</td><td>${formatDeviceId(item.shared_device_id || '')}</td><td>${formatIp(item.shared_ip || '')}</td><td><span class="badge bg-primary">${item.match_count || 0}</span></td><td>${formatTime(item.user_a_time || '')}</td><td>${formatTime(item.user_b_time || '')}</td>`;
                }
            }
            tbody.appendChild(row);
        }
    }

    function createPagination(tableId) {
        const tableKey = tableId.replace('Table', '');
        const { data, currentPage } = tableData[tableKey];
        const paginationContainer = document.getElementById(`${tableKey}Pagination`);
        if (!paginationContainer) {
            return;
        }
        
        paginationContainer.innerHTML = '';
        
        // 检查是否为服务器端分页模式
        const isPaginationMode = window.currentAgentPagination && window.currentAgentPagination._pagination_mode === true;
        let totalPages, totalRecords, pageSize;
        
        if (isPaginationMode) {
            // 服务器端分页模式
            const relationshipType = getRelationshipTypeFromTableId(tableId);
            const paginationInfo = window.currentAgentPagination[relationshipType];
            
            if (!paginationInfo) {
                return; // 没有对应类型的分页信息
            }
            
            totalPages = paginationInfo.total_pages;
            totalRecords = paginationInfo.total_records;
            pageSize = paginationInfo.page_size;
        } else {
            // 客户端分页模式
            if (!data || data.length === 0) {
                return;
            }
            totalPages = Math.ceil(data.length / ITEMS_PER_PAGE);
            totalRecords = data.length;
            pageSize = ITEMS_PER_PAGE;
        }
        
        if (totalPages <= 1) return;
        
        const pagination = document.createElement('div');
        pagination.className = 'pagination';
        
        const prevButton = document.createElement('button');
        prevButton.innerHTML = '&laquo; 上一页';
        prevButton.disabled = currentPage === 1;
        prevButton.addEventListener('click', () => {
            if (currentPage > 1) {
                if (isPaginationMode) {
                    // 服务器端分页 - 请求新数据
                    loadRelationshipPage(tableId, currentPage - 1);
                } else {
                    // 客户端分页
                    tableData[tableKey].currentPage--;
                    displayTablePage(tableId);
                    createPagination(tableId);
                }
            }
        });
        pagination.appendChild(prevButton);
        
        const pageInfo = document.createElement('div');
        pageInfo.className = 'page-info';
        const modeText = isPaginationMode ? ' (服务器分页)' : '';
        pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页 (总计 ${totalRecords} 条)${modeText}`;
        pagination.appendChild(pageInfo);
        
        const nextButton = document.createElement('button');
        nextButton.innerHTML = '下一页 &raquo;';
        nextButton.disabled = currentPage === totalPages;
        nextButton.addEventListener('click', () => {
            if (currentPage < totalPages) {
                if (isPaginationMode) {
                    // 服务器端分页 - 请求新数据
                    loadRelationshipPage(tableId, currentPage + 1);
                } else {
                    // 客户端分页
                    tableData[tableKey].currentPage++;
                    displayTablePage(tableId);
                    createPagination(tableId);
                }
            }
        });
        pagination.appendChild(nextButton);
        paginationContainer.appendChild(pagination);
    }
    
    // 辅助函数：从表格ID获取关系类型
    function getRelationshipTypeFromTableId(tableId) {
        if (tableId.includes('device')) return 'device';
        if (tableId.includes('ip')) return 'ip';
        if (tableId.includes('both')) return 'both';
        return 'device'; // 默认值
    }
    
    // 加载特定页面的关系数据
    async function loadRelationshipPage(tableId, page) {
        const relationshipType = getRelationshipTypeFromTableId(tableId);
        const sameBdOnly = tableId.includes('Same');
        
        try {
            showAlert(`正在加载第${page}页数据...`, 'info');
            
            const response = await fetch(`/api/agent/result/${window.currentAgentTaskId}/relationships/${relationshipType}?page=${page}&page_size=50&same_bd_only=${sameBdOnly}`, {
            credentials: 'include'
        });
            
            if (!response.ok) {
                throw new Error(`请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status !== 'success') {
                throw new Error(data.error || '加载失败');
            }
            
            // 更新表格数据
            const tableKey = tableId.replace('Table', '');
            tableData[tableKey].data = data.relationships;
            tableData[tableKey].currentPage = page;
            
            // 更新分页信息
            if (window.currentAgentPagination && window.currentAgentPagination[relationshipType]) {
                window.currentAgentPagination[relationshipType].current_page = page;
            }
            
            // 重新显示表格和分页
            displayTablePage(tableId);
            createPagination(tableId);
            
            showAlert(`第${page}页数据加载完成`, 'success', 2000);
            
        } catch (error) {
            console.error('加载分页数据失败:', error);
            showAlert(`加载第${page}页数据失败: ${error.message}`, 'danger');
        }
    }

    function exportResults() {
        if (!analysisResults) return;
        if(processingDiv) processingDiv.style.display = 'block';
        const searchTerm = document.getElementById('searchInput').value.trim();
        const filterUnassignedBD = document.getElementById('filterUnassignedBD').checked;
        const filterOurbitInternal = document.getElementById('filterOurbitInternal').checked;
        const filterBd = FilterManager.currentBdFilter;
        
        // 🔄 准备导出数据
        const exportData = {
            device_shared: analysisResults.device_shared,
            ip_shared: analysisResults.ip_shared,
            both_shared: analysisResults.both_shared,
            search_term: searchTerm,
            filter_bd: filterBd,
            filter_unassigned_bd: filterUnassignedBD,
            filter_ourbit_internal: filterOurbitInternal
        };
        
        // 如果是分页模式，传递任务ID以便后端获取完整数据
        if (analysisResults._pagination_mode && currentAgentTaskId) {
            exportData.task_id = currentAgentTaskId;
        }
        
        fetch('/api/agent/download', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify(exportData)
        })
        .then(response => {
            if (!response.ok) throw new Error('导出失败');
            return response.blob();
        })
        .then(blob => {
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', '代理关系分析_' + new Date().toISOString().slice(0, 10) + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            if(processingDiv) processingDiv.style.display = 'none';
            showAlert('导出成功', 'success');
        })
        .catch(error => {
            console.error('导出失败:', error);
            if(processingDiv) processingDiv.style.display = 'none';
            showAlert('导出失败: ' + error.message, 'danger');
        });
    }

    function clearTables() {
        const tables = ['deviceSameBdTable', 'deviceDiffBdTable', 'ipSameBdTable', 'ipDiffBdTable', 'bothSameBdTable', 'bothDiffBdTable', 'deviceRankingTable', 'ipRankingTable', 'bothRankingTable'];
        tables.forEach(tableId => {
            const tbody = document.querySelector(`#${tableId} tbody`);
            if(tbody) tbody.innerHTML = '';
        });
    }

    function populateRankingTable(data, tableId) {
        if (!data || data.length === 0) {
             const tbodyClear = document.querySelector(`#${tableId} tbody`);
             if(tbodyClear) tbodyClear.innerHTML = '';
             return;
        }
        const tbody = document.querySelector(`#${tableId} tbody`);
        if(!tbody) return;
        tbody.innerHTML = '';
        const filterUnassignedBD = document.getElementById('filterUnassignedBD').checked;
        const filterOurbitInternal = document.getElementById('filterOurbitInternal').checked;
        let filteredData = data.filter(item => {
            if (filterOurbitInternal && item.bd_name === 'Ourbit_Internal') return false;
            if (filterUnassignedBD && (!item.bd_name || item.bd_name === '未分配')) return false;
            return true;
        });
        filteredData.sort((a, b) => {
            if (b.count !== a.count) return b.count - a.count;
            const bdNameA = a.bd_name || '未分配';
            const bdNameB = b.bd_name || '未分配';
            return bdNameA.localeCompare(bdNameB);
        });
        filteredData.forEach((item, index) => {
            const row = document.createElement('tr');
            const bdName = item.bd_name || '未分配';
            row.innerHTML = `<td>${index + 1}</td><td><a href="#" class="bd-team-link" data-bd-name="${bdName}">${bdName}</a></td><td>${item.count}</td>`;
            tbody.appendChild(row);
        });
        tbody.querySelectorAll('.bd-team-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const bdName = this.getAttribute('data-bd-name');
                FilterManager.filterByBdTeam(bdName, tableId);
            });
        });
    }

    const FIXED_SQL = 
`SELECT \n    a.*,\n    dl.device_id,\n    dl.ip,\n    dl.created_time\nFROM (\n    SELECT\n        member_id,\n        digital_id,\n        agent_flag, -- 用户身份：KOL，代理带来的用户，直客\n        recommender_digital_id, -- 推荐人短ID\n        top_kol_digital_id, -- 一级代理ID\n        top_kol_bd_name, -- BD\n        user_agent_level -- 用户的代理等级，若用户为直客为返回空，若为代理用户则返回代理等级\n    FROM dw_amd.amd_user_info_full_a_daily\n    WHERE dt = replace(cast(cast(date_add('day',-1,current_date) AS date) AS varchar),'-','')\n) AS a\nJOIN (\n    -- 设备日志表\n    SELECT \n        member_id, \n        device_id,\n        ip,\n        created_time,\n        time_zone_offset\n    FROM dw_ods.ods_ucenter_member_device_log_i_daily\n    WHERE dt >= replace(cast(cast(date_trunc('month', current_date) AS date) AS varchar), '-', '')\n        AND dt < replace(cast(cast(date_add('month', 1, date_trunc('month', current_date)) AS date) AS varchar), '-', '')\n) dl ON a.member_id = dl.member_id;`;

    const showSqlBtn = document.getElementById('showSqlBtn');
    if (showSqlBtn) {
        showSqlBtn.addEventListener('click', function() {
            const sqlContainer = document.getElementById('sqlContainer');
            const sqlTextarea = document.getElementById('sqlTextarea');
            if (sqlContainer && sqlTextarea) {
                sqlTextarea.value = FIXED_SQL;
                sqlContainer.classList.toggle('d-none');
            }
        });
    }

    const copySqlBtn = document.getElementById('copySqlBtn');
    if (copySqlBtn) {
        copySqlBtn.addEventListener('click', function() {
            const sqlTextarea = document.getElementById('sqlTextarea');
            if (sqlTextarea) {
                navigator.clipboard.writeText(sqlTextarea.value)
                    .then(() => alert('SQL语句已复制!'))
                    .catch(err => console.error('复制失败:', err));
            }
        });
    }

    // 暴露到全局作用域用于调试 - 移到事件监听器内部
    window.FilterManager = FilterManager;
    window.tableData = tableData;
    // 使用getter来确保获取到最新的analysisResults
    Object.defineProperty(window, 'analysisResults', {
        get: function() {
            return analysisResults;
        },
        configurable: true
    });
}); 