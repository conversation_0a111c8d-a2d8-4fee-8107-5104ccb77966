class NavigationComponent {
    constructor() {
        this.currentPage = this.getCurrentPage();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'home';
        if (path.includes('agent') || path.includes('index')) return 'agent';
        if (path.includes('contract_integration') || path.includes('contract-integration')) return 'contract-integration';
        if (path.includes('contract')) return 'contract';
        if (path.includes('link')) return 'link';
        if (path.includes('user_analysis') || path.includes('user-analysis')) return 'user-analysis';
        return 'home';
    }

    generateNavigationHTML() {
        return `
            <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom mb-4">
                <div class="container-fluid">
                    <a class="navbar-brand fw-bold text-primary" href="/">
                        <i class="bi bi-graph-up"></i> 风险分析系统
                    </a>
                    
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    
                    <div class="collapse navbar-collapse" id="mainNavigation">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'agent' ? 'active fw-bold' : ''}" href="/agent_relationship.html">
                                    <i class="bi bi-diagram-3"></i> 代理分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'contract' ? 'active fw-bold' : ''}" href="/contract_analysis.html">
                                    <i class="bi bi-file-earmark-bar-graph"></i> 合约分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'contract-integration' ? 'active fw-bold' : ''}" href="/contract_integration.html">
                                    <i class="bi bi-graph-up"></i> 链路分析
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link ${this.currentPage === 'user-analysis' ? 'active fw-bold' : ''}" href="/user_analysis.html">
                                    <i class="bi bi-person-check"></i> 用户分析
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        `;
    }

    render(containerId = null) {
        const navigationHTML = this.generateNavigationHTML();
        
        if (containerId) {
            // 如果指定了容器ID，插入到指定容器
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = navigationHTML;
            }
        } else {
            // 否则插入到body的开头
            document.body.insertAdjacentHTML('afterbegin', navigationHTML);
        }
    }

    // 静态方法，方便在其他地方调用
    static init(containerId = null) {
        const navigation = new NavigationComponent();
        navigation.render(containerId);
        return navigation;
    }
}

// 导出供其他模块使用
export default NavigationComponent; 