/**
 * HTTP请求拦截器 - 处理认证相关的响应
 */
class HttpInterceptor {
    /**
     * 拦截fetch请求
     */
    static interceptFetch() {
        const originalFetch = window.fetch;
        
        window.fetch = async function(...args) {
            try {
                // 确保所有API请求都包含credentials
                if (args[1] && args[0].startsWith('/api/')) {
                    args[1].credentials = 'include';
                }
                
                const response = await originalFetch.apply(this, args);
                
                // 处理认证相关的响应
                if (response.status === 401) {
                    console.log('检测到401响应，用户需要重新登录');
                    
                    // 尝试解析响应获取更多信息
                    try {
                        const data = await response.clone().json();
                        if (data.code === 'SESSION_EXPIRED') {
                            HttpInterceptor.showMessage('会话已过期，请重新登录', 'warning');
                        } else if (data.code === 'AUTH_REQUIRED') {
                            HttpInterceptor.showMessage('请先登录', 'info');
                        } else {
                            HttpInterceptor.showMessage('认证失败，请重新登录', 'error');
                        }
                    } catch (e) {
                        HttpInterceptor.showMessage('认证失败，请重新登录', 'error');
                    }
                    
                    // 延迟跳转，让用户看到提示
                    setTimeout(() => {
                        AuthUtils.redirectToLogin();
                    }, 2000);
                    
                    return response;
                }
                
                if (response.status === 403) {
                    console.log('检测到403响应，用户权限不足');
                    
                    try {
                        const data = await response.clone().json();
                        const message = data.error || '您没有执行此操作的权限';
                        HttpInterceptor.showMessage(message, 'error');
                        
                        // 如果有权限相关信息，显示详细提示
                        if (data.required_role) {
                            const roleMap = { 'admin': '管理员', 'viewer': '查看者' };
                            const requiredRoleName = roleMap[data.required_role] || data.required_role;
                            const currentRoleName = roleMap[data.current_role] || data.current_role;
                            
                            HttpInterceptor.showPermissionDialog(
                                message,
                                `需要权限: ${requiredRoleName}`,
                                `当前权限: ${currentRoleName}`
                            );
                        }
                    } catch (e) {
                        HttpInterceptor.showMessage('权限不足', 'error');
                    }
                    
                    return response;
                }
                
                // 检查其他错误状态
                if (response.status >= 500) {
                    console.error('服务器错误:', response.status);
                    HttpInterceptor.showMessage('服务器内部错误，请稍后重试', 'error');
                }
                
                return response;
            } catch (error) {
                console.error('请求失败:', error);
                
                // 网络错误处理
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    HttpInterceptor.showMessage('网络连接失败，请检查网络后重试', 'error');
                } else {
                    HttpInterceptor.showMessage('请求失败，请重试', 'error');
                }
                
                throw error;
            }
        };
    }
    
    /**
     * 显示消息提示
     */
    static showMessage(message, type = 'info') {
        // 移除已有的提示
        const existingToast = document.querySelector('.http-interceptor-toast');
        if (existingToast) {
            existingToast.remove();
        }
        
        // 创建新的提示
        const toast = document.createElement('div');
        toast.className = 'http-interceptor-toast';
        
        const colors = {
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' }
        };
        
        const color = colors[type] || colors.info;
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${color.bg};
            border: 1px solid ${color.border};
            color: ${color.text};
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 350px;
            font-size: 14px;
            animation: slideInRight 0.3s ease-out;
        `;
        
        // 添加图标
        const icons = {
            info: 'ℹ️',
            warning: '⚠️',
            error: '❌',
            success: '✅'
        };
        
        toast.innerHTML = `
            <div style="display: flex; align-items: center;">
                <span style="margin-right: 8px; font-size: 16px;">${icons[type] || icons.info}</span>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: none;
                    border: none;
                    color: ${color.text};
                    margin-left: 12px;
                    cursor: pointer;
                    font-size: 16px;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">×</button>
            </div>
        `;
        
        // 添加动画样式
        if (!document.querySelector('#http-interceptor-styles')) {
            const style = document.createElement('style');
            style.id = 'http-interceptor-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(toast);
        
        // 自动消失
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }
    
    /**
     * 显示权限对话框
     */
    static showPermissionDialog(message, requiredPermission, currentPermission) {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            animation: fadeIn 0.3s ease-out;
        `;
        
        modal.innerHTML = `
            <div style="
                background: white;
                padding: 30px;
                border-radius: 12px;
                max-width: 450px;
                width: 90%;
                text-align: center;
                box-shadow: 0 10px 40px rgba(0,0,0,0.2);
                animation: scaleIn 0.3s ease-out;
            ">
                <div style="font-size: 64px; margin-bottom: 20px;">🔒</div>
                <h3 style="color: #dc3545; margin-bottom: 16px; font-size: 20px;">权限不足</h3>
                <p style="color: #6c757d; margin-bottom: 20px; line-height: 1.5;">${message}</p>
                
                <div style="
                    background: #f8f9fa;
                    padding: 16px;
                    border-radius: 8px;
                    margin-bottom: 24px;
                    text-align: left;
                ">
                    <div style="margin-bottom: 8px;">
                        <strong style="color: #495057;">${requiredPermission}</strong>
                    </div>
                    <div style="color: #6c757d;">
                        ${currentPermission}
                    </div>
                </div>
                
                <div style="display: flex; gap: 12px; justify-content: center;">
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                    ">确定</button>
                    <button onclick="AuthUtils.logout()" style="
                        background: #dc3545;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                    ">切换账户</button>
                </div>
            </div>
        `;
        
        // 添加动画样式
        if (!document.querySelector('#permission-dialog-styles')) {
            const style = document.createElement('style');
            style.id = 'permission-dialog-styles';
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes scaleIn {
                    from { transform: scale(0.7); opacity: 0; }
                    to { transform: scale(1); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    }
    
    /**
     * 初始化拦截器
     */
    static init() {
        this.interceptFetch();
        console.log('HTTP拦截器已初始化');
        
        // 添加全局错误处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
            
            // 如果是网络相关错误，显示友好提示
            if (event.reason && event.reason.message && 
                event.reason.message.includes('fetch')) {
                this.showMessage('网络请求失败，请检查网络连接', 'error');
            }
        });
    }
}

// 页面加载时自动初始化
document.addEventListener('DOMContentLoaded', () => {
    HttpInterceptor.init();
});

window.HttpInterceptor = HttpInterceptor; 