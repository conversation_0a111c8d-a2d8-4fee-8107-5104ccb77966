// Main JavaScript entry point for Webpack
// 引入日志管理器
import logger from '../utils/logger.js';

logger.info("main.js loaded via Webpack");

// Import Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css';
// Import Bootstrap Icons CSS
import 'bootstrap-icons/font/bootstrap-icons.css';

// Import custom styles for index.html
import '../index-styles.css'; // Assuming src/shared/index-styles.css
import '../css/navigation.css'; // 导入导航栏样式

// Import Bootstrap JS (all of it, or specific components if preferred)
// This makes `bootstrap` global object available if scripts depend on it that way,
// and also initializes Bootstrap components that rely on data attributes.
import 'bootstrap';

// Import navigation component
import NavigationComponent from '../components/navigation.js';

// Import 独立的合约上传服务（避免重复绑定）
import contractUploadService from './contract-upload-service.js';

// 动态导入代理商关系服务（仅在包含上传功能的页面）
// import '../../modules/agent-relationship/services/agent-relationship-service.js';

// Example: Importing custom CSS (ensure you have a CSS file and loaders configured)
// import '../css/main.css'; // Corrected path assuming src/css/main.css

// Any other global setup or initialization for index.html can go here.
// For example, event listeners that were previously in inline scripts on index.html

document.addEventListener('DOMContentLoaded', function() {
    logger.debug("DOM fully loaded and parsed from main.js for any further global setup.");
    
    // 初始化导航栏
    NavigationComponent.init('navigationContainer');
    
    // 初始化合约上传服务（仅在包含合约上传Modal的页面）
    if (document.getElementById('contractAnalysisModal')) {
        contractUploadService.init();
        logger.info('合约上传服务已在主页面初始化');
    }
    
    // 动态加载代理商关系服务（如果页面包含相关元素）
    if (document.getElementById('fileUpload') || document.getElementById('selectFileBtn')) {
        logger.info('检测到代理商上传元素，初始化服务...');
        
        // 直接初始化代理商关系功能，而不是动态导入整个模块
        initAgentRelationshipFeatures();
    }
    
    // 设置全局错误处理
    window.addEventListener('error', function(e) {
        logger.error('Global JavaScript error:', e.error);
    });
    
    // 设置未处理的Promise错误
    window.addEventListener('unhandledrejection', function(e) {
        logger.error('Unhandled promise rejection:', e.reason);
        e.preventDefault();
    });
    
    // Any further global setup for index.html, not covered by imported scripts, can go here.
});

// 代理商关系功能初始化函数
function initAgentRelationshipFeatures() {
    const fileUploadInput = document.getElementById('fileUpload');
    const selectFileBtn = document.getElementById('selectFileBtn');
    const uploadContainer = document.getElementById('uploadContainer');
    const processingDiv = document.getElementById('processing');
    const resultsContainer = document.getElementById('resultsContainer');
    
    // 任务恢复相关元素
    const taskIdSelector = document.getElementById('taskIdSelector');
    const refreshTasksBtn = document.getElementById('refreshTasksBtn');
    const loadTaskBtn = document.getElementById('loadTaskBtn');
    
    if (!fileUploadInput || !selectFileBtn) {
        logger.warn('代理商上传元素未找到，跳过初始化');
        return;
    }
    
    logger.info('开始初始化代理商关系功能...');
    
    // 初始化任务列表
    loadTaskList();
    
    // 文件选择按钮事件
    selectFileBtn.addEventListener('click', function() {
        logger.debug('用户点击选择文件按钮');
        fileUploadInput.click();
    });
    
    // 文件上传事件
    fileUploadInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            logger.info('用户选择了文件:', file.name);
            handleFileUpload(file);
        }
    });
    
    // 拖拽上传事件
    if (uploadContainer) {
        uploadContainer.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadContainer.classList.add('dragover');
        });
        
        uploadContainer.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadContainer.classList.remove('dragover');
        });
        
        uploadContainer.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadContainer.classList.remove('dragover');
            
            const file = e.dataTransfer.files[0];
            if (file) {
                logger.info('用户拖拽了文件:', file.name);
                handleFileUpload(file);
            }
        });
    }
    
    // 任务恢复相关事件
    if (refreshTasksBtn) {
        refreshTasksBtn.addEventListener('click', function() {
            logger.info('刷新任务列表');
            loadTaskList();
        });
    }
    
    if (taskIdSelector) {
        taskIdSelector.addEventListener('change', function() {
            const selectedTaskId = this.value;
            if (loadTaskBtn) {
                loadTaskBtn.disabled = !selectedTaskId;
            }
        });
    }
    
    if (loadTaskBtn) {
        loadTaskBtn.addEventListener('click', function() {
            const selectedTaskId = taskIdSelector ? taskIdSelector.value : '';
            if (selectedTaskId) {
                logger.info('加载任务:', selectedTaskId);
                loadTask(selectedTaskId);
            }
        });
    }
    
    logger.info('代理商关系功能初始化完成');
}

// 处理文件上传
async function handleFileUpload(file) {
    const processingDiv = document.getElementById('processing');
    const uploadContainer = document.getElementById('uploadContainer');
    const resultsContainer = document.getElementById('resultsContainer');
    
    try {
        logger.info('开始上传文件:', file.name);
        
        // 显示处理状态
        if (uploadContainer) uploadContainer.style.display = 'none';
        if (processingDiv) processingDiv.style.display = 'block';
        if (resultsContainer) resultsContainer.style.display = 'none';
        
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('async', 'true'); // 使用异步模式
        
        // 上传文件
        const response = await fetch('/api/agent/upload', {
            method: 'POST',
            credentials: 'include',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`上传失败: HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        if (data.task_id) {
            logger.info('文件上传成功，任务ID:', data.task_id);
            // 开始轮询任务状态
            startTaskPolling(data.task_id);
        } else {
            // 同步模式，直接显示结果
            logger.info('同步模式，直接显示结果');
            displayResults(data);
        }
        
    } catch (error) {
        logger.error('文件上传失败:', error);
        showAlert('上传失败: ' + error.message, 'danger');
        
        // 恢复上传界面
        if (uploadContainer) uploadContainer.style.display = 'block';
        if (processingDiv) processingDiv.style.display = 'none';
    }
}

// 简化的任务轮询
function startTaskPolling(taskId) {
    logger.info('开始轮询任务:', taskId);
    
    const pollInterval = setInterval(async () => {
        try {
            const response = await fetch(`/api/agent/result/${taskId}`, {
                credentials: 'include'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const status = await response.json();
            logger.debug('任务状态:', status.status);
            
            if (status.status === 'completed') {
                clearInterval(pollInterval);
                logger.info('任务完成');
                displayResults(status.result || status);
                showAlert('分析完成!', 'success');
            } else if (status.status === 'failed') {
                clearInterval(pollInterval);
                logger.error('任务失败:', status.error || status.message);
                showAlert('分析失败: ' + (status.error || status.message), 'danger');
                
                // 恢复上传界面
                const uploadContainer = document.getElementById('uploadContainer');
                const processingDiv = document.getElementById('processing');
                if (uploadContainer) uploadContainer.style.display = 'block';
                if (processingDiv) processingDiv.style.display = 'none';
            }
            
        } catch (error) {
            clearInterval(pollInterval);
            logger.error('轮询失败:', error);
            showAlert('检查任务状态失败: ' + error.message, 'danger');
            
            // 恢复上传界面
            const uploadContainer = document.getElementById('uploadContainer');
            const processingDiv = document.getElementById('processing');
            if (uploadContainer) uploadContainer.style.display = 'block';
            if (processingDiv) processingDiv.style.display = 'none';
        }
    }, 2000);
    
    // 5分钟超时
    setTimeout(() => {
        clearInterval(pollInterval);
        logger.warn('任务轮询超时');
        showAlert('任务处理超时，请刷新页面查看结果', 'warning');
    }, 5 * 60 * 1000);
}

// 简化的结果显示
function displayResults(data) {
    const processingDiv = document.getElementById('processing');
    const resultsContainer = document.getElementById('resultsContainer');
    
    if (processingDiv) processingDiv.style.display = 'none';
    if (resultsContainer) resultsContainer.style.display = 'block';
    
    // 更新摘要统计
    const deviceSharedCount = document.getElementById('deviceSharedCount');
    const ipSharedCount = document.getElementById('ipSharedCount');
    const bothSharedCount = document.getElementById('bothSharedCount');
    
    if (deviceSharedCount) {
        deviceSharedCount.textContent = data.device_shared ? data.device_shared.length : 0;
    }
    if (ipSharedCount) {
        ipSharedCount.textContent = data.ip_shared ? data.ip_shared.length : 0;
    }
    if (bothSharedCount) {
        bothSharedCount.textContent = data.both_shared ? data.both_shared.length : 0;
    }
    
    logger.info('结果显示完成');
}

// 显示提示信息
function showAlert(message, type = 'info') {
    logger.info(`Alert [${type}]: ${message}`);
    
    // 创建或获取alert容器
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.style.position = 'fixed';
        alertContainer.style.top = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        alertContainer.style.maxWidth = '400px';
        document.body.appendChild(alertContainer);
    }
    
    // 创建alert元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 3000);
}

// 加载任务列表
async function loadTaskList() {
    const taskIdSelector = document.getElementById('taskIdSelector');
    
    if (!taskIdSelector) {
        logger.warn('任务选择器不存在，跳过任务列表加载');
        return;
    }
    
    try {
        logger.info('正在加载任务列表...');
        
        const response = await fetch('/api/agent/tasks', {
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.status === 'success') {
            const tasks = data.tasks || [];
            updateTaskSelector(tasks);
            logger.info(`任务列表加载完成，共${tasks.length}个任务`);
        } else {
            throw new Error(data.error || '获取任务列表失败');
        }
    } catch (error) {
        logger.error('加载任务列表失败:', error);
        updateTaskSelector([], '加载失败，请刷新重试');
        showAlert('加载任务列表失败: ' + error.message, 'warning');
    }
}

// 更新任务选择器
function updateTaskSelector(tasks, errorMessage = null) {
    const taskIdSelector = document.getElementById('taskIdSelector');
    
    if (!taskIdSelector) return;
    
    // 清空现有选项
    taskIdSelector.innerHTML = '';
    
    if (errorMessage) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = errorMessage;
        option.disabled = true;
        taskIdSelector.appendChild(option);
        return;
    }
    
    // 添加默认选项
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = tasks.length > 0 ? '请手动选择要恢复的任务...' : '暂无可用任务';
    taskIdSelector.appendChild(defaultOption);
    
    // 添加任务选项
    tasks.forEach(task => {
        const option = document.createElement('option');
        option.value = task.task_id;
        
        // 格式化显示文本
        const createdDate = new Date(task.created_at).toLocaleString();
        const memberCount = task.total_members || 0;
        const filename = task.filename || '未知文件';
        
        option.textContent = `${filename} (${memberCount}个用户, ${createdDate})`;
        taskIdSelector.appendChild(option);
    });
    
    // 重置加载按钮状态
    const loadTaskBtn = document.getElementById('loadTaskBtn');
    if (loadTaskBtn) {
        loadTaskBtn.disabled = true;
    }
}

// 加载指定任务
async function loadTask(taskId) {
    const uploadContainer = document.getElementById('uploadContainer');
    const processingDiv = document.getElementById('processing');
    const resultsContainer = document.getElementById('resultsContainer');
    
    if (!taskId) {
        showAlert('请选择一个任务', 'warning');
        return;
    }
    
    try {
        logger.info('正在加载任务:', taskId);
        
        // 显示加载状态
        if (uploadContainer) uploadContainer.style.display = 'none';
        if (processingDiv) processingDiv.style.display = 'block';
        if (resultsContainer) resultsContainer.style.display = 'none';
        
        showAlert(`正在加载任务 ${taskId}...`, 'info');
        
        // 获取任务结果
        const response = await fetch(`/api/agent/result/${taskId}`, {
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.error) {
            throw new Error(data.error);
        }
        
        if (!data.result) {
            throw new Error('任务结果为空');
        }
        
        // 显示结果
        displayResults(data.result);
        
        if (processingDiv) processingDiv.style.display = 'none';
        
        showAlert(`任务 ${taskId} 加载成功`, 'success');
        
        logger.info('任务加载完成');
        
    } catch (error) {
        logger.error('加载任务失败:', error);
        showAlert('加载任务失败: ' + error.message, 'danger');
        
        if (uploadContainer) uploadContainer.style.display = 'block';
        if (processingDiv) processingDiv.style.display = 'none';
    }
} 