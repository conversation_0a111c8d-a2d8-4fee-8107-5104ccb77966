#!/usr/bin/env python3
"""
调试两个用户数据差异的脚本
"""

import requests
import json
import sys

def test_user_api(user_id):
    """测试单个用户的API响应"""
    print(f"\n{'='*60}")
    print(f"测试用户: {user_id}")
    print(f"{'='*60}")
    
    try:
        # 调用API
        url = f'http://localhost:5005/api/user-analysis/complete-analysis/{user_id}'
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # 检查关键字段
            print("\n--- 关键字段检查 ---")
            
            # 1. advantage_coins
            advantage_coins = data.get('coin_analysis', {}).get('advantage_coins')
            print(f"advantage_coins:")
            print(f"  类型: {type(advantage_coins)}")
            print(f"  是否为数组: {isinstance(advantage_coins, list)}")
            if advantage_coins:
                print(f"  长度: {len(advantage_coins)}")
                print(f"  内容预览: {str(advantage_coins)[:100]}...")
            else:
                print(f"  值: {advantage_coins}")
            
            # 2. favorite_contracts
            favorite_contracts = data.get('trading_preferences', {}).get('coin_preference', {}).get('favorite_contracts')
            print(f"\nfavorite_contracts:")
            print(f"  类型: {type(favorite_contracts)}")
            print(f"  是否为数组: {isinstance(favorite_contracts, list)}")
            if favorite_contracts:
                print(f"  长度: {len(favorite_contracts)}")
                print(f"  内容: {favorite_contracts}")
            else:
                print(f"  值: {favorite_contracts}")
            
            # 3. coin_performance_ranking
            coin_ranking = data.get('coin_analysis', {}).get('coin_performance_ranking')
            print(f"\ncoin_performance_ranking:")
            print(f"  类型: {type(coin_ranking)}")
            print(f"  是否为数组: {isinstance(coin_ranking, list)}")
            if coin_ranking:
                print(f"  长度: {len(coin_ranking)}")
                print(f"  内容预览: {str(coin_ranking)[:100]}...")
            else:
                print(f"  值: {coin_ranking}")
            
            # 4. 测试slice操作
            print(f"\n--- Slice操作测试 ---")
            
            # 测试advantage_coins
            try:
                if isinstance(advantage_coins, list):
                    result = advantage_coins[:5]
                    print(f"advantage_coins slice成功: {len(result)} 个元素")
                elif advantage_coins and isinstance(advantage_coins, dict):
                    result = list(advantage_coins.values())[:5]
                    print(f"advantage_coins 对象转数组slice成功: {len(result)} 个元素")
                else:
                    print(f"advantage_coins slice跳过: 数据为空或类型不支持")
            except Exception as e:
                print(f"advantage_coins slice失败: {e}")
            
            # 测试favorite_contracts
            try:
                if isinstance(favorite_contracts, list):
                    result = favorite_contracts[:5]
                    print(f"favorite_contracts slice成功: {len(result)} 个元素")
                elif favorite_contracts and isinstance(favorite_contracts, dict):
                    result = list(favorite_contracts.values())[:5]
                    print(f"favorite_contracts 对象转数组slice成功: {len(result)} 个元素")
                else:
                    print(f"favorite_contracts slice跳过: 数据为空或类型不支持")
            except Exception as e:
                print(f"favorite_contracts slice失败: {e}")
            
            return True
            
        else:
            print(f"API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"API调用异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 用户数据差异调试工具")
    print("对比两个用户的API响应数据")
    
    # 测试的两个用户
    users = [
        '9c8db566f6354095bf237e962d0ab4c5',  # 有问题的用户
        '4594f46c189640ea9072ba844ff5ad52'   # 正常的用户
    ]
    
    results = {}
    
    for user_id in users:
        results[user_id] = test_user_api(user_id)
    
    # 总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    
    for user_id, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{user_id}: {status}")

if __name__ == "__main__":
    main()
