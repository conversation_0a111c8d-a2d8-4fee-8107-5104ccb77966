#!/usr/bin/env python3
"""
开仓价格设置策略分析
分析不同开仓价格设置方法对后续分析的影响
"""

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def analyze_price_strategies():
    """分析不同开仓价格设置策略的影响"""
    
    # 模拟数据：只有平仓记录的情况
    test_cases = [
        {
            'name': '多头盈利',
            'close_amount': 1000.0,
            'close_price': 0.1,
            'profit': 50.0,
            'side': 'long'
        },
        {
            'name': '多头亏损', 
            'close_amount': 1000.0,
            'close_price': 0.1,
            'profit': -30.0,
            'side': 'long'
        },
        {
            'name': '空头盈利',
            'close_amount': 1000.0,
            'close_price': 0.1,
            'profit': 40.0,
            'side': 'short'
        },
        {
            'name': '空头亏损',
            'close_amount': 1000.0,
            'close_price': 0.1,
            'profit': -25.0,
            'side': 'short'
        }
    ]
    
    print("=== 开仓价格设置策略对比分析 ===\n")
    
    for case in test_cases:
        print(f"📊 {case['name']} 案例分析:")
        print(f"   平仓金额: {case['close_amount']:.2f}")
        print(f"   平仓价格: {case['close_price']:.4f}")
        print(f"   实际盈亏: {case['profit']:.2f}")
        print(f"   方向: {'多头' if case['side'] == 'long' else '空头'}")
        
        # 计算数量
        quantity = case['close_amount'] / case['close_price']
        
        # 策略1：开仓价格 = 平仓价格
        strategy1_open_price = case['close_price']
        strategy1_open_amount = strategy1_open_price * quantity
        strategy1_theoretical_profit = 0.0  # 开仓价格=平仓价格，理论盈亏为0
        
        # 策略2：基于盈亏反推开仓价格（我们当前的方案）
        if case['side'] == 'long':
            # 多头：盈亏 = (平仓价格 - 开仓价格) × 数量
            strategy2_open_price = case['close_price'] - (case['profit'] / quantity)
        else:
            # 空头：盈亏 = (开仓价格 - 平仓价格) × 数量
            strategy2_open_price = case['close_price'] + (case['profit'] / quantity)
        
        strategy2_open_amount = strategy2_open_price * quantity
        strategy2_theoretical_profit = case['profit']  # 理论盈亏=实际盈亏
        
        # 策略3：开仓价格 = 平仓价格 ± 5%（简单估算）
        price_diff_5pct = case['close_price'] * 0.05
        if case['profit'] > 0:  # 盈利
            if case['side'] == 'long':
                strategy3_open_price = case['close_price'] - price_diff_5pct  # 多头盈利：低买高卖
            else:
                strategy3_open_price = case['close_price'] + price_diff_5pct  # 空头盈利：高卖低买
        else:  # 亏损
            if case['side'] == 'long':
                strategy3_open_price = case['close_price'] + price_diff_5pct  # 多头亏损：高买低卖
            else:
                strategy3_open_price = case['close_price'] - price_diff_5pct  # 空头亏损：低卖高买
        
        strategy3_open_amount = strategy3_open_price * quantity
        if case['side'] == 'long':
            strategy3_theoretical_profit = (case['close_price'] - strategy3_open_price) * quantity
        else:
            strategy3_theoretical_profit = (strategy3_open_price - case['close_price']) * quantity
        
        print(f"\n   📈 策略对比:")
        print(f"   策略1 (开仓价格=平仓价格):")
        print(f"     开仓价格: {strategy1_open_price:.4f}")
        print(f"     开仓金额: {strategy1_open_amount:.2f}")
        print(f"     理论盈亏: {strategy1_theoretical_profit:.2f}")
        print(f"     误差: {abs(strategy1_theoretical_profit - case['profit']):.2f}")
        
        print(f"   策略2 (基于盈亏反推) ⭐ 推荐:")
        print(f"     开仓价格: {strategy2_open_price:.4f}")
        print(f"     开仓金额: {strategy2_open_amount:.2f}")
        print(f"     理论盈亏: {strategy2_theoretical_profit:.2f}")
        print(f"     误差: {abs(strategy2_theoretical_profit - case['profit']):.2f}")
        
        print(f"   策略3 (±5%估算):")
        print(f"     开仓价格: {strategy3_open_price:.4f}")
        print(f"     开仓金额: {strategy3_open_amount:.2f}")
        print(f"     理论盈亏: {strategy3_theoretical_profit:.2f}")
        print(f"     误差: {abs(strategy3_theoretical_profit - case['profit']):.2f}")
        
        print(f"\n" + "="*60 + "\n")

def analyze_downstream_impacts():
    """分析对下游分析的影响"""
    
    print("=== 对下游分析指标的影响 ===\n")
    
    impacts = {
        "策略1 (开仓价格=平仓价格)": {
            "胜率计算": "❌ 严重影响 - 所有修复的持仓理论盈亏为0，胜率计算失真",
            "盈亏比计算": "❌ 严重影响 - 平均盈利和平均亏损计算错误",
            "收益率分析": "❌ 严重影响 - ROI计算完全错误",
            "价格分析": "❌ 中等影响 - 开仓价格不真实，影响价格相关分析",
            "持仓时长分析": "✅ 无影响 - 时间计算不受影响",
            "交易频率分析": "✅ 无影响 - 交易次数统计不受影响"
        },
        "策略2 (基于盈亏反推)": {
            "胜率计算": "✅ 无影响 - 盈亏保持真实，胜率计算准确",
            "盈亏比计算": "✅ 无影响 - 盈亏数据保持真实",
            "收益率分析": "✅ 基本无影响 - ROI计算基于真实盈亏",
            "价格分析": "⚠️ 轻微影响 - 开仓价格是推算的，但基于真实盈亏",
            "持仓时长分析": "⚠️ 轻微影响 - 开仓时间是估算的",
            "交易频率分析": "✅ 无影响 - 交易次数统计不受影响"
        },
        "策略3 (±5%估算)": {
            "胜率计算": "⚠️ 中等影响 - 盈亏计算有误差，但方向正确",
            "盈亏比计算": "⚠️ 中等影响 - 盈亏金额有误差",
            "收益率分析": "⚠️ 中等影响 - ROI计算有误差",
            "价格分析": "⚠️ 中等影响 - 开仓价格是估算的",
            "持仓时长分析": "⚠️ 轻微影响 - 开仓时间是估算的",
            "交易频率分析": "✅ 无影响 - 交易次数统计不受影响"
        }
    }
    
    for strategy, impact_dict in impacts.items():
        print(f"📊 {strategy}:")
        for metric, impact in impact_dict.items():
            print(f"   {metric}: {impact}")
        print()

def main():
    """主函数"""
    analyze_price_strategies()
    analyze_downstream_impacts()
    
    print("=== 总结和建议 ===")
    print("🎯 推荐策略：策略2 (基于盈亏反推开仓价格)")
    print("✅ 优势：")
    print("   - 保持盈亏数据的真实性")
    print("   - 对关键分析指标（胜率、盈亏比、ROI）影响最小")
    print("   - 数学逻辑严谨，理论盈亏=实际盈亏")
    print("⚠️ 注意事项：")
    print("   - 开仓价格是推算的，不是真实的历史价格")
    print("   - 开仓时间是估算的（前一天23:55）")
    print("   - 适用于数据修复，不适用于实时交易分析")

if __name__ == "__main__":
    main()
