#!/usr/bin/env python3
"""
更新用户画像中的异常交易分析数据
从contract_risk_details表重新计算并更新user_trading_profiles表中的异常字段
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import logging
from database.duckdb_manager import DuckDBManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_abnormal_analysis():
    """更新所有用户的异常交易分析数据"""
    db_manager = DuckDBManager()
    
    try:
        # 1. 获取所有有异常记录的用户
        users_with_anomalies = db_manager.execute_sql("""
        SELECT 
            member_id,
            COUNT(*) as anomaly_count
        FROM contract_risk_details 
        GROUP BY member_id 
        ORDER BY anomaly_count DESC
        """)
        
        logger.info(f"找到 {len(users_with_anomalies)} 个有异常记录的用户")
        
        if not users_with_anomalies:
            logger.warning("没有找到异常交易记录，无需更新")
            return True
        
        # 2. 为每个用户重新计算异常分析数据
        updated_count = 0
        for i, user in enumerate(users_with_anomalies):
            member_id = user['member_id']
            anomaly_count = user['anomaly_count']
            
            logger.info(f"处理用户 {i+1}/{len(users_with_anomalies)}: {member_id} ({anomaly_count}条异常记录)")
            
            try:
                # 计算该用户的异常分析数据
                abnormal_data = calculate_user_abnormal_analysis(db_manager, member_id)
                
                if abnormal_data:
                    # 更新数据库
                    update_user_profile_abnormal_data(db_manager, member_id, abnormal_data)
                    updated_count += 1
                    logger.info(f"✅ 用户 {member_id} 异常数据更新成功")
                else:
                    logger.warning(f"⚠️  用户 {member_id} 异常数据计算失败")
                    
            except Exception as e:
                logger.error(f"❌ 处理用户 {member_id} 失败: {e}")
                continue
        
        logger.info(f"🎉 异常分析数据更新完成！成功更新 {updated_count}/{len(users_with_anomalies)} 个用户")
        return True
        
    except Exception as e:
        logger.error(f"更新异常分析数据失败: {e}")
        return False

def calculate_user_abnormal_analysis(db_manager: DuckDBManager, member_id: str):
    """计算单个用户的异常分析数据"""
    try:
        # 查询用户的异常交易记录，按类型分组统计
        sql = """
        SELECT 
            detection_type,
            COUNT(*) as count,
            SUM(COALESCE(abnormal_volume, 0)) as total_volume,
            AVG(COALESCE(risk_score, 0)) as avg_risk_score
        FROM contract_risk_details 
        WHERE member_id = ?
        GROUP BY detection_type
        """
        
        results = db_manager.execute_sql(sql, [member_id])
        
        # 初始化数据
        wash_trading_volume = 0.0
        wash_trading_count = 0
        high_frequency_volume = 0.0
        high_frequency_count = 0
        funding_arbitrage_volume = 0.0
        funding_arbitrage_count = 0
        
        # 统计各类型数据
        for row in results:
            detection_type = row['detection_type'].lower()
            count = int(row['count'] or 0)
            volume = float(row['total_volume'] or 0)
            
            if detection_type == 'wash_trading':
                wash_trading_volume = volume
                wash_trading_count = count
            elif detection_type == 'high_frequency':
                high_frequency_volume = volume
                high_frequency_count = count
            elif detection_type == 'funding_arbitrage':
                funding_arbitrage_volume = volume
                funding_arbitrage_count = count
        
        # 计算总异常交易量
        total_abnormal_volume = wash_trading_volume + high_frequency_volume + funding_arbitrage_volume
        
        # 获取用户总交易量（用于计算比例）
        total_volume_result = db_manager.execute_sql("""
        SELECT total_volume FROM user_trading_profiles 
        WHERE member_id = ?
        """, [member_id])
        
        total_volume = float(total_volume_result[0]['total_volume']) if total_volume_result else 0
        
        # 计算异常比例
        abnormal_ratio = total_abnormal_volume / total_volume if total_volume > 0 else 0
        
        # 计算风险事件总数
        risk_events_count = wash_trading_count + high_frequency_count + funding_arbitrage_count
        
        return {
            'abnormal_volume': total_abnormal_volume,
            'abnormal_ratio': abnormal_ratio,
            'wash_trading_volume': wash_trading_volume,
            'high_frequency_volume': high_frequency_volume,
            'funding_arbitrage_volume': funding_arbitrage_volume,
            'risk_events_count': risk_events_count
        }
        
    except Exception as e:
        logger.error(f"计算用户 {member_id} 异常分析数据失败: {e}")
        return None

def update_user_profile_abnormal_data(db_manager: DuckDBManager, member_id: str, abnormal_data: dict):
    """更新用户画像中的异常分析数据"""
    try:
        sql = """
        UPDATE user_trading_profiles 
        SET 
            abnormal_volume = ?,
            abnormal_ratio = ?,
            wash_trading_volume = ?,
            high_frequency_volume = ?,
            funding_arbitrage_volume = ?,
            risk_events_count = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE member_id = ?
        """
        
        params = [
            abnormal_data['abnormal_volume'],
            abnormal_data['abnormal_ratio'],
            abnormal_data['wash_trading_volume'],
            abnormal_data['high_frequency_volume'],
            abnormal_data['funding_arbitrage_volume'],
            abnormal_data['risk_events_count'],
            member_id
        ]
        
        db_manager.execute_sql(sql, params)
        
        logger.debug(f"用户 {member_id} 异常数据更新: "
                    f"总异常量={abnormal_data['abnormal_volume']:.2f}, "
                    f"对敲={abnormal_data['wash_trading_volume']:.2f}, "
                    f"高频={abnormal_data['high_frequency_volume']:.2f}")
        
    except Exception as e:
        logger.error(f"更新用户 {member_id} 画像数据失败: {e}")
        raise

def verify_update():
    """验证更新结果"""
    db_manager = DuckDBManager()
    
    try:
        # 检查更新后的数据
        updated_profiles = db_manager.execute_sql("""
        SELECT COUNT(*) as count
        FROM user_trading_profiles 
        WHERE abnormal_volume > 0 OR wash_trading_volume > 0 OR high_frequency_volume > 0
        """)
        
        updated_count = updated_profiles[0]['count']
        logger.info(f"📊 更新验证: {updated_count} 个用户画像包含异常数据")
        
        # 显示样本数据
        samples = db_manager.execute_sql("""
        SELECT 
            member_id,
            abnormal_volume,
            wash_trading_volume,
            high_frequency_volume,
            funding_arbitrage_volume,
            risk_events_count
        FROM user_trading_profiles 
        WHERE abnormal_volume > 0
        ORDER BY abnormal_volume DESC
        LIMIT 5
        """)
        
        logger.info("📋 更新后的样本数据:")
        for sample in samples:
            logger.info(f"  用户: {sample['member_id']}")
            logger.info(f"    总异常量: {sample['abnormal_volume']}")
            logger.info(f"    对敲交易: {sample['wash_trading_volume']}")
            logger.info(f"    高频交易: {sample['high_frequency_volume']}")
            logger.info(f"    套利交易: {sample['funding_arbitrage_volume']}")
            logger.info(f"    风险事件: {sample['risk_events_count']}")
        
        return updated_count > 0
        
    except Exception as e:
        logger.error(f"验证更新结果失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始更新用户画像异常交易分析数据...")
    
    try:
        # 1. 更新异常分析数据
        success = update_abnormal_analysis()
        
        if success:
            # 2. 验证更新结果
            verify_success = verify_update()
            
            if verify_success:
                logger.info("🎉 异常交易分析数据更新完成！")
                logger.info("💡 现在前端应该能正确显示异常交易数据了")
                return True
            else:
                logger.warning("⚠️  数据更新完成，但验证失败")
                return False
        else:
            logger.error("❌ 数据更新失败")
            return False
            
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
