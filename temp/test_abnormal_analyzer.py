#!/usr/bin/env python3
"""
测试异常交易分析器
验证从contract_risk_details表计算异常交易数据的逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
from database.duckdb_manager import DuckDBManager

def test_abnormal_analysis():
    """测试异常交易分析"""
    print("🧪 测试异常交易分析器...")
    
    # 获取有异常交易记录的用户
    db_manager = DuckDBManager()
    
    # 查找有异常交易记录的用户
    users_with_anomalies = db_manager.execute_sql("""
    SELECT 
        member_id,
        COUNT(*) as anomaly_count,
        GROUP_CONCAT(DISTINCT detection_type) as types
    FROM contract_risk_details 
    GROUP BY member_id 
    ORDER BY anomaly_count DESC 
    LIMIT 3
    """)
    
    if not users_with_anomalies:
        print("❌ 没有找到有异常交易记录的用户")
        return False
    
    print(f"📋 找到 {len(users_with_anomalies)} 个有异常记录的用户:")
    for user in users_with_anomalies:
        print(f"  用户: {user['member_id']}, 异常记录: {user['anomaly_count']}, 类型: {user['types']}")
    
    # 测试第一个用户
    test_user = users_with_anomalies[0]
    user_id = test_user['member_id']
    
    print(f"\n🔍 测试用户: {user_id}")
    
    # 直接测试异常分析方法
    analyzer = UserBehaviorAnalyzer()
    
    # 获取用户的持仓数据（用于基础指标计算）
    positions = db_manager.execute_sql("""
    SELECT * FROM position_analysis 
    WHERE member_id = ?
    ORDER BY open_time
    LIMIT 10
    """, [user_id])
    
    if not positions:
        print(f"❌ 用户 {user_id} 没有持仓数据")
        return False
    
    print(f"📊 用户持仓记录: {len(positions)} 条")
    
    # 计算基础指标（简化版）
    class MockBasicMetrics:
        def __init__(self, total_volume):
            self.total_volume = total_volume
    
    # 计算总交易量
    total_volume = sum(float(p.get('volume', 0) or 0) for p in positions)
    basic_metrics = MockBasicMetrics(total_volume)
    
    print(f"📈 总交易量: {total_volume}")
    
    # 测试异常分析
    try:
        abnormal_analysis = analyzer._calculate_abnormal_analysis(user_id, positions, basic_metrics)
        
        print("\n📊 异常分析结果:")
        print(f"  总异常量: {abnormal_analysis.abnormal_volume}")
        print(f"  异常比例: {abnormal_analysis.abnormal_ratio:.4f}")
        print(f"  对敲交易量: {abnormal_analysis.wash_trading_volume}")
        print(f"  高频交易量: {abnormal_analysis.high_frequency_volume}")
        print(f"  套利交易量: {abnormal_analysis.funding_arbitrage_volume}")
        print(f"  风险事件数: {abnormal_analysis.risk_events_count}")
        
        # 检查详细信息
        if hasattr(abnormal_analysis, 'abnormal_details'):
            details = abnormal_analysis.abnormal_details
            print("\n📋 详细信息:")
            print(f"  对敲交易笔数: {details.get('wash_trading_count', 0)}")
            print(f"  高频交易笔数: {details.get('high_frequency_count', 0)}")
            print(f"  套利交易笔数: {details.get('funding_arbitrage_count', 0)}")
        
        # 验证结果
        if abnormal_analysis.abnormal_volume > 0:
            print("\n✅ 异常分析计算成功，检测到异常交易")
            return True
        else:
            print("\n⚠️  异常分析计算完成，但未检测到异常交易")
            return False
            
    except Exception as e:
        print(f"\n❌ 异常分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_query():
    """直接测试数据库查询"""
    print("\n🔍 直接测试数据库查询...")
    
    db_manager = DuckDBManager()
    
    # 获取一个有异常记录的用户
    user_result = db_manager.execute_sql("""
    SELECT member_id FROM contract_risk_details 
    GROUP BY member_id 
    HAVING COUNT(*) > 0 
    LIMIT 1
    """)
    
    if not user_result:
        print("❌ 没有找到有异常记录的用户")
        return False
    
    user_id = user_result[0]['member_id']
    print(f"测试用户: {user_id}")
    
    # 执行与分析器相同的查询
    query = """
    SELECT 
        detection_type,
        COUNT(*) as count,
        SUM(abnormal_volume) as volume,
        AVG(risk_score) as avg_score
    FROM contract_risk_details 
    WHERE member_id = ?
    GROUP BY detection_type
    """
    
    results = db_manager.execute_sql(query, [user_id])
    
    print("📋 查询结果:")
    for row in results:
        print(f"  类型: {row['detection_type']}")
        print(f"  笔数: {row['count']}")
        print(f"  总量: {row['volume']}")
        print(f"  平均分: {row['avg_score']}")
        print()
    
    return len(results) > 0

def main():
    """主测试函数"""
    print("🚀 开始测试异常交易分析器...")
    print("=" * 60)
    
    # 测试1: 直接数据库查询
    test1_passed = test_direct_query()
    
    # 测试2: 异常分析器
    test2_passed = test_abnormal_analysis()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"  直接查询测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  异常分析器测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！异常分析器工作正常！")
        return True
    else:
        print("\n🚨 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
