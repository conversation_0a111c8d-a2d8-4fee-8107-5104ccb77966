#!/usr/bin/env python3
"""
测试修复后的用户画像生成逻辑
验证异常分析数据是否能正确计算和保存
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import logging
from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
from database.duckdb_manager import DuckDBManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_abnormal_analysis_calculation():
    """测试异常分析计算逻辑"""
    print("🧪 测试异常分析计算逻辑...")
    
    try:
        # 选择一个有异常记录的用户
        db_manager = DuckDBManager()
        
        # 获取有异常记录的用户
        users_with_anomalies = db_manager.execute_sql("""
        SELECT 
            member_id,
            COUNT(*) as anomaly_count
        FROM contract_risk_details 
        GROUP BY member_id 
        ORDER BY anomaly_count DESC 
        LIMIT 3
        """)
        
        if not users_with_anomalies:
            print("❌ 没有找到有异常记录的用户")
            return False
        
        test_user = users_with_anomalies[0]
        user_id = test_user['member_id']
        
        print(f"📋 测试用户: {user_id} (异常记录: {test_user['anomaly_count']}条)")
        
        # 获取用户的持仓数据
        positions = db_manager.execute_sql("""
        SELECT * FROM position_analysis 
        WHERE member_id = ?
        ORDER BY open_time
        """, [user_id])
        
        if not positions:
            print(f"❌ 用户 {user_id} 没有持仓数据")
            return False
        
        print(f"📊 用户持仓记录: {len(positions)} 条")
        
        # 创建分析器并测试异常分析
        analyzer = UserBehaviorAnalyzer()
        
        # 执行完整的用户行为分析
        analysis_result = analyzer.analyze_user_behavior(user_id, positions)
        
        if analysis_result and analysis_result.abnormal_analysis:
            abnormal = analysis_result.abnormal_analysis
            
            print("\n📊 异常分析计算结果:")
            print(f"  总异常量: {abnormal.abnormal_volume}")
            print(f"  异常比例: {abnormal.abnormal_ratio:.4f}")
            print(f"  对敲交易量: {abnormal.wash_trading_volume}")
            print(f"  高频交易量: {abnormal.high_frequency_volume}")
            print(f"  套利交易量: {abnormal.funding_arbitrage_volume}")
            print(f"  风险事件数: {abnormal.risk_events_count}")
            
            # 检查详细信息
            if abnormal.abnormal_details:
                details = abnormal.abnormal_details
                print("\n📋 详细信息:")
                print(f"  对敲交易笔数: {details.get('wash_trading_count', 0)}")
                print(f"  高频交易笔数: {details.get('high_frequency_count', 0)}")
                print(f"  套利交易笔数: {details.get('funding_arbitrage_count', 0)}")
            
            # 验证数据一致性
            has_abnormal_data = (
                abnormal.abnormal_volume > 0 or
                abnormal.wash_trading_volume > 0 or
                abnormal.high_frequency_volume > 0 or
                abnormal.risk_events_count > 0
            )
            
            if has_abnormal_data:
                print("\n✅ 异常分析计算成功，检测到异常交易数据")
                return True
            else:
                print("\n⚠️  异常分析计算完成，但未检测到异常交易")
                return False
        else:
            print("\n❌ 异常分析计算失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_profile_data_mapping():
    """测试用户画像数据映射逻辑"""
    print("\n🔄 测试用户画像数据映射逻辑...")
    
    try:
        # 导入修复后的脚本
        sys.path.append('./scripts')
        from populate_user_trading_profiles import UserTradingProfileGenerator
        
        generator = UserTradingProfileGenerator()
        
        # 选择一个有异常记录的用户
        db_manager = DuckDBManager()
        
        users_with_anomalies = db_manager.execute_sql("""
        SELECT member_id FROM contract_risk_details 
        GROUP BY member_id 
        HAVING COUNT(*) > 0 
        LIMIT 1
        """)
        
        if not users_with_anomalies:
            print("❌ 没有找到有异常记录的用户")
            return False
        
        user_id = users_with_anomalies[0]['member_id']
        print(f"📋 测试用户: {user_id}")
        
        # 测试画像生成
        profile_data = generator._generate_user_profile(user_id)
        
        if profile_data:
            print("📊 生成的画像数据:")
            print(f"  用户ID: {profile_data.get('member_id')}")
            print(f"  总异常量: {profile_data.get('abnormal_volume', 0)}")
            print(f"  异常比例: {profile_data.get('abnormal_ratio', 0):.4f}")
            print(f"  对敲交易量: {profile_data.get('wash_trading_volume', 0)}")
            print(f"  高频交易量: {profile_data.get('high_frequency_volume', 0)}")
            print(f"  套利交易量: {profile_data.get('funding_arbitrage_volume', 0)}")
            print(f"  风险事件数: {profile_data.get('risk_events_count', 0)}")
            
            # 验证关键字段是否存在
            required_fields = [
                'abnormal_volume', 'abnormal_ratio', 'wash_trading_volume',
                'high_frequency_volume', 'funding_arbitrage_volume', 'risk_events_count'
            ]
            
            missing_fields = [field for field in required_fields if field not in profile_data]
            
            if missing_fields:
                print(f"\n❌ 缺失字段: {missing_fields}")
                return False
            else:
                print("\n✅ 所有异常分析字段都正确映射")
                return True
        else:
            print("❌ 画像生成失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的用户画像生成逻辑...")
    print("=" * 60)
    
    # 测试1: 异常分析计算
    test1_passed = test_abnormal_analysis_calculation()
    
    # 测试2: 数据映射
    test2_passed = test_profile_data_mapping()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"  异常分析计算: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  数据映射逻辑: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！用户画像生成逻辑修复成功！")
        print("💡 下次生成用户画像时，异常分析数据将正确计算和保存")
        return True
    else:
        print("\n🚨 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
