const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const webpack = require('webpack'); // Required for ProvidePlugin

const isProduction = process.env.NODE_ENV === 'production';

// 设置不同环境的输出路径
const outputPath = isProduction 
    ? path.resolve(__dirname, '../backend/agent_analyzer', 'static', 'dist')
    : path.resolve(__dirname, '../dist');

module.exports = {
    mode: isProduction ? 'production' : 'development',
    context: path.resolve(__dirname, '../frontend'),
    entry: {
        main: './src/shared/services/main.js',
        contractAnalysisPage: './src/modules/contract-risk-analysis/pages/main.js',
        agentRelationshipPage: './src/modules/agent-relationship/pages/main.js',
        contractIntegrationPage: './src/modules/link-risk-analysis/pages/contract-integration-main.js',
        userAnalysisPage: './src/modules/user-analysis/pages/main-refactored.js',
        loginPage: './src/auth/login.js'  // 添加登录页面入口
    },
    output: {
        filename: 'js/[name].[contenthash].js',
        path: outputPath,
        publicPath: isProduction ? '/static/dist/' : '/',
        clean: true,
    },
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: ['@babel/preset-env']
                    }
                }
            },
            {
                test: /\.css$/,
                use: [
                    isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
                    'css-loader'
                ]
            },
            {
                test: /\.(png|svg|jpg|jpeg|gif)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/images/[hash][ext][query]'
                }
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'assets/fonts/[hash][ext][query]'
                }
            }
        ]
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: './src/modules/agent-relationship/pages/index.html',
            filename: 'index.html', 
            chunks: ['main', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/contract-risk-analysis/pages/contract-analysis.html',
            filename: 'contract_analysis.html', 
            chunks: ['contractAnalysisPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/agent-relationship/pages/index.html',
            filename: 'agent_relationship.html', 
            chunks: ['agentRelationshipPage', 'vendors']
        }),

        new HtmlWebpackPlugin({
            template: './src/modules/link-risk-analysis/pages/contract-integration.html',
            filename: 'contract_integration.html', 
            chunks: ['contractIntegrationPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/modules/user-analysis/pages/user-analysis.html',
            filename: 'user_analysis.html', 
            chunks: ['userAnalysisPage', 'vendors']
        }),
        new HtmlWebpackPlugin({
            template: './src/auth/login.html',
            filename: 'login.html', 
            chunks: ['loginPage']  // 只使用loginPage，不需要vendors
        }),
        new MiniCssExtractPlugin({
            filename: 'css/[name].[contenthash].css',
        }),
        new webpack.ProvidePlugin({
            $: 'jquery',
            jQuery: 'jquery',
            Popper: ['popper.js', 'default'],
            bootstrap: 'bootstrap'
        }),
    ],
    optimization: {
        minimize: isProduction,
        minimizer: [
            new TerserPlugin(),
        ],
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/](bootstrap|echarts|chart\.js|@babel|jquery|popper\.js|bootstrap-icons)[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
            }
        }
    },
    devServer: {
        static: {
            directory: path.resolve(__dirname, '../dist'),
        },
        historyApiFallback: {
            rewrites: [
                { from: /\/login/, to: '/login.html' },
                { from: /\/contract_analysis_page/, to: '/contract_analysis.html' },
                { from: /\/contract-integration/, to: '/contract_integration.html' },
                { from: /\/agent_relationship/, to: '/agent_relationship.html' },
                { from: /\/user_analysis/, to: '/user_analysis.html' },
                { from: /./, to: '/index.html' }
            ]
        },
        compress: true,
        port: 3000,
        hot: true,
        open: false,
        devMiddleware: {
            writeToDisk: true,
        },
        proxy: [
            {
                context: ['/api'],
                target: 'http://localhost:5005',
                changeOrigin: true,
                pathRewrite: { '^/api': '/api' },
                secure: false,
                logLevel: 'debug',
                cookieDomainRewrite: 'localhost',
                headers: {
                    'Connection': 'keep-alive'
                }
            }
        ]
    }
}; 