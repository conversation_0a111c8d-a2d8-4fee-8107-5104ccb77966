#!/bin/bash

# 确保脚本从项目根目录运行
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 停止之前可能运行的进程
echo "停止现有进程..."
pkill -f "python3 app.py" || true
pkill -f "webpack serve" || true
lsof -ti:5005 | xargs kill -9 || true
lsof -ti:3000 | xargs kill -9 || true

# 启动后端服务
echo "启动后端服务..."
cd backend
python3 app.py &
BACKEND_PID=$!

# 等待后端启动
echo "等待后端服务启动..."
sleep 3

# 启动前端webpack开发服务器
echo "启动前端webpack开发服务器..."
cd ../
npm run dev &
FRONTEND_PID=$!

echo "======================================"
echo "服务启动完成！"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:5005"
echo "======================================"
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
wait

# 清理函数
cleanup() {
    echo "正在停止所有服务..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    pkill -f "python3 app.py" || true
    pkill -f "webpack serve" || true
    echo "所有服务已停止"
}

# 设置陷阱，确保在脚本退出时清理进程
trap cleanup EXIT INT TERM 