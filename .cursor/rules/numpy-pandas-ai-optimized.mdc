---
description: 
globs: 
alwaysApply: true
---
## 1. NumPy 与 Pandas 核心模式与反模式

### 1.1. NumPy 关键方法回顾 (在Pandas中同样重要)

*   **向量化 (Vectorization)**: NumPy 的核心优势。尽可能使用 NumPy 的内置函数对整个数组进行操作，而不是使用 Python 的 `for` 循环。
*   **广播 (Broadcasting)**: 理解 NumPy 如何在形状不同的数组之间执行算术运算。
*   **通用函数 (Ufuncs)**: 如 `np.sin()`, `np.exp()`, `np.add()` 等，它们是高度优化的向量化函数。
*   **掩码 (Masking)**: 使用布尔数组来选择或修改数组中的特定元素。

### 1.2. Pandas 高效使用方法

*   **链式操作**: Pandas 支持链式调用方法，可以使代码更易读，例如 `df.dropna().reset_index(drop=True)`。
*   **常用函数 (重点回顾)**:
    *   数据加载: `pd.read_csv()`, `pd.read_excel()`, `pd.read_sql()`。
    *   数据清洗: `df.dropna()`, `df.fillna()`, `df.replace()`, `df.duplicated()`, `df.drop_duplicates()`。
    *   数据选择/索引: 强烈推荐 `.loc[]` (基于标签) 和 `.iloc[]` (基于整数位置)。
    *   数据转换: `df.apply()`, `df.map()`, `series.astype()`。
    *   分组与聚合: `df.groupby()`, `df.agg()`, `df.pivot_table()`。
    *   合并与连接: `pd.concat()`, `pd.merge()`。

### 1.3. Pandas 中尤其需要避免的反模式

*   **避免循环**: 尽量避免使用 Python 的 `for` 循环遍历 Pandas DataFrame/Series 的行。优先考虑向量化操作。
    *   尤其要避免 `df.iterrows()` 和 `df.itertuples()` 进行修改操作或复杂计算，它们通常很慢。
*   **不必要的副本与原地修改**:
    *   理解视图 (View) vs. 副本 (Copy)。Pandas 在这里行为更复杂，有时会返回视图有时返回副本。
    *   使用 `.copy()` 明确创建副本，避免意外修改原始数据。
    *   谨慎使用 `inplace=True`，它会直接修改原对象，有时会导致困惑，且并非总能提升性能。
*   **链式索引赋值**: 避免 `df['col1']['row_label'] = value` 这种形式，可能因返回副本而赋值失败。使用 `.loc` 或 `.iloc` 进行赋值：`df.loc['row_label', 'col1'] = value`。
*   **硬编码形状/大小**: (通用NumPy/Pandas) 尽量动态获取数组/DataFrame形状 (`.shape`)。

### 1.4. 数据类型的重要性 (NumPy 与 Pandas)

*   **明确数据类型**: NumPy 数组和 Pandas Series 都有数据类型 (`dtype`)。确保数据类型正确，可以节省内存并提高运算速度。
*   **Pandas 类型转换**: 使用 `series.astype()` 转换类型，例如将对象类型 (object) 的数字列转换为 `int` 或 `float`。对于类别较少的文本数据，可考虑转换为 `category` 类型以节省内存。

## 2. Pandas 与 NumPy 性能提升初步

对于新手，关注以下几点通常能带来显著的性能提升：

*   **向量化**: 这是 NumPy 和 Pandas 最重要的性能提升手段。
*   **选择合适的数据类型**:
    *   例如，如果整数值范围不大，使用 `np.int32` 而不是默认的 `np.int64`。
    *   Pandas 中，对于只有少数几个唯一值的列，使用 `category` 类型。
*   **内存管理意识**:
    *   避免创建不必要的中间变量或大型数组副本。
    *   处理完不再需要的大型数据结构后，可以使用 `del variable_name` 并配合 `gc.collect()` (来自 `import gc`) 尝试释放内存。
*   **(可选) 进阶工具简介**: 当你遇到性能瓶颈时，可以进一步了解 Numba (JIT 编译器，能加速 Python 和 NumPy 代码) 或 Dask (用于处理大于内存数据集的并行计算库)。

## 3. Pandas 数据处理安全基础

*   **处理外部数据**:
    *   **输入验证**: 当从外部文件 (CSV, Excel等) 或数据库加载数据时，检查数据类型、范围、格式是否符合预期。
    *   Pandas 读取 CSV 时，若包含恶意公式且用户在 Excel 中打开，可能存在风险。通常数据分析场景下直接用 Pandas 处理问题不大。
*   **警惕 `eval()` 和不安全的序列化**:
    *   避免对不可信的字符串输入使用 `eval()` 或 Pandas 的 `pd.eval()` (除非你完全理解其安全性)。
    *   加载 pickle 文件 (`pd.read_pickle()`) 时要确保文件来源可靠，因为 pickle 可以执行任意代码。

## 4. Pandas 与 NumPy 测试入门

*   **为什么需要测试**: 确保代码按预期工作，修改代码时能快速发现问题。
*   **简单的单元测试**:
    *   推荐使用 `pytest` 或 Python 内置的 `unittest`。
    *   为你的函数编写测试用例：给定已知输入，断言输出是否符合预期。
    *   例如，测试一个数据清洗函数，输入一个包含特定问题的 DataFrame，检查输出是否已正确处理。
*   **测试浮点数**: 由于浮点数精度问题，比较浮点数时不要用 `==`。NumPy 提供了 `np.testing.assert_allclose(actual, desired)`，它能在一定容差内比较数组。

## 5. Pandas 与 NumPy 常见陷阱与注意事项

*   **索引**:
    *   NumPy 和 Pandas 都是 0-based 索引。
    *   Pandas 中 `.loc` (基于标签) 和 `.iloc` (基于整数位置) 的区别要搞清楚。
*   **视图 (View) vs. 副本 (Copy)**:
    *   NumPy 切片常返回视图。修改视图会影响原数组。`arr_slice = arr[:]`, `arr_slice_copy = arr[:].copy()`。
    *   Pandas 中，一个操作返回视图还是副本有时不那么直观。当你需要确保不修改原 DataFrame/Series 时，显式使用 `.copy()`。
*   **处理缺失值 (NaN)**:
    *   NumPy 中的 `np.nan`。算术运算涉及 `np.nan` 通常结果也是 `np.nan`。
    *   Pandas 提供了 `isnull()`, `notnull()`, `fillna()`, `dropna()` 等方法处理缺失值。
*   **数据类型不匹配**: 运算时确保数据类型兼容，否则可能出错或得到意外结果。
*   **广播规则 (NumPy)**: 如果不熟悉，可能会导致意外的数组形状或计算错误。
